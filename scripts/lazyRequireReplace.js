const path = require('path');
const replace = require('./addIndexModule');
const { recursion } = require('./utils');

const indexRoots = [
  '../build/src/ComponentBusiness/Tips/index.js',
  '../build/src/ComponentBusiness/HomeAD/index.js',
  '../build/src/ComponentBusiness/SearchPanel/index.js',
  '../build/src/ComponentBusiness/Common/index.js',
  '../build/src/Constants/PageId/Index.js',
  '../build/src/Wrap/Index.js',
  '../build/src/State/Index.js',
  '../build/src/Pages/Home/Components/Index.js',
  '../build/src/Pages/Home/UtilsNew/Index.js',
  '../build/src/ComponentBusiness/OrderConfirmModal/Index.js',
  '../build/src/ComponentBusiness/Index.js',
  '../build/src/Constants/TextIndex.js',
  '../build/src/Components/Index.js',
  '../build/src/Pages/Home/Logic/Index.js',
  '../build/src/Types/Index.js',
  '../build/src/ComponentBusiness/CouponEntry/index.js',
];

const lazyRequireReplace = (file, match) => {
  if (
    file.includes('/index.') ||
    file.includes('/Index.') ||
    file.includes('/TextIndex.') ||
    file.includes('/ListIndex.') ||
    file.includes('/VendorListIndex.') ||
    file.includes('/SelectorIndex.')
  ) {
    let success = true;
    try {
      success = replace({
        indexPath: file,
        outPath: file,
      });
    } catch (e) {
      console.log(`lazyRequireReplace file error: ${file}`, e);
      success = false;
    }
    if (success) {
      console.log(`lazyRequireReplace file: ${file}`);
    }
  }
};

indexRoots.map(root => {
  lazyRequireReplace(path.resolve(__dirname, root));
  // recursion(path.resolve(__dirname, root), (file) => lazyRequireReplace(file, '/index.'));
});

// recursion(path.resolve(__dirname, '../build'), (file) => lazyRequireReplace(file, '/Index.'));
