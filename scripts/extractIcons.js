const fs = require('fs');
const path = require('path');
const glob = require('glob');

// 读取icon.ts文件并解析常量
const iconFilePath = path.join(__dirname, '../src/Common/src/Tokens/tokens/icon.ctrip.ts');
const iconFileContent = fs.readFileSync(iconFilePath, 'utf-8');

// 使用正则表达式解析出所有的常量
const iconConstants = {};
const iconRegex = /export const (\w+) = '(.*?)';/g;
let match;
while ((match = iconRegex.exec(iconFileContent)) !== null) {
  iconConstants[match[1]] = match[2];
}

// 扫描项目中的所有文件，找到使用了哪些icon.*常量
const projectFiles = glob.sync('src/**', { ignore: ['node_modules/**', 'icon.ts'] });
const usedIcons = new Set();

projectFiles.forEach((file) => {
  const filePath = path.join(__dirname, '../'+file);
  if (fs.lstatSync(filePath).isFile()) {
    const fileContent = fs.readFileSync(file, 'utf-8');
    Object.keys(iconConstants).forEach((icon) => {
      const iconUsageRegex = new RegExp(`icon\\.${icon}\\b`, 'g');
      if (iconUsageRegex.test(fileContent)) {
        usedIcons.add(icon);
      }
    });
  }
});

// 根据使用的常量生成新的iconNew.ts文件
const newIconFilePath = path.join(__dirname, '../src/Common/src/Tokens/tokens/iconNew.ts');
const newIconFileContent = Array.from(usedIcons)
  .map((icon) => `export const ${icon} = '${iconConstants[icon]}';`)
  .join('\n');

fs.writeFileSync(newIconFilePath, newIconFileContent, 'utf-8');

console.log('iconNew.ts文件已生成，包含以下常量:');
console.log(Array.from(usedIcons).join(', '));