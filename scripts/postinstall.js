/* eslint-disable */
const fs = require('fs');
const path = require('path');
const { recursion } = require('./utils');

const appid = process.env.NODE_ENV_APPID;

// debug
// const appid = '99999';
// TIPS @rn_com_car 打入node_modules需要替换成 const rootPaths = ['../build', '../node_modules/@ctrip/rn_trip_com_car/dist'];
const rootPaths = ['../build/src'];
const changePaths = [];
const NullFileComponentTmp = 'export default () => null;';


if (appid != '37') {
  rootPaths.map(root => {
    recursion(path.resolve(__dirname, root), miniByEnv);
  });
}
console.log(`replace paths: ${changePaths.join('\n')}`);

// 手动处理非打包内容
function clearConfigFiles(file) {
  // 处理Token
  // button.ly.js
  // color.ly.js
  // color.qunar.js
  // color.trip.dark.js
  // font.common.js
  const configs = [{
    path: 'Tokens/tokens',
    files: ['button.ly.js', 'color.ly.js', 'color.qunar.js'],
  }];

  configs.map(item => {
    if (file && file.indexOf(item.path) > -1 && item.files.includes(path.basename(file))) {
      miniFileComponents(path.resolve(__dirname, file));
      console.log(`[ACTION]:${item.path}, ${file}`);
    }
  });
};

/**
 * Attention:
 * 仅支持 xx.trip.js xx.ctrip.js 的优化
 */

function miniByEnv(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  const requireReg = /(require\(.+?)\.(ctrip|trip)'/mg;
  const importReg = /(import.+?)\.(ctrip|trip)'/mg;

  if (appid == '37') {
    if (filePath.match(/\.(ctrip|Ctrip|CTRIP)(.*)\.js/g)) {
      miniFileComponents(path.resolve(__dirname, filePath))
    }
  } else {
    if (filePath.match(/\.(trip|Trip|TRIP)(.*)\.js/g)) {
      miniFileComponents(path.resolve(__dirname, filePath))
    }
  }
  // 处理token
  clearConfigFiles(filePath);

  if (
    content.match(requireReg)
    || content.match(importReg)
  ) {
    changePaths.push(filePath);
    const needChannel = appid == '37' ? '.trip\'' : '.ctrip\'';
    // 根据appid对引用组件做替换，在Trip App中只引用trip.js，在Ctrip App中只引用ctrip.js
    content = content.replace(requireReg, (match, $1) => $1 + needChannel);
    content = content.replace(importReg, (match, $1) => $1 + needChannel);
    fs.writeFileSync(filePath, content, 'utf8');
  }
}

/**
 * 将组件页面置空
 */
function miniFileComponents(filePath) {
  fs.writeFileSync(filePath, NullFileComponentTmp, 'utf8');
  console.log(`mini File Components paths: ${filePath}`);
}
