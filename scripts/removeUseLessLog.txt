
开始无用导出代码检测，目录：src/State  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Area/Actions.ts 
检查文件存在以下导出未使用 
export default setPickType; 

开始删除未使用导出代码 

替换前 export default setPickType; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Area/FunctionTypes.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Area/Reducer.ts 

检查文件在被引用文件中进行了 require('./Area/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Area/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Area/Types.ts 
检查文件存在以下导出未使用 
export default SET_PICKTYPE; 

开始删除未使用导出代码 

替换前 export default SET_PICKTYPE; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Actions.ts 
检查文件存在以下导出未使用 
ListActionType 

开始删除未使用导出代码 

替换前 export interface ListActionType extends ActionType {
  data?: Object;
} 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Logic.ts 

检查文件在被引用文件中进行了 require('./Common/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Mapper.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Reducer.ts 

检查文件在被引用文件中进行了 require('./Common/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Requests.ts 
检查文件存在以下导出未使用 
export default getWarningListParams; 

开始删除未使用导出代码 

替换前 export default getWarningListParams; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Common/Types.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/CountryInfo/Reducers.ts 

检查文件在被引用文件中进行了 require('./CountryInfo/Reducers') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/CountryInfo/Selectors.ts 
检查文件存在以下导出未使用 
export default getCountryId; 

开始删除未使用导出代码 

替换前 export default getCountryId; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Actions.ts 
检查文件存在以下导出未使用 
setCouponPreValidationModalVisible 

开始删除未使用导出代码 

替换前 export const setCouponPreValidationModalVisible = (
  visible: boolean,
  content?: ResultInfoType,
) => ({
  type: SET_COUPON_PREVALIDATION_MODAL_VISIBLE,
  data: { visible, content },
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Logic.ts 

检查文件在被引用文件中进行了 require('./Coupon/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Reducer.ts 

检查文件在被引用文件中进行了 require('./Coupon/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Requests.ts 
检查文件存在以下导出未使用 
export default getApiFetchReceivePromotionParams; 

开始删除未使用导出代码 

替换前 export default getApiFetchReceivePromotionParams; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Actions.ts 
替换前 import {
  FETCH_RECEIVE_PROMOTION,
  FETCH_RECEIVE_PROMOTION_CALLBACK,
  FETCH_RECEIVE_PROMOTION_CALLBACK_TASK,
  RECEIVE_PROMOTION,
  RECEIVE_PROMOTION_CALLBACK,
  SET_ANCHOR_PROMOTION_ID,
  COUPON_RESET,
  SET_IS_LIST_RECEIVE_SUCCESS,
  SET_COUPON_PREVALIDATION_MODAL_VISIBLE,
  RECEIVE_ALL_PROMOTION,
  RECEIVE_ALL_PROMOTION_CALLBACK,
} from './Types'; 
替换后 import { FETCH_RECEIVE_PROMOTION, FETCH_RECEIVE_PROMOTION_CALLBACK, FETCH_RECEIVE_PROMOTION_CALLBACK_TASK, RECEIVE_PROMOTION, RECEIVE_PROMOTION_CALLBACK, SET_ANCHOR_PROMOTION_ID, COUPON_RESET, SET_IS_LIST_RECEIVE_SUCCESS, RECEIVE_ALL_PROMOTION, RECEIVE_ALL_PROMOTION_CALLBACK } from './Types'; 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Debug/Actions.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Debug/Logic.ts 

检查文件在被引用文件中进行了 require('./Debug/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Debug/Reducers.ts 

检查文件在被引用文件中进行了 require('./Debug/Reducers') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Debug/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Debug/Types.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/DriverAgeAndNumber/Actions.ts 
检查文件存在以下导出未使用 
AgeActionType 

开始删除未使用导出代码 

替换前 export interface AgeActionType extends ActionType {
  data?: Object;
} 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/DriverAgeAndNumber/Reducers.ts 

检查文件在被引用文件中进行了 require('./DriverAgeAndNumber/Reducers') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/DriverAgeAndNumber/Selectors.ts 
检查文件存在以下导出未使用 
getPopulation 
getDriverAgeAndNumber 

开始删除未使用导出代码 

替换前 export const getPopulation = state =>
  getAdultNumbers(state) + getChildNumbers(state); 
替换后  

替换前 export const getDriverAgeAndNumber = state => state.DriverAgeAndNumber; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/DriverAgeAndNumber/Types.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Enhancer.ts 
检查文件存在以下导出未使用 
safe 
export default getEnhancer; 

开始删除未使用导出代码 

替换前 export function safe(sagaFn) {
  return function* (action) {
    try {
      return yield call(sagaFn, action);
    } catch (error) {
      const isTimeOut = Utils.getIsTimeOut(null, error);
      if (!isTimeOut) {
        // @ts-ignore
        Log.logPromiseError(error);
      }
      CarLog.LogError(ErrorKey.e_get_enhancer, {
        error,
        extraData: `${action?.type}  ${error?.stack}`,
        expMsg: Utils.getErrorMessage(error),
        expCode: isTimeOut
          ? ApiResCode.TraceCode.E1004
          : ApiResCode.TraceCode.E1003,
      });
      return null;
    }
  };
} 
替换后 function safe(sagaFn) {
  return function* (action) {
    try {
      return yield call(sagaFn, action);
    } catch (error) {
      const isTimeOut = Utils.getIsTimeOut(null, error);
      if (!isTimeOut) {
        // @ts-ignore
        Log.logPromiseError(error);
      }
      CarLog.LogError(ErrorKey.e_get_enhancer, {
        error,
        extraData: `${action?.type}  ${error?.stack}`,
        expMsg: Utils.getErrorMessage(error),
        expCode: isTimeOut
          ? ApiResCode.TraceCode.E1004
          : ApiResCode.TraceCode.E1003,
      });
      return null;
    }
  };
} 

替换前 export default getEnhancer; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Actions.ts 
检查文件存在以下导出未使用 
fetchDistrictCityMapping 
fetchDistrictCityMappingCallback 
setDatePickerIsShow 

开始删除未使用导出代码 

替换前 export const fetchDistrictCityMapping = () => ({
  type: FETCH_DISTRICTCITYMAPPING,
}); 
替换后  

替换前 export const fetchDistrictCityMappingCallback = data => ({
  type: FETCH_DISTRICTCITYMAPPING_CALLBACK,
  data,
}); 
替换后  

替换前 export const setDatePickerIsShow = data => ({
  type: SET_DATEPICKER_VISIBLE,
  data,
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/FunctionTypes.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Logic.ts 

检查文件在被引用文件中进行了 require('./Home/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Mapper.ts 
检查文件存在以下导出未使用 
getDailyText 

开始删除未使用导出代码 

替换前 export const getDailyText = createSelector([getTenancyDays], isdRentalDays => {
  return isdRentalDays > 0 ? '日均' : '';
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Reducer.ts 

检查文件在被引用文件中进行了 require('./Home/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Selectors.ts 
检查文件存在以下导出未使用 
getIsLoadingSuperEnterInfo 
getPrivileges 
getHomeFilterItems 
getUserLevelRightsInfo 

开始删除未使用导出代码 

替换前 export const getIsLoadingSuperEnterInfo = state =>
  state.Home.isLoadingSuperEnterInfo; 
替换后  

替换前 export const getPrivileges = state => state.Home.privileges; 
替换后 const getPrivileges = state => state.Home.privileges; 

替换前 export const getHomeFilterItems = state => state.Home?.filterItems; 
替换后 const getHomeFilterItems = state => state.Home?.filterItems; 

替换前 export const getUserLevelRightsInfo = state => state.Home.userLevelRights; 
替换后 const getUserLevelRightsInfo = state => state.Home.userLevelRights; 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Actions.ts 
替换前 import {
  FETCH_DISTRICTCITYMAPPING,
  FETCH_DISTRICTCITYMAPPING_CALLBACK,
  SET_HOME_LOCATIONDATEPOP_VISIBLE,
  SET_DATEPICKER_VISIBLE,
  FETCH_USERPRIVILEGELIST,
  FETCH_USERPRIVILEGELIST_CALLBACK,
  SET_PRIVILEGE_VISIBLE,
  SET_FILTER_ITEMS,
  FETCH_ITINERARYCARDINFO,
  FETCH_ITINERARYCARDINFO_CALLBACK,
  UPDATE_SELECTED_ITINERARYCARD,
  GET_LOTTIE_JSON,
  GET_LOTTIE_JSON_CALLBACK,
  FETCH_MEMBERSHIPRIGHTS,
  FETCH_MEMBERSHIPRIGHTS_CALLBACK,
  FETCH_MEMBERSHIPRIGHTS_CALLBACK_TASK,
  GET_MARKET_THEME,
  GET_MARKET_THEME_CALLBACK,
  FETCH_SECOND_SCREEN_DATA,
  QUERY_ORDERINSUANDXPRODUCT,
  SET_ORDER_CARD_CAN_UPGRADE_INS,
  QUERY_TRIP_RECOMMEND,
  QUERY_TRIP_RECOMMEND_CALLBACK,
  SET_BUSINESS_LICENSE_VISIBLE,
  QUERY_VEHICLE_STATUS,
  QUERY_VEHICLE_STATUS_CALLBACK,
  SELF_SERVICE_OPERATION,
  SET_SHOW_BANNERS,
  GET_FOLD_SCREEN_LOTTIE_JSON,
  GET_FOLD_SCREEN_LOTTIE_JSON_CALLBACK,
} from './Types'; 
替换后 import { SET_HOME_LOCATIONDATEPOP_VISIBLE, FETCH_USERPRIVILEGELIST, FETCH_USERPRIVILEGELIST_CALLBACK, SET_PRIVILEGE_VISIBLE, SET_FILTER_ITEMS, FETCH_ITINERARYCARDINFO, FETCH_ITINERARYCARDINFO_CALLBACK, UPDATE_SELECTED_ITINERARYCARD, GET_LOTTIE_JSON, GET_LOTTIE_JSON_CALLBACK, FETCH_MEMBERSHIPRIGHTS, FETCH_MEMBERSHIPRIGHTS_CALLBACK, FETCH_MEMBERSHIPRIGHTS_CALLBACK_TASK, GET_MARKET_THEME, GET_MARKET_THEME_CALLBACK, FETCH_SECOND_SCREEN_DATA, QUERY_ORDERINSUANDXPRODUCT, SET_ORDER_CARD_CAN_UPGRADE_INS, QUERY_TRIP_RECOMMEND, QUERY_TRIP_RECOMMEND_CALLBACK, SET_BUSINESS_LICENSE_VISIBLE, QUERY_VEHICLE_STATUS, QUERY_VEHICLE_STATUS_CALLBACK, SELF_SERVICE_OPERATION, SET_SHOW_BANNERS, GET_FOLD_SCREEN_LOTTIE_JSON, GET_FOLD_SCREEN_LOTTIE_JSON_CALLBACK } from './Types'; 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Actions.ts 
检查文件存在以下导出未使用 
setAgeTipPopIsShow 

开始删除未使用导出代码 

替换前 export const setAgeTipPopIsShow = data => ({
  type: SET_AGETIPPOP_VISIBLE,
  data,
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Logic.ts 

检查文件在被引用文件中进行了 require('./List/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Mappers.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Reducer.ts 

检查文件在被引用文件中进行了 require('./List/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/List/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/List/Actions.ts 
替换前 import {
  SET_AGETIPPOP_VISIBLE,
  FETCH_LIMIT_CONTENT,
  SET_LIMITTIP_LOADFINISH,
  PRE_FETCH_LIST_BATCH,
  PRE_FETCH_LIST_PAGE,
} from './Types'; 
替换后 import { FETCH_LIMIT_CONTENT, SET_LIMITTIP_LOADFINISH, PRE_FETCH_LIST_BATCH, PRE_FETCH_LIST_PAGE } from './Types'; 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Actions.ts 
检查文件存在以下导出未使用 
LocationAndDateActionType 

开始删除未使用导出代码 

替换前 export interface LocationAndDateActionType extends ActionType {
  data?: Object;
} 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Logic.ts 

检查文件在被引用文件中进行了 require('./LocationAndDate/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/LogicSaga.ts 

检查文件在被引用文件中进行了 require('./LocationAndDate/LogicSaga') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Mappers.ts 
检查文件存在以下导出未使用 
placeHolder 
getIsdInitTime 
getIsdInitPTime 
getOsdInitPTime 
getIsdInitRTime 
getOsdInitRTime 
parseStorageResult 
getLocationAndDateStorage 
getHomeCombineLocationStorageSync 
clearLocationAndDateStorage 
getInitDate 
getLocationAndDataInfo 
getInitLocationAndDateSync 
getInitLocationSync 
validLocation 
defaultRecommendDate 

开始删除未使用导出代码 

替换前 export const placeHolder = null; 
替换后  

替换前 export const getIsdInitTime = (t: dayjs.Dayjs) => {
  const now = dayjs();
  let time = t;
  if (now.hour() >= 0 && now.hour() < 8) {
    time = time.startOf('hour').hour(10);
  } else if (now.hour() >= 8 && now.hour() < 16) {
    if (now.minute() > 30) {
      time = time.add(5, 'h').minute(0).second(0);
    } else {
      time = time.add(4, 'h').minute(30).second(0);
    }
  } else {
    time = time.add(1, 'd').startOf('hour').hour(10);
  }

  return time;
}; 
替换后 const getIsdInitTime = (t: dayjs.Dayjs) => {
  const now = dayjs();
  let time = t;
  if (now.hour() >= 0 && now.hour() < 8) {
    time = time.startOf('hour').hour(10);
  } else if (now.hour() >= 8 && now.hour() < 16) {
    if (now.minute() > 30) {
      time = time.add(5, 'h').minute(0).second(0);
    } else {
      time = time.add(4, 'h').minute(30).second(0);
    }
  } else {
    time = time.add(1, 'd').startOf('hour').hour(10);
  }

  return time;
}; 

替换前 export const getIsdInitPTime = () => getIsdInitTime(dayjs()); 
替换后 const getIsdInitPTime = () => getIsdInitTime(dayjs()); 

替换前 export const getOsdInitPTime = () =>
  dayjs().add(7, 'd').startOf('hour').hour(10); 
替换后 const getOsdInitPTime = () =>
  dayjs().add(7, 'd').startOf('hour').hour(10); 

替换前 export const getIsdInitRTime = () =>
  getIsdInitPTime().add(Platform.RENTAL_GAP.ISD, 'd'); 
替换后 const getIsdInitRTime = () =>
  getIsdInitPTime().add(Platform.RENTAL_GAP.ISD, 'd'); 

替换前 export const getOsdInitRTime = () =>
  getOsdInitPTime().add(Platform.RENTAL_GAP.OSD, 'd'); 
替换后  

替换前 export const parseStorageResult = storage => {
  if (storage) {
    try {
      return JSON.parse(storage);
    } catch (e) {
      /* eslint-disable no-console */
      console.warn('getCommentsList storage fail');
    }
  }
  return {};
}; 
替换后 const parseStorageResult = storage => {
  if (storage) {
    try {
      return JSON.parse(storage);
    } catch (e) {
      /* eslint-disable no-console */
      console.warn('getCommentsList storage fail');
    }
  }
  return {};
}; 

替换前 export const getLocationAndDateStorage = async () => {
  const storage = await CarStorage.loadAsync(
    StorageKey.CAR_LOCATION_DATE_HISTORY,
  );
  return parseStorageResult(storage);
}; 
替换后 const getLocationAndDateStorage = async () => {
  const storage = await CarStorage.loadAsync(
    StorageKey.CAR_LOCATION_DATE_HISTORY,
  );
  return parseStorageResult(storage);
}; 

替换前 export const getHomeCombineLocationStorageSync = () => {
  const storage = CarStorage.privateLoadSync(
    StorageKey.CAR_HOME_COMBINE_TAB_LOCATION,
  );

  return parseStorageResult(storage);
}; 
替换后 const getHomeCombineLocationStorageSync = () => {
  const storage = CarStorage.privateLoadSync(
    StorageKey.CAR_HOME_COMBINE_TAB_LOCATION,
  );

  return parseStorageResult(storage);
}; 

替换前 export const clearLocationAndDateStorage = () => {
  CarStorage.save(StorageKey.CAR_LOCATION_DATE_HISTORY, JSON.stringify({}));
}; 
替换后 const clearLocationAndDateStorage = () => {
  CarStorage.save(StorageKey.CAR_LOCATION_DATE_HISTORY, JSON.stringify({}));
}; 

替换前 export const getInitDate = rentalDate => {
  const pickUpTime = _.get(rentalDate, 'pickup');
  const dropOffTime = _.get(rentalDate, 'dropoff');
  if (isValidRentalDate(pickUpTime, dropOffTime)) {
    return {
      pickUp: { dateTime: dayjs(rentalDate.pickup) },
      dropOff: { dateTime: dayjs(rentalDate.dropoff) },
      productDropOff: { dateTime: dayjs(rentalDate.dropoff) },
    };
  }
  return {
    pickUp: { dateTime: getIsdInitPTime() },
    dropOff: { dateTime: getIsdInitRTime() },
    productDropOff: { dateTime: getIsdInitRTime() },
  };
}; 
替换后 const getInitDate = rentalDate => {
  const pickUpTime = _.get(rentalDate, 'pickup');
  const dropOffTime = _.get(rentalDate, 'dropoff');
  if (isValidRentalDate(pickUpTime, dropOffTime)) {
    return {
      pickUp: { dateTime: dayjs(rentalDate.pickup) },
      dropOff: { dateTime: dayjs(rentalDate.dropoff) },
      productDropOff: { dateTime: dayjs(rentalDate.dropoff) },
    };
  }
  return {
    pickUp: { dateTime: getIsdInitPTime() },
    dropOff: { dateTime: getIsdInitRTime() },
    productDropOff: { dateTime: getIsdInitRTime() },
  };
}; 

替换前 export const getLocationAndDataInfo = data => {
  const { storageTime, ISD, OSD, isIsdHistoryLocation } = data;

  const locationAndDate = {
    ISD: {
      rentalLocation: null,
      rentalDate: null,
    },
    OSD: {
      rentalLocation: null,
      rentalDate: null,
    },
    isIsdHistoryLocation,
  };

  const isdStorageValid =
    _.get(data, 'ISD.rentalLocation.pickUp.isDomestic') === true &&
    _.get(data, 'ISD.rentalLocation.dropOff.isDomestic') === true &&
    storageTime;

  const osdStoragevalid =
    _.get(data, 'OSD.rentalLocation.pickUp.isDomestic') === false &&
    _.get(data, 'OSD.rentalLocation.dropOff.isDomestic') === false &&
    storageTime;

  if (!isdStorageValid && !osdStoragevalid) {
    clearLocationAndDateStorage();
  } else {
    if (isdStorageValid) {
      locationAndDate.ISD.rentalLocation = ISD.rentalLocation;
      locationAndDate.ISD.rentalDate = getInitDate(ISD.rentalDate);
    }
    if (osdStoragevalid) {
      locationAndDate.OSD.rentalLocation = OSD.rentalLocation;
      locationAndDate.OSD.rentalDate = getInitDate(OSD.rentalDate);
    }
  }

  return locationAndDate;
}; 
替换后 const getLocationAndDataInfo = data => {
  const { storageTime, ISD, OSD, isIsdHistoryLocation } = data;

  const locationAndDate = {
    ISD: {
      rentalLocation: null,
      rentalDate: null,
    },
    OSD: {
      rentalLocation: null,
      rentalDate: null,
    },
    isIsdHistoryLocation,
  };

  const isdStorageValid =
    _.get(data, 'ISD.rentalLocation.pickUp.isDomestic') === true &&
    _.get(data, 'ISD.rentalLocation.dropOff.isDomestic') === true &&
    storageTime;

  const osdStoragevalid =
    _.get(data, 'OSD.rentalLocation.pickUp.isDomestic') === false &&
    _.get(data, 'OSD.rentalLocation.dropOff.isDomestic') === false &&
    storageTime;

  if (!isdStorageValid && !osdStoragevalid) {
    clearLocationAndDateStorage();
  } else {
    if (isdStorageValid) {
      locationAndDate.ISD.rentalLocation = ISD.rentalLocation;
      locationAndDate.ISD.rentalDate = getInitDate(ISD.rentalDate);
    }
    if (osdStoragevalid) {
      locationAndDate.OSD.rentalLocation = OSD.rentalLocation;
      locationAndDate.OSD.rentalDate = getInitDate(OSD.rentalDate);
    }
  }

  return locationAndDate;
}; 

替换前 export const getInitLocationAndDateSync = () => {
  const data = getLocationAndDateStorageSync();

  return getLocationAndDataInfo(data);
}; 
替换后 const getInitLocationAndDateSync = () => {
  const data = getLocationAndDateStorageSync();

  return getLocationAndDataInfo(data);
}; 

替换前 export const getInitLocationSync = () => {
  const homeCombineData = getHomeCombineLocationStorageSync();
  if (Utils.isNewHomeTab() && homeCombineData) {
    AppContext.setHasUseHomeTabLocationData(true);
    return homeCombineData;
  }
  return getInitLocationAndDateSync();
}; 
替换后 const getInitLocationSync = () => {
  const homeCombineData = getHomeCombineLocationStorageSync();
  if (Utils.isNewHomeTab() && homeCombineData) {
    AppContext.setHasUseHomeTabLocationData(true);
    return homeCombineData;
  }
  return getInitLocationAndDateSync();
}; 

替换前 export const validLocation = (key: string, locationAndData: any) => {
  const rentalLocation = locationAndData?.[key]?.rentalLocation;
  const pickUp = rentalLocation?.pickUp;
  const dropOff = rentalLocation?.dropOff;

  if (key === 'ISD') {
    return pickUp?.isDomestic === true && dropOff?.isDomestic === true;
  }

  if (key === 'OSD') {
    return pickUp?.isDomestic === false && dropOff?.isDomestic === false;
  }

  return false;
}; 
替换后 const validLocation = (key: string, locationAndData: any) => {
  const rentalLocation = locationAndData?.[key]?.rentalLocation;
  const pickUp = rentalLocation?.pickUp;
  const dropOff = rentalLocation?.dropOff;

  if (key === 'ISD') {
    return pickUp?.isDomestic === true && dropOff?.isDomestic === true;
  }

  if (key === 'OSD') {
    return pickUp?.isDomestic === false && dropOff?.isDomestic === false;
  }

  return false;
}; 

替换前 export const defaultRecommendDate = () => {
  const dropOffTime = getInitRTime();
  return {
    pickUp: { dateTime: getInitPTime() },
    dropOff: { dateTime: dropOffTime },
    productDropOff: { dateTime: dropOffTime }, // 如果使用getInitRTime再获取一遍时间，会造成dropOff 与 productDropOff的时间有毫秒差
  };
}; 
替换后 const defaultRecommendDate = () => {
  const dropOffTime = getInitRTime();
  return {
    pickUp: { dateTime: getInitPTime() },
    dropOff: { dateTime: dropOffTime },
    productDropOff: { dateTime: dropOffTime }, // 如果使用getInitRTime再获取一遍时间，会造成dropOff 与 productDropOff的时间有毫秒差
  };
}; 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Reducers.ts 

检查文件在被引用文件中进行了 require('./LocationAndDate/Reducers') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Requests.ts 
检查文件存在以下导出未使用 
notUndefinedOrNull 

开始删除未使用导出代码 

替换前 export { notUndefinedOrNull, getRentCenterInfoParams, verifyResponseIsValid }; 
替换后 export { getRentCenterInfoParams, verifyResponseIsValid }; 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Selectors.ts 

被引用文件进行了无用引用，进行删除 

替换前 import { getTenancyDays } from '../LocationAndDate/Selectors'; 
替换后  
检查文件存在以下导出未使用 
getFormatPickUpTime 
getFormatDropOffTime 
getProductDropOffTime 
getFilterItems 
getSelectCityId 
getPickUpCountry 
getPickUpLocationType 
getPickUpLocationCode 
getPickUpLocationName 
getPickUpLocationMeetingPointId 
getPickUpLocationMeetingSubPoiType 
getDropOffLocationMeetingPointId 
getDropOffLocationMeetingSubPoiType 
getPickUpIsFromPosition 
getPickUpLocationLat 
getPickUpLocationLng 
getDropOffCityName 
getDropOffCountry 
getDropOffLocationType 
getDropOffLocationCode 
getDropOffLocationName 
getDropOffIsFromPosition 
getDropOffLocationLat 
getDropOffLocationLng 
getIsDifferentLocation 
getPositionInfo 
getPositionLocation 
getPositionCityId 
getPositionCityName 
isDifferentLocation 
getCarHeaderData 
getNationalDayWarning 
getDiffLocationWarning 
getIMLocationAndDate 
getTenancyDays 

开始删除未使用导出代码 

替换前 export const getFormatPickUpTime = createSelector([getPickUpTime], time =>
  dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
); 
替换后  

替换前 export const getFormatDropOffTime = createSelector([getDropOffTime], time =>
  dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
); 
替换后  

替换前 export const getProductDropOffTime = state =>
  _.get(getRentalDate(state), 'productDropOff.dateTime'); 
替换后 const getProductDropOffTime = state =>
  _.get(getRentalDate(state), 'productDropOff.dateTime'); 

替换前 export const getFilterItems = state => state.LocationAndDate.filterItems; 
替换后  

替换前 export const getSelectCityId = state => state.LocationAndDate.selectCityId; 
替换后  

替换前 export const getPickUpCountry = state =>
  state.LocationAndDate.rentalLocation.pickUp.country; 
替换后  

替换前 export const getPickUpLocationType = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.type; 
替换后 const getPickUpLocationType = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.type; 

替换前 export const getPickUpLocationCode = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.id; 
替换后 const getPickUpLocationCode = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.id; 

替换前 export const getPickUpLocationName = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.name; 
替换后 const getPickUpLocationName = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.name; 

替换前 export const getPickUpLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.meetingPointId; 
替换后 const getPickUpLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.meetingPointId; 

替换前 export const getPickUpLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.subPoiType; 
替换后 const getPickUpLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.subPoiType; 

替换前 export const getDropOffLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.meetingPointId; 
替换后 const getDropOffLocationMeetingPointId = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.meetingPointId; 

替换前 export const getDropOffLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.subPoiType; 
替换后 const getDropOffLocationMeetingSubPoiType = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.subPoiType; 

替换前 export const getPickUpIsFromPosition = state =>
  state.LocationAndDate.rentalLocation.pickUp.isFromPosition; 
替换后 const getPickUpIsFromPosition = state =>
  state.LocationAndDate.rentalLocation.pickUp.isFromPosition; 

替换前 export const getPickUpLocationLat = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.lat; 
替换后 const getPickUpLocationLat = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.lat; 

替换前 export const getPickUpLocationLng = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.lng; 
替换后 const getPickUpLocationLng = state =>
  state.LocationAndDate.rentalLocation.pickUp.area.lng; 

替换前 export const getDropOffCityName = state =>
  state.LocationAndDate.rentalLocation.dropOff.cname; 
替换后 const getDropOffCityName = state =>
  state.LocationAndDate.rentalLocation.dropOff.cname; 

替换前 export const getDropOffCountry = state =>
  state.LocationAndDate.rentalLocation.dropOff.country; 
替换后  

替换前 export const getDropOffLocationType = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.type; 
替换后 const getDropOffLocationType = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.type; 

替换前 export const getDropOffLocationCode = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.id; 
替换后 const getDropOffLocationCode = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.id; 

替换前 export const getDropOffLocationName = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.name; 
替换后 const getDropOffLocationName = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.name; 

替换前 export const getDropOffIsFromPosition = state =>
  state.LocationAndDate.rentalLocation.dropOff.isFromPosition; 
替换后 const getDropOffIsFromPosition = state =>
  state.LocationAndDate.rentalLocation.dropOff.isFromPosition; 

替换前 export const getDropOffLocationLat = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.lat; 
替换后 const getDropOffLocationLat = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.lat; 

替换前 export const getDropOffLocationLng = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.lng; 
替换后 const getDropOffLocationLng = state =>
  state.LocationAndDate.rentalLocation.dropOff.area.lng; 

替换前 export const getIsDifferentLocation = state =>
  !(
    getPickUpCityId(state) === getDropOffCityId(state) &&
    getPickUpLocationName(state) === getDropOffLocationName(state)
  ); 
替换后  

替换前 export const getPositionInfo = state => state.LocationAndDate.position; 
替换后 const getPositionInfo = state => state.LocationAndDate.position; 

替换前 export const getPositionLocation = state =>
  _.get(getPositionInfo(state), 'positionLocation'); 
替换后 const getPositionLocation = state =>
  _.get(getPositionInfo(state), 'positionLocation'); 

替换前 export const getPositionCityId = state =>
  _.get(getPositionLocation(state), 'cid'); 
替换后  

替换前 export const getPositionCityName = state =>
  _.get(getPositionLocation(state), 'cname'); 
替换后  

替换前 export const isDifferentLocation = state =>
  getPickUpLocationCode(state) !== getDropOffLocationCode(state); 
替换后  

替换前 export const getCarHeaderData = createSelector(
  [
    getPickUpTime,
    getDropOffTime,
    getPickUpLocationType,
    getPickUpLocationCode,
    getPickUpLocationName,
    getDropOffLocationType,
    getDropOffLocationCode,
    getDropOffLocationName,
  ],
  (
    ptime,
    rtime,
    pLocationType,
    pLocationCode,
    pLocationName,
    rLocationType,
    rLocationCode,
    rLocationName,
  ) => ({
    ptime,
    rtime,
    pickupLocation: {
      locationType: pLocationType,
      locationCode: pLocationCode,
      locationName: pLocationName,
    },
    returnLocation: {
      locationType: rLocationType,
      locationCode: rLocationCode,
      locationName: rLocationName,
    },
  }),
); 
替换后  

替换前 export const getNationalDayWarning = createSelector(
  [getPickUpTime, getDropOffTime, getQConfig],
  (pickupTime, dropoffTime, remoteQConfig) =>
    getNationalDayWarningFunc(
      pickupTime,
      dropoffTime,
      remoteQConfig?.nationalDayWarning,
    ),
); 
替换后 const getNationalDayWarning = createSelector(
  [getPickUpTime, getDropOffTime, getQConfig],
  (pickupTime, dropoffTime, remoteQConfig) =>
    getNationalDayWarningFunc(
      pickupTime,
      dropoffTime,
      remoteQConfig?.nationalDayWarning,
    ),
); 

替换前 export const getDiffLocationWarning = state => {
  let diffLocationWarning = '';
  const pCityId = getPickUpCityId(state);
  const rCityId = getDropOffCityId(state);
  const pLocationName = getPickUpLocationName(state);
  const rLocationName = getDropOffLocationName(state);
  if (pCityId !== rCityId || pLocationName !== rLocationName) {
    diffLocationWarning = '可能产生异地还车费';
  }
  return diffLocationWarning;
}; 
替换后  

替换前 export const getIMLocationAndDate = createSelector(
  [
    getPickUpLocationName,
    getDropOffLocationName,
    getPickUpTime,
    getDropOffTime,
  ],
  (paddr, raddr, ptime, rtime) => ({
    pickupLocation: paddr,
    returnLocation: raddr,
    pickupDate: dayjs(ptime).format('YYYY-MM-DD HH:mm'),
    returnDate: dayjs(rtime).format('YYYY-MM-DD HH:mm'),
    days: BbkUtils.isd_dhm(ptime, rtime),
  }),
); 
替换后  

替换前 export const getTenancyDays = createSelector(
  [getPickUpTime, getDropOffTime],
  (pTime, rTime) => {
    const minutes = dayjs(rTime).diff(pTime, 'minutes');
    const days = minutes > dayMinutes ? 1 : 0;
    return days;
  },
); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Types.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Util.ts 
检查文件存在以下导出未使用 
BjTimeZone 
BjTimeZoneName 

开始删除未使用导出代码 

替换前 export const BjTimeZone = 8; 
替换后 const BjTimeZone = 8; 

替换前 export const BjTimeZoneName = 'GMT+8'; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSaga.ts 
检查文件存在以下导出未使用 
export default function* rootSaga() {
  yield all([...Home, ...Environment, ...LocationAndDateSaga]);
} 

开始删除未使用导出代码 

替换前 export default function* rootSaga() {
  yield all([...Home, ...Environment, ...LocationAndDateSaga]);
} 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Actions.ts 
检查文件存在以下导出未使用 
LocationAndDateActionType 
loadMarketFailed 

开始删除未使用导出代码 

替换前 export interface LocationAndDateActionType extends ActionType {
  data?: Object;
} 
替换后  

替换前 export const loadMarketFailed = (err?: any) => ({
  type: LOAD_FAILED,
  data: err,
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Helpers.ts 
检查文件存在以下导出未使用 
checkParams 

开始删除未使用导出代码 

替换前 export const checkParams = (data): boolean =>
  data && data.rentalDate && data.rentalLocation; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Logic.ts 

检查文件在被引用文件中进行了 require('./Market/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Reducers.ts 

检查文件在被引用文件中进行了 require('./Market/Reducers') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Actions.ts 
替换前 import { LOAD, LOAD_COMPLETED, LOAD_FAILED } from './Types'; 
替换后 import { LOAD, LOAD_COMPLETED } from './Types'; 

检查文件在被引用文件中进行了 * 全部引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/Market/Reducers.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Member/Actions.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Member/Logic.ts 

检查文件在被引用文件中进行了 require('./Member/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Member/Reducer.ts 

检查文件在被引用文件中进行了 require('./Member/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Member/Selectors.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Member/Types.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/OrderDetail/Mappers.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/OrderDetail/Selectors.ts 
检查文件存在以下导出未使用 
getPhoneModalFromWhere 

开始删除未使用导出代码 

替换前 export const getPhoneModalFromWhere = state =>
  state.OrderDetail.phoneModalFromWhere; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/PreCache/Logic.ts 

检查文件在被引用文件中进行了 require('./PreCache/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts 
检查文件存在以下导出未使用 
export default loadReducerRouter; 

开始删除未使用导出代码 

替换前 export default loadReducerRouter; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/Store.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Actions.ts 
检查文件存在以下导出未使用 
queryOrderDetailSupplierData 

开始删除未使用导出代码 

替换前 export const queryOrderDetailSupplierData = (data?: { vendorId?: string }) => ({
  type: QUERY_ORDERDETAIL_SUPPLIER_DATA,
  data,
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Reducer.ts 

检查文件在被引用文件中进行了 require('./SupplierData/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Selector.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Actions.ts 
替换前 import { SET_SUPPLIER_DATA, QUERY_ORDERDETAIL_SUPPLIER_DATA } from './Types'; 
替换后 import { SET_SUPPLIER_DATA } from './Types'; 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Actions.ts 
检查文件存在以下导出未使用 
initializeEnvDone 
changeEnvironment 
setCurrentEnvName 
setReduxState 

开始删除未使用导出代码 

替换前 export const initializeEnvDone = data => ({
  type: INIT_ENV_DATA_DONE,
  playload: { ...data },
}); 
替换后  

替换前 export const changeEnvironment = (data: EnvType) => ({
  type: CHANGE_ENVIRONMENT,
  playload: { ...data },
}); 
替换后  

替换前 export const setCurrentEnvName = (data: EnvType) => ({
  type: CHANGE_CURRENT_ENV,
  playload: { ...data },
}); 
替换后  

替换前 export const setReduxState = (data: any) => ({
  type: SET_REDUX_STATE,
  data: { ...data },
}); 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/DataUtils.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/EnvMeta.ts 
检查文件存在以下导出未使用 
EnvMetaType 

开始删除未使用导出代码 

替换前 export interface EnvMetaType {
  isMultiEnvironment: boolean;
} 
替换后 interface EnvMetaType {
  isMultiEnvironment: boolean;
} 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Logic.ts 

被引用文件进行了无用引用，进行删除 

替换前 import Environment from './__Environment/Logic'; 
替换后  

检查文件在被引用文件中进行了 require('./__Environment/Logic') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/LogicSagaRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Reducer.ts 

检查文件在被引用文件中进行了 require('./__Environment/Reducer') 引入，放弃无用代码检查：/Users/<USER>/workarea/rn_car_home_isd/src/State/ReducerRouter.ts  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Selectors.ts 
检查文件存在以下导出未使用 
getCurrentEnv 

开始删除未使用导出代码 

替换前 export const getCurrentEnv = state => state[ENV_REDUCER].currentEnv; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Types.ts 

被引用文件中有无用引用，进行删除路径，/Users/<USER>/workarea/rn_car_home_isd/src/State/__Environment/Actions.ts 
替换前 import {
  RESET_REDUX_STATE,
  INIT_ENV_DATA,
  INIT_ENV_DATA_DONE,
  CHANGE_ENVIRONMENT,
  CHANGE_CURRENT_ENV,
  SET_REDUX_STATE,
} from './Types'; 
替换后 import { RESET_REDUX_STATE, INIT_ENV_DATA } from './Types'; 

被引用文件进行了无用引用，进行删除 

替换前 import { ENV_REDUCER } from './Types'; 
替换后  
检查文件存在以下导出未使用 
SET_CAR_ENV 

开始删除未使用导出代码 

替换前 export const SET_CAR_ENV = '__Environment/SET_CAR_ENV'; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Global/Types.ts 
检查文件存在以下导出未使用 
PREVIOUS_STATE 
export default PREVIOUS_STATE; 

开始删除未使用导出代码 

替换前 export const PREVIOUS_STATE = '__Global/PREVIOUS_STATE'; 
替换后 const PREVIOUS_STATE = '__Global/PREVIOUS_STATE'; 

替换前 export default PREVIOUS_STATE; 
替换后  

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Middleware/ChangeEnvMiddleware.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Middleware/ResetReduxState.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

开始无用代码检测，文件路径： /Users/<USER>/workarea/rn_car_home_isd/src/State/__Middleware/UpdateLocationAndDateMiddleware.ts 

检查文件中导出的内容均有被引用，检查无用代码结束 

处理SagaReducer文件，/Users/<USER>/workarea/rn_car_home_isd/src/State/Coupon/Reducer.ts 存在未使用代码 
替换前 case SET_COUPON_PREVALIDATION_MODAL_VISIBLE:
      return {
        ...state,
        couponPreValidationModalVisible: action.data.visible,
        couponPreValidationModalProps: action.data.content,
      }; 
替换后  

处理SagaReducer文件，/Users/<USER>/workarea/rn_car_home_isd/src/State/Home/Reducer.ts 存在未使用代码 
替换前 case FETCH_DISTRICTCITYMAPPING: 
替换后  
替换前 case FETCH_DISTRICTCITYMAPPING_CALLBACK: 
替换后  
替换前 case SET_DATEPICKER_VISIBLE:
      return {
        ...state,
        datePickerVisible: action.data.visible,
      }; 
替换后  

处理SagaReducer文件，/Users/<USER>/workarea/rn_car_home_isd/src/State/LocationAndDate/Reducers.ts 存在未使用代码 
替换前 case CHANGE_ENVIRONMENT:
      return { ...initialState(), ...action.LocationAndDate }; 
替换后  

处理SagaReducer文件，/Users/<USER>/workarea/rn_car_home_isd/src/State/SupplierData/Reducer.ts 存在未使用代码 
替换前 case QUERY_ORDERDETAIL_SUPPLIER_DATA:
      return { ...state, supplierData: action.data }; 
替换后  
