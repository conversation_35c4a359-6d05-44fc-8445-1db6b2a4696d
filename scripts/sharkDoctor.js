/* eslint-disable */
// https://www.jianshu.com/p/8a8f7b0f887a
const fs = require('fs');
const path = require('path');
const sharks = require('../tmp/index');
const _ = require('lodash');
const { recursion } = require('./utils')

const sharkMap = new Map();
const reg = /getSharkValue\(\'(\S*)\'/g;
const reg2 = /<SharkValueText+(\s|[\r\n])+data=\"(\S*)\"/g;
const reg3 = /getSharkValue\(+(\s|[\r\n])+\'(\S*)\'/g;
const unusedkeyPath = path.resolve(__dirname, '../tmp/unusedkey.json');

// 检测没有使用到的shark key
function main() {
  recursion(path.resolve(__dirname, '../src'), detectSharkKey);
  recursion(path.resolve(__dirname, '../node_modules/@ctrip'), detectSharkKey);
  const source = doctor(sharks, sharkMap);
  generatorResponse(source);
  deleteUnusedKeys();
  console.log('未使用的[Shark Key] ./tmp/unusedkey.json');
}

const doctor = (source, usedKeys) => {
  let allsource = {};
  Object.keys(source).forEach(key => allsource = Object.assign(allsource, source[key]))

  usedKeys.forEach((value, key) => {
    if (allsource && allsource[key]) {
      delete allsource[key]
    }
  });
  return allsource;
}

const generatorResponse = (source) => fs.writeFileSync(unusedkeyPath, JSON.stringify(_.map(source, obj => obj.id)));

const detectSharkKey = (file) => {
  content = readFile(file);
  const usedKeys = getSharks(content);
  usedKeys.map(key => {
    sharkMap.set(key, sharkMap.has(key) ? sharkMap.get(key) + 1 : 1);
  });
  const usedKeys2 = getShark2(content);
  if (usedKeys2.length > 0) {
    console.log('<SharkValueText使用到的key ', usedKeys2);
  }
  usedKeys2.map(key => {
    sharkMap.set(key, sharkMap.has(key) ? sharkMap.get(key) + 1 : 1);
  });

  const usedKeys3 = getShark3(content);
  if (usedKeys3.length > 0) {
    console.log('getSharkValue换行使用到的key ', usedKeys3);
  }
  usedKeys3.map(key => {
    sharkMap.set(key, sharkMap.has(key) ? sharkMap.get(key) + 1 : 1);
  });
}

const getSharks = content => {
  const matchs = content.match(reg) || [];
  return matchs.length === 0 ? matchs : matchs.map(m => m.replace('getSharkValue(\'', '').replace('\'', ''));
}

const getShark2 = (content) => {
  const matchs = content.match(reg2) || [];
  return matchs.length === 0 ? matchs : matchs.map(m => m.replace(/\s|[\r\n]/g, '').replace('<SharkValueTextdata=\"', '').replace('\"', ''));
}

const getShark3 = (content) => {
  const matchs = content.match(reg3) || [];
  return matchs.length === 0 ? matchs : matchs.map(m => m.replace(/\s|[\r\n]/g, '').replace('getSharkValue(\'', '').replace('\'', ''));
}

const readFile = dir => (fs.readFileSync(dir)).toString();
// TIPS @rn_com_car 打入node_modules, 路径是 '../node_modules/@ctrip/rn_trip_com_car/dist/src/SharkKeys';
const sharkKeyPath = '../build/src/Common/src/SharkKeys';

function deleteUnusedKeys() {
  const unusedKeys = require(unusedkeyPath);
  console.log('unusedKeys个数', unusedKeys.length)
  recursion(path.resolve(__dirname, sharkKeyPath), function (file) {
    if (
      !(file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js'))
    ) {
      return;
    }
    const fileContent = readFile(file);
    const reg = new RegExp(`\\S+:\\s+{[^{}]+id:\\s+'(\\S+)'.+?}\\s*(,|})`, 'msg');
    const replaceFileContent = fileContent.replace(reg, (match, $1) => {
      if (unusedKeys.includes($1)) {
        console.log('delete ', $1)
        _.pull(unusedKeys, $1)
        return ''
      }
      return match
    })

    fs.writeFileSync(file, replaceFileContent)
  });

  console.log('剩余unusedKeys', unusedKeys.length, JSON.stringify(unusedKeys))
}

main();
