const fs = require('fs');
const path = require('path');

function recursion(dirs, cb, isFilterCommon = true) {
  (fs.readdirSync(dirs) || []).map((file) => {
    const absPath = path.resolve(dirs, file);
    const stat = fs.statSync(absPath);
    if (stat.isFile()) {
      cb(absPath);
    } else if (stat.isDirectory()) {
      if (isFilterCommon && absPath.indexOf('src/Common') > -1) return;
      recursion(absPath, cb);
    }
  });
}

module.exports = {
  recursion,
};
