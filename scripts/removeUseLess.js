const fs = require('fs');
const path = require('path');
const babel = require('@babel/core');
const { recursion } = require('./utils');

const rootUrl = 'src'; // 代码根目录，用于检测该目录下的代码是否有用
// const rootUrl = 'src/State'; // 代码根目录，用于检测该目录下的代码是否有用 Debug 删除
const dirRootUrl = 'src/State'; // 需要下线无用导出代码的路径，分批按目录检测下线无用代码，防止更改文件过多
const sagaActionUrl = 'src/State'; //指定sagaAction的路径，saga函数下线需要特殊处理
const channelList = ['crn', 'mini', 'h5']; // 不同渠道的特定文件
const sagaActionRoot = path.resolve(sagaActionUrl);
let exportList = [];
let exportTSList = []; // ts文件导出内容
let sagaActionTypeALL = []; // sagaAction所对应的Type的全量导出，用于Action的下线(不引用Action的调用方式：例如store.dispatch({type: 'LocationaAndDate/GET_LOCATION_DATE_INFO'}))
let sageLogicFileList = []; // 需要检查的Logic文件列表
let sageReducerFileList = []; // 需要检查的Reducer文件列表
let isSagaAction = false; // 是否是sagaAction检测, 此类检测需特殊处理
let curRemovePath = '';
let isContinueCheck = true; // 是否继续全局检查引用
// 本地console.log调试开关
const isDebug = true;
let logFile = '';
const logFilePath = './scripts/removeUseLessLog.txt';

function getNodeStartEnd(node) {
  return {
    start: node?.start || node?.loc?.start?.index,
    end: node?.end || node?.loc?.end?.index,
  };
}

function isChannelFile(path) {
  let isChannel = false;
  channelList.forEach(item => {
    if (path.indexOf(`.${item}.`) > -1) {
      isChannel = true;
    }
  });
  return isChannel;
}

function stopCheck() {
  isContinueCheck = false;
  // 清空检查文件的导出
  exportList = [];
  exportTSList = [];
}

// 获取Action文件对应的Type、Logic、Reducer文件
function getSagaFilePath(path, suffix) {
  let typeFilePath = '';
  [
    path.replace('Actions', `${suffix}s`),
    path.replace('Actions', suffix),
    path.replace('Action', `${suffix}s`),
    path.replace('Action', suffix),
  ].forEach(filePath => {
    // 读取文件
    try {
      let content = fs.readFileSync(filePath).toString();
      typeFilePath = filePath;
    } catch (e) {}
  });
  return typeFilePath;
}

// 获取SagaAction中导出函数代码中的Type字段 可能有多个Type
function getSagaActionType(code) {
  let scanCode = code;
  const actionTypeList = [];
  let actionTypeStart = scanCode.indexOf('type:');
  let actionTypeEnd = 0;
  let actionType = '';

  while (actionTypeStart > -1) {
    scanCode = scanCode.substring(actionTypeStart + 5);
    actionTypeEnd = scanCode.indexOf(',');
    actionType = scanCode.substring(0, actionTypeEnd).trim();
    if (actionType.indexOf(' ') === -1) {
      actionTypeList.push(actionType);
    }
    scanCode = scanCode.substring(actionTypeEnd);
    actionTypeStart = scanCode.indexOf('type:');
  }
  return actionTypeList;
}

function getExportNamedDeclarationList(path, content, actionTypePath) {
  const node = path.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const textContext = content.substring(nodeStart, nodeEnd);
  const list = [];
  // debug 删除
  // if (textContext.indexOf('LisenceType') > -1) {
  //   debugLog(`检查文件的导入内容 ${textContext}\n${JSON.stringify(node)}`);
  // }
  switch (node?.declaration?.type) {
    case 'VariableDeclaration':
      node?.declaration?.declarations?.forEach(item => {
        if (item?.id?.name) {
          if (isSagaAction) {
            const actionTypeList = getSagaActionType(textContext);
            // if (actionTypeList.length > 1) {
            //   debugLog(
            //     `注意：当前文件 ${path} 存在多个ActionType 导出函数代码\n${textContext}`,
            //   );
            // } else if (actionTypeList.length < 1) {
            //   debugLog(
            //     `注意：当前文件 ${path} 没有ActionType 导出函数代码\n${textContext}`,
            //   );
            // }
            actionTypeList.forEach(actionType => {
              list.push({
                name: item?.id?.name,
                type: actionType,
                code: textContext,
                actionTypePath,
              });
            });
          } else {
            list.push({
              name: item?.id?.name,
              code: textContext,
            });
          }
        }
      });
      break;
    case 'TSInterfaceDeclaration':
    case 'TSTypeAliasDeclaration':
    case 'TSEnumDeclaration':
      if (node?.declaration?.id?.name) {
        list.push({
          name: node?.declaration?.id?.name,
          code: textContext,
          nameCode: node?.declaration?.id?.name,
          isTypeScript: true,
        });
      }
      break;
    case 'FunctionDeclaration':
      if (node?.declaration?.id?.name) {
        list.push({
          name: node?.declaration?.id?.name,
          code: textContext,
        });
      }
      break;
  }

  node?.specifiers?.forEach(item => {
    if (item?.exported?.name && item?.type === 'ExportSpecifier') {
      list.push({
        name: item?.exported?.name,
        code: textContext,
        isExport: true,
      });
    }
  });
  return list;
}

function getImportNamedDeclarationList(path, content) {
  const node = path.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const textContext = content.substring(nodeStart, nodeEnd);
  const list = [];
  // debug 删除
  // debugLog(`获取导入的内容字段==` + textContext);
  // debugLog(`获取导入的节点数据==` + JSON.stringify(node));
  node?.specifiers?.forEach(item => {
    switch (item.type) {
      case 'ImportDefaultSpecifier':
        if (item?.local?.name) {
          list.push({
            name: item?.local?.name,
            isDefault: true,
            nameCode: content.substring(item?.start, item?.end),
            code: textContext,
          });
        }
        break;
      case 'ImportSpecifier':
        if (item?.imported?.name) {
          list.push({
            name: item?.imported?.name,
            nameCode: content.substring(item?.start, item?.end),
            code: textContext,
          });
        }
        break;
      default:
        break;
    }
  });
  return list;
}

function getExportDefaultDeclaration(path, content, actionTypePath) {
  const node = path.node;
  const { start: nodeStart, end: nodeEnd } = getNodeStartEnd(node);
  const textContext = content.substring(nodeStart, nodeEnd);
  const list = [];
  list.push({
    name: textContext,
    code: textContext,
    actionTypePath: isSagaAction ? actionTypePath : '',
    isDefault: true,
  });
  return list;
}

function debugLog(message) {
  logFile += `${message} \n`;
  if (isDebug) {
    console.log(message);
  }
}

// 获取两个文件的引用相对路径
function getRelativePath(from, to) {
  let relativePath = path.relative(from, to);
  relativePath = relativePath.replace('../', '');
  // 去除后缀名
  relativePath = relativePath.substring(0, relativePath.lastIndexOf('.'));
  if (relativePath.indexOf('../') < 0) {
    relativePath = `./${relativePath}`;
  }
  const indexAttr = relativePath.substring(relativePath.length - 6);
  if (indexAttr === '/index') {
    return [relativePath, relativePath.substring(0, relativePath.length - 6)];
  }
  return [relativePath];
}

// 判断文件导出的内容是否在内部有使用 仅粗略判断
function isInnerUseLess(content, exportCode, exportName, isFn = false) {
  const useCode = isFn ? `${exportName}(` : exportName;
  const isInnerNoUse = content?.replace(exportCode, '')?.indexOf(useCode) === -1;
  // if (!isInnerNoUse) {
  //   debugLog(`内部存在其它调用，${content?.replace(exportCode, '')} \n 调用代码 ${useCode}`);
  // }
  return isInnerNoUse;
}

// 判断文件是否有引用当前文件, 并返回引入的路径代码
function isRequireImport(content, relativePath, isRequire = false) {
  let requireStr = '';
  relativePath.forEach(rPath => {
    const requirePathCode = isRequire ? `require('${rPath}')` : `'${rPath}';`;
    if (content.indexOf(requirePathCode) > -1) {
      requireStr = requirePathCode;
    }
  });
  return requireStr;
}

// 判断文件是否是SagaAction文件
function isSagaActionFile(path) {
  return path.indexOf(sagaActionRoot) > -1 && path.indexOf('Action') > -1;
}

// 判断是否是SagaLogic函数
function isSagaLogicCode(fnCode) {
  // TODO 判断Logic函数时，最好判断SagaAction所对应的ActionPattern（即Type）不为字符窜或者数组
  // 即需要满足单个ActionPattern且Type存在与Types文件中
  return (
    fnCode.indexOf('function*') > -1 &&
    /takeLatest|takeEvery|takeLeading/.test(fnCode)
  );
}

// 判断是否时SagaReducer函数
function isSagaReducerCode(caseCode, switchCode) {
  const isRootSwitch = switchCode.indexOf('.type)') > -1;
  const isDefaultCase = caseCode.indexOf('default:') > -1;
  return isRootSwitch && !isDefaultCase;
}

// 收集Action所对应的Type字段
function collectActionTypes(actionTypePath) {
  if (isChannelFile(actionTypePath) || actionTypePath.indexOf('/Type') === -1) {
    return;
  }
  let content = fs.readFileSync(actionTypePath).toString();
  babel.transform(content, {
    plugins: [
      {
        visitor: {
          ExportDeclaration(path) {
            const node = path.node;
            const textContext = content.substring(node?.start, node?.end);
            // debug 删除
            // debugLog(`导出代码 ${textContext}\n导出节点 ${JSON.stringify(node)}`);
            node?.declaration?.declarations?.forEach(item => {
              if (item?.id?.name && item?.init?.value) {
                sagaActionTypeALL.push({
                  name: item?.id?.name,
                  value: item?.init?.value,
                  actionTypePath,
                });
              }
            });
          },
        },
      },
    ],
    filename: actionTypePath,
  });
}

// 检查是否有外部调用Action并标记，用于Logic的下线
function checkExternalCallAction(content, path) {
  const isExternalCall = content.indexOf('.dispatch(') > -1;
  if (isExternalCall) {
    sagaActionTypeALL.forEach(action => {
      if (content.indexOf(action.value) > -1) {
        // debugLog(
        //   `被引用文件 ${path} 有外部调用，标记的ActionType ${action.name} ____ ${action.value}`,
        // );
        action.isExternalCall = true;
      }
    });
  }
}

// 1、收集未使用的导出内容
function collectUseLess(path) {
  if (!/\.ts(x)?$/.test(path) || /\/dist/.test(path)) {
    return;
  }
  // 读取文件
  let content = fs.readFileSync(path).toString();
  let actionTypePath = '';
  if (isSagaAction) {
    actionTypePath = getSagaFilePath(path, 'Type');
    sageLogicFileList.push(getSagaFilePath(path, 'Logic'));
    sageReducerFileList.push(getSagaFilePath(path, 'Reducer'));
  }
  exportList = [];
  exportTSList = [];
  curRemovePath = path;
  isContinueCheck = true;
  // debug 删除
  // debugLog(`path==${path} \n`)
  babel.transform(content, {
    plugins: [
      {
        visitor: {
          ExportDeclaration(path) {
            const node = path.node;
            // debug 删除
            // debugLog(`检查文件的导出节点 ${JSON.stringify(node)}`)
            switch (node.type) {
              case 'ExportNamedDeclaration':
                const exportNamedList = getExportNamedDeclarationList(
                  path,
                  content,
                  actionTypePath,
                );
                if (exportNamedList.length > 0) {
                  exportList.push(...exportNamedList);
                }

                break;
              // 默认导出不支持高阶函数，例如memo(Component)，
              case 'ExportDefaultDeclaration':
                const exportDefaultList = getExportDefaultDeclaration(
                  path,
                  content,
                  actionTypePath,
                );
                if (exportDefaultList.length > 0) {
                  exportList.push(...exportDefaultList);
                }
                break;
              default:
                break;
            }
          },
        },
      },
    ],
    filename: path,
  });
  // 如果导入内容包括interface内容，保存起来，用于ts导出的特殊处理
  exportTSList = exportList.filter(item => !!item.isTypeScript);
  if (!(exportList.length > 0)) {
    // debugLog(`注意：当前文件 ${path} 没有任何导出内容`);
  }
}

// 2、检查是否有文件使用
function checkUseLess(path) {
  if (
    !/\.ts(x)?$/.test(path) ||
    /\/dist|c2x\/components|xcar\/Common\/src/.test(path) ||
    curRemovePath === path ||
    !isContinueCheck
  ) {
    return;
  }
  // Debug 删除
  // if (path.indexOf('DepositPaymentBlock') < 0) {
  //   return;
  // }
  // 判断文件引入正在检查的文件路径
  const relativePath = getRelativePath(path, curRemovePath);
  const importList = [];
  // debugLog("当前文件路径==" + curRemovePath + " 检查的文件路径==" + path + "相对路径==" + JSON.stringify(relativePath));

  // 读取文件
  let content = fs.readFileSync(path).toString();
  // 判断当前文件是否有require引用，如果有，当前检测文件放弃检测，需手工下线代码
  if (isRequireImport(content, relativePath, true)) {
    stopCheck();
    debugLog(
      `\n检查文件在被引用文件中进行了 ${`require('${relativePath[0]}')`} 引入，放弃无用代码检查：${path} `,
    );
    return;
  }
  // 判断当前文件是否是Action的检测，且是否有对应的Action的Type调用 例如store.dispatch({type: 'LocationaAndDate/GET_LOCATION_DATE_INFO'}代码
  if (isSagaAction) {
    // 检查文件是否有外部调用
    checkExternalCallAction(content, path);
  }
  const endStr = isRequireImport(content, relativePath, false);
  // debug 删除
  // debugLog("引入变量=====" + endStr);
  // 判断当前文件是否有相对路径的引用
  if (!endStr) {
    return;
  }
  // 当前文件有引入且未使用的代码， 需要删除
  const startStr = 'import';
  const useLessImportEnd = content.indexOf(endStr);
  const tempStr = content.substring(0, useLessImportEnd);
  const useLessImportStart = tempStr.lastIndexOf(startStr);
  const before =
    content.substring(useLessImportStart, useLessImportEnd) + endStr;
  // debugLog("检测引入文件路径:" + path + "\nbefore===" + before);
  // debugLog(`开始检测被引用文件，路径${path}，引入内容：`);
  babel.transform(content, {
    plugins: [
      {
        visitor: {
          // 被引入文件中，Interface引入或者Enum引入，且没有使用的代码无法获取，需特殊处理
          ImportDeclaration(path) {
            const node = path.node;
            const textContext = content.substring(node?.start, node?.end);
            // debug
            // if (textContext.indexOf('LisenceType') > -1 && textContext.length < 1000) {
            //   debugLog(`被引入文件的引用代码 ${textContext}`);
            //   debugLog(JSON.stringify(node));
            // }
            if (
              textContext.indexOf(endStr) > -1 &&
              node?.start >= 0 &&
              node?.end >= 0
            ) {
              importList.push(...getImportNamedDeclarationList(path, content));
            }
          },
        },
      },
    ],
    filename: path,
  });

  // 如果当前文件中有ts导出，且被引用文件的导入语句中有ts变量，则新增相应导入内容
  if (exportTSList?.length > 0) {
    // debugLog("检查文件中，TypeScript类型内容\n" + JSON.stringify(exportTSList));
    exportTSList.forEach(tsItem => {
      const isExistImport = importList.find(
        iItem => iItem.name === tsItem.name,
      );
      const { objectInfoList } = getObjectInfoList(before);
      // 是否存在导入语句 TODO 如果是无用引用如何删除
      const isExistObjectInfo = objectInfoList.find(
        item => item.trim() === tsItem?.name,
      );
      const isExistFile = content.lastIndexOf(tsItem?.name) > useLessImportEnd;
      // debugLog(`当前导入语句内容${JSON.stringify(objectInfoList)} 是否存在导入语句中=${isExistObjectInfo} tsItem.name=${tsItem.name}`);
      // 在非引入语句中是否存在 模糊判断是否包含，存在极限情况，页面没有引入导出内容，但没有做删除处理 TODO
      if (!isExistImport && isExistObjectInfo && isExistFile) {
        importList.push({
          name: tsItem?.name,
          nameCode: tsItem?.name,
          code: before,
          isTypeScript: true,
        });
      }
    });
  }

  // debugLog(JSON.stringify(importList));
  // 如果有引入当前检查的文件内容
  if (importList?.length > 0) {
    // debugLog(`当前文件目录 ${path}  引入的内容  ${JSON.stringify(importList)}`);
    // 如果当前文件有引入检查内容的语句，且有未使用的导入，则需要删除未使用的导入
    const replaceExpress = getImportReplaceExpression(importList);
    if (replaceExpress?.length > 0) {
      debugLog(`\n被引用文件中有无用引用，进行删除路径，${path}`);
      // debugLog(`引用文件引入的内容` + JSON.stringify(replaceExpress));
      replaceExpress.forEach(rExpress => {
        content = content.replace(rExpress.before, rExpress.after);
        debugLog(`替换前 ${rExpress.before} \n替换后 ${rExpress.after}`);
      });
      // 保存删除未使用导入代码的文件
      fs.writeFileSync(path, content);
    }

    // debugLog(`\n文件目录: ${path} 导出内容被引用的列表\n${JSON.stringify(importList)}`);
    const unImportExportList = [];
    exportList.forEach(eItem => {
      const isExist = importList?.find(
        iItem =>
          iItem.name === eItem.name || (!!iItem.isDefault && !!eItem.isDefault),
      );
      if (!isExist) {
        unImportExportList.push(eItem);
      }
    });
    exportList = unImportExportList;
    if (!(exportList?.length > 0)) {
      isContinueCheck = false;
      debugLog(`\n检查文件中导出的内容均有被引用，检查无用代码结束`);
    }
  } else {
    // 如果导入语句中使用了*号，则不进行无用引入检测
    const isAllImport = before.indexOf('*') > -1;
    if (!isAllImport) {
      const after = '';
      content = content.replace(before, after);
      debugLog(`\n被引用文件进行了无用引用，进行删除`);
      debugLog(`\n替换前 ${before} \n替换后 ${after}`);
      // 保存删除未使用导出代码的文件
      fs.writeFileSync(path, content);
    } else {
      stopCheck();
      debugLog(
        `\n检查文件在被引用文件中进行了 * 全部引入，放弃无用代码检查：${path} `,
      );
    }
  }
}

// 获取导入或者导出语句中 {} 中的内容
function getObjectInfoList(code) {
  let objectInfoList = [];
  let startIndex = code.indexOf('{');
  let endIndex = code.indexOf('}');
  let beforeStr = code.substring(0, startIndex);
  let objectInfoStr = code.substring(startIndex + 1, endIndex);
  objectInfoList = objectInfoStr.split(',');
  return {
    startIndex,
    beforeStr,
    objectInfoList,
    objectInfoStr,
  };
}

// 获取导入的内容的删除未使用导入的表达式
function getImportReplaceExpression(importList) {
  const replaceExpressionList = [];
  importList.forEach(item => {
    // 默认导出内容不进行处理
    if (item.isDefault) {
      return;
    }
    const { code } = item;
    // 获取当前导入语句有多少个导入变量
    const { startIndex, beforeStr, objectInfoStr, objectInfoList } =
      getObjectInfoList(code);
    const objectInfoListLength = objectInfoList.filter(
      eItem => eItem.length > 1,
    ).length;
    // 获取当前使用的导入变量个数
    const usedImport = [];
    let isHasDefaultImport = false;
    importList.forEach(fitem => {
      if (fitem.code == code) {
        if (fitem.isDefault) {
          isHasDefaultImport = true;
        } else {
          usedImport.push(fitem.nameCode);
        }
      }
    });
    if (objectInfoListLength !== usedImport.length) {
      // debugLog("导入内容数量不一致" + objectInfoListLength + "      " + usedImport.length);
      const usedListStr = ` ${usedImport.join(', ')} `;
      const isExist = replaceExpressionList.find(
        rItem => rItem.before === code,
      );
      if (!isExist) {
        let after = code.replace(objectInfoStr, usedListStr);
        if (
          !isHasDefaultImport &&
          startIndex > -1 &&
          beforeStr.indexOf(',') > -1
        ) {
          after = code.replace(beforeStr, 'import ');
        }
        replaceExpressionList.push({
          before: code,
          after,
        });
      }
    }
  });
  return replaceExpressionList;
}

// 获取导出的内容的删除表达式
function getExportReplaceExpression(code, name, isExport, isInnerNoUse) {
  if (isExport) {
    // 获取当前导出语句有多少个导出变量
    const { objectInfoStr, objectInfoList } = getObjectInfoList(code);
    let exportIndex = objectInfoList.findIndex(item => item.trim() === name);
    if (exportIndex > -1) {
      objectInfoList.splice(exportIndex, 1);
    }
    if (objectInfoList.length > 0) {
      return {
        before: code,
        after: code.replace(objectInfoStr, objectInfoList.join(',')),
      };
    } else {
      return {
        before: code,
        after: '',
      };
    }
  } else {
    // NOTE 如果是默认导出，则全部删除
    // 如果是内部无使用，则全部删除
    // 否则仅删除export代码
    const isDefaultExport = code.indexOf('export default') > -1;
    return {
      before: code,
      after: isDefaultExport || isInnerNoUse ? '' : code.replace('export ', ''),
    };
  }
}

// 3、删除未使用导出代码
function deleteUseLessCode(path) {
  if (exportList.length > 0) {
    debugLog(`\n开始删除未使用导出代码`);
    // 读取文件
    let content = fs.readFileSync(path).toString();
    const replaceExpression = [];
    exportList.forEach(item => {
      const isExistIndex = replaceExpression.findIndex(
        rItem => rItem.before === item.code,
      );
      const isInnerNoUse = isInnerUseLess(content, item.code, item.name, !!item.type);
      // 如果SagaAction文件的删除
      // 设置全量的ActionType删除状态，什么情况下可以删除
      // 没有外部调用且name相等
      if (isSagaAction && isInnerNoUse) {
        sagaActionTypeALL.forEach(typeAction => {
          if (
            !typeAction.isExternalCall &&
            typeAction.name === item.type &&
            typeAction.actionTypePath === item.actionTypePath
          ) {
            // debugLog(`下线Action ${JSON.stringify(item)}`);
            typeAction.isDelete = true;
          }
        });
      }
      // debug 删除
      // if (isInnerNoUse) {
      //   debugLog(
      //     `不存在内部引用 ${content.replace(
      //       item.code,
      //       '',
      //     )} \n不存在内部引用代码 ${item.code} \n${item.name}`,
      //   );
      // }
      if (isExistIndex > -1) {
        replaceExpression[isExistIndex].after = getExportReplaceExpression(
          replaceExpression[isExistIndex].after,
          item.name,
          item.isExport,
          isInnerNoUse,
        ).after;
      } else {
        replaceExpression.push(
          getExportReplaceExpression(
            item.code,
            item.name,
            item.isExport,
            isInnerNoUse,
          ),
        );
      }
    });
    replaceExpression.forEach(rItem => {
      content = content.replace(rItem.before, rItem.after);
      debugLog(`\n替换前 ${rItem.before} \n替换后 ${rItem.after}`);
    });
    // 保存删除未使用导出代码的文件
    fs.writeFileSync(path, content);
  }
}

// 删除Logic文件中未使用的函数
function deleteLogicUseLessCode() {
  // debugLog(`全量的Logic文件= ${JSON.stringify(sageLogicFileList)}`);
  sageLogicFileList.forEach(logicPath => {
    // 读取文件
    try {
      let content = fs.readFileSync(logicPath).toString();
      const replaceExpress = [];
      babel.transform(content, {
        plugins: [
          {
            visitor: {
              ExportDeclaration(path) {
                const node = path.node;
                const textContext = content.substring(node?.start, node?.end);
                const isExist = replaceExpress.find(
                  rItem => rItem.before === textContext,
                );
                const isLogicFn = isSagaLogicCode(textContext);

                if (!isExist && isLogicFn) {
                  let isExistActionType = false;
                  sagaActionTypeALL.forEach(actionType => {
                    // Logic判断是否删除时支持变量和字符窜的判断
                    if (
                      !actionType.isDelete &&
                      (textContext.indexOf(actionType.name) > -1 ||
                        textContext.indexOf(actionType.value) > -1)
                    ) {
                      // debugLog(
                      //   `存在于ActionType的调用 ${textContext} \n ${JSON.stringify(actionType)}`,
                      // );
                      isExistActionType = true;
                    }
                  });
                  if (!isExistActionType) {
                    let exportFnName = `${node?.declaration?.id?.name}()`;
                    if (content.indexOf(`${exportFnName},`) > -1) {
                      exportFnName = `${exportFnName},`;
                    }
                    replaceExpress.push({
                      before: textContext,
                      after: '',
                    });
                    replaceExpress.push({
                      before: exportFnName,
                      after: '',
                    });
                  }
                }
              },
            },
          },
        ],
        filename: logicPath,
      });
      if (replaceExpress?.length > 0) {
        debugLog(`\n处理SagaLogic文件，${logicPath} 存在未使用代码`);
        replaceExpress.forEach(rExpress => {
          content = content.replace(rExpress.before, rExpress.after);
          debugLog(`替换前 ${rExpress.before} \n替换后 ${rExpress.after}`);
        });
        // 保存删除未使用导入代码的文件
        fs.writeFileSync(logicPath, content);
      }
    } catch (e) {
      // debugLog('异常报错啦--' + e.message);
    }
  });
}

// 删除Reducer文件中未使用的函数
function deleteReducerUseLessCode() {
  // debugLog(`全量的Reducer文件= ${JSON.stringify(sageReducerFileList)}`);
  sageReducerFileList.forEach(reducerPath => {
    // 读取文件
    try {
      let content = fs.readFileSync(reducerPath).toString();
      const replaceExpress = [];
      babel.transform(content, {
        plugins: [
          {
            visitor: {
              SwitchCase(path) {
                const node = path.node;
                const parentNode = path.parentPath.node;
                const textContext = content.substring(node?.start, node?.end);
                // debugLog(`当前Case ${textContext}`);
                const parentTextContent = content.substring(
                  parentNode?.start,
                  parentNode?.end,
                );

                const isExist = replaceExpress.find(
                  rItem => rItem.before === textContext,
                );
                let isExistActionType = false;
                sagaActionTypeALL?.forEach(actionTypeItem => {
                  if (
                    !actionTypeItem.isDelete &&
                    (textContext.indexOf(actionTypeItem?.name) > -1 ||
                      textContext.indexOf(actionTypeItem?.value) > -1)
                  ) {
                    // debugLog(`存在与ActionType中=${JSON.stringify(actionTypeItem)}`);
                    isExistActionType = true;
                  }
                });
                const isReducerCase = isSagaReducerCode(
                  textContext,
                  parentTextContent,
                );
                // debugLog(`当前Case ${textContext}  ${isRootSwitch}`);
                if (isReducerCase && !isExist && !isExistActionType) {
                  replaceExpress.push({
                    before: textContext,
                    after: '',
                  });
                }
              },
            },
          },
        ],
        filename: reducerPath,
      });
      if (replaceExpress?.length > 0) {
        debugLog(`\n处理SagaReducer文件，${reducerPath} 存在未使用代码`);
        replaceExpress.forEach(rExpress => {
          content = content.replace(rExpress.before, rExpress.after);
          debugLog(`替换前 ${rExpress.before} \n替换后 ${rExpress.after}`);
        });
        // 保存删除未使用导入代码的文件
        fs.writeFileSync(reducerPath, content);
      }
    } catch (e) {}
  });
}

// 下线未使用代码
function removeUseLess(path) {
  // 暂时不支持渠道特定文件的检测，例如ReducerRouter.crn.ts文件
  if (!/\.ts(x)?$/.test(path) || /\/dist/.test(path) || isChannelFile(path)) {
    return;
  }
  // Debug 需要删除
  // if (path.indexOf('Action') < 0) {
  //   return;
  // }
  isSagaAction = isSagaActionFile(path);
  // 如果是Action文件检查，则初始化所有导出的actionType
  if (isSagaAction && !(sagaActionTypeALL.length > 0)) {
    recursion(sagaActionUrl, collectActionTypes);
    // debug 删除
    // debugLog(`全量导出的SagaType`);
    // sagaActionTypeALL.forEach(item => {
    //   debugLog(JSON.stringify(item));
    // });
  }
  // 收集文件导出内容
  collectUseLess(path);
  if (exportList.length > 0) {
    debugLog(`\n开始无用代码检测，文件路径： ${path}`);
    // debugLog(`\n文件导出内容 \n${JSON.stringify(exportList)}`);

    recursion(rootUrl, checkUseLess);
    // 查询导出的内容是否有文件引用
    if (exportList.length > 0) {
      debugLog(`检查文件存在以下导出未使用`);
      exportList.forEach(item => {
        debugLog(item.name);
      });
    }

    // 删除未被使用代码
    deleteUseLessCode(path);
  }
}

function startRemove() {
  debugLog(`\n开始无用导出代码检测，目录：${dirRootUrl} `);
  recursion(dirRootUrl, removeUseLess);
  // debug 删除
  // debugLog(`全量导出的SagaType`);
  // sagaActionTypeALL.forEach(item => {
  //   if (item.isDelete) {
  //     debugLog(`需要下线的Action  ${JSON.stringify(item)}`);
  //   }
  // });
  deleteLogicUseLessCode();

  deleteReducerUseLessCode();

  fs.writeFileSync(logFilePath, logFile, err => {
    debugLog('\n创建日志文件失败' + err);
  });

  debugLog(`\n日志文件路径：${path.resolve(logFilePath)}`);
}

function main() {
  startRemove();
}

main();
