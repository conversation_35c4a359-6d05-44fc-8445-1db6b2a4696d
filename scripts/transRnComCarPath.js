/**
 * 这个脚本主要是实现
 * 遍历rn_com_car目录下的所有文件，将引用的@ctrip/bbk组件转换为@ctrip/rn_com_car：
 */

const fs = require('fs-extra');
const path = require('path');
const babelParser = require("@babel/parser");
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');
const generate = require('@babel/generator').default;
const { recursion } = require('./utils');
const { bbkMaps } = require('./bbkMaps');  
const rnComCarPath = path.resolve(__dirname, '../src');

function main() {
    recursion(rnComCarPath, transRnComCarPath)
};

const options = {
    sourceType: 'module',
    allowHashBang: true,
    ecmaVersion: Infinity,
    allowImportExportEverywhere: true,
    allowReturnOutsideFunction: true,
    plugins: [
        'exportDefaultFrom',
        'jsx',
        'typescript',
    ],
};

function transRnComCarPath(file) {
    if (!(path.extname(file).match(/^\.(ts|tsx|js)$/g))) return;
    var content = fs.readFileSync(file).toString();
    var importMatchArray = content.match(/^import(?:.|\n)+from[\s]+'(.+)$/mg) || [];
    var importLast = importMatchArray.length > 0 ? importMatchArray[importMatchArray.length-1] : 0;
    var importLastIndex = content.lastIndexOf(importLast) + importLast.length;
    var importContent = content.substring(0, importLastIndex);
    var otherContent = content.substring(importLastIndex);
    var newContent = '';
    var newImportContent = '';

    var flag = false;
    var ast = babelParser.parse(importContent, options);

    traverse(ast, {
        ImportDeclaration(nodePath) {
            const importValue = nodePath.node.source.value;
            if (importValue.indexOf('@ctrip/bbk') > -1) {
                const isPathHasDist = importValue.indexOf('dist') > -1;
                const isPathHasSrc = importValue.match(/^.+(?<!dist)\/src.+$/g);
                if (isPathHasSrc) {
                    debugger;
                }
                const bbkName = isPathHasSrc ? importValue.substring(importValue.indexOf('/')+1, importValue.indexOf('src')-1)
                                : isPathHasDist ? importValue.substring(importValue.indexOf('/')+1, importValue.indexOf('dist')-1)
                                : importValue.substring(importValue.indexOf('/')+1) ;
                if (bbkMaps[bbkName]) {
                    const newNpmPath = `@ctrip/rn_com_car/dist/src/${bbkMaps[bbkName]}`;
                    const newPath = isPathHasSrc ? `${newNpmPath}${importValue.substring(importValue.indexOf('src')-1)}` 
                                    : isPathHasDist ? `${newNpmPath}${importValue.substring(importValue.indexOf('dist')+4)}` : newNpmPath;
                    nodePath.replaceWith(t.importDeclaration(nodePath.node.specifiers, t.stringLiteral(newPath)));
                    flag = true;
                    console.log(`${file}中的${importValue} 替换为 ${nodePath.node.source.value}`)
                }      
            }
        }
    });

    if (flag) {
        newImportContent = generate(ast, { }, importContent).code;
        newContent = newImportContent.replace(/"/g, "'") + otherContent;
        fs.writeFileSync(file, newContent, 'utf-8');
    }
};

main();
