{"name": "rn_car_home_isd", "version": "0.0.18", "appVersion": "8.15", "private": true, "scripts": {"patch": "crn-cli run-patch", "start": "crn-cli start --port 5388 --reset", "ios": "crn-cli run-ios --app-version 8.68.6 --port 5388 --simulator 'iPhone 14 Pro' --reset && npm run ts:watch", "lint": "npm run lint:js", "lint:js": "eslint . --ignore-path .eslint<PERSON>ore --ext .js,.ts,.tsx", "lint:fix": "npm run lint:js:fix", "lint:js:staged": "eslint . --ignore-path .eslint<PERSON>ore", "lint:js:fix": "npm run lint:js -- --fix", "lint:js:fix:staged": "eslint --ignore-path .eslint<PERSON>ore --fix", "ts:build": "tsc && tsc-alias && npm run lazyRequire", "ts:watch": "tsc && (concurrently \"tsc -w\" \"tsc-alias -w\")", "build:version": "sh ./scripts/BuildVersion.sh", "precommit": "lint-staged", "test": "jest --coverage --runInBand --forceExit", "test:ui": "jest --config jest.config.ui.js --json --outputFile='./coverage/case-result.json'", "test:ut": "jest --coverage --config jest.config.ut.js --detectOpenHandles  --forceExit", "test:new": "jest --coverage --changedSince=master-064 --coverageThreshold='{\"global\":{\"branches\":\"50\"}}' --detectOpenHandles  --forceExit", "test:watch": "jest --watch --debug --verbose --coverage=false", "test:fix": "npm run test -- -u", "clean": "npm cache clean --force && watchman watch-del-all && rm -rf package-lock.json && rm -rf node_modules && rm -rf $TMPDIR/metro-* && rm -rf $TMPDIR/haste-map-* && npm run install:ctrip", "install:ctrip": "npm install --registry=--registry=http://artifactory.release.ctripcorp.com/artifactory/api/npm/trip-npm-prod/ --legacy-peer-deps", "tsc": "tsc", "install": "tsc || true", "dev": "react-devtools", "shark:es6to5": "./node_modules/.bin/babel ./build/src/Common/src/SharkKeys --out-dir tmp", "shark:detect": "node --harmony ./scripts/sharkDoctor.js", "shark:format": "node ./scripts/sharkFormat.js", "shark:doctor": "npm run shark:es6to5 && npm run shark:detect && npm run shark:format", "postinstall": "npm run lazyRequire && node ./scripts/postinstall.js && npm run commonScript && tsc-alias", "lazyRequire": "node --harmony ./scripts/lazyRequireReplace", "removeAB": "node ./scripts/removeAB.js", "removeUL": "node ./scripts/removeUseLess.js", "commonScript": "node ./src/Common/scripts/postinstall.js && node ./src/Common/scripts/lazyRequireReplace", "transRnComCar": "node ./scripts/transRnComCarPath.js", "i18nReplace": "node ./scripts/i18n-replace.js"}, "dependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@ctrip/crn": "git+http://git.dev.sh.ctripcorp.com/crn/crn.git#rel/8.68.6", "@ctrip/crn-ext-adsdk": "3.5.12", "@ctrip/devhub-trace-log": "^0.2.3", "@ctrip/ttd-marketing-rn": "3.0.4", "@testing-library/jest-native": "^5.0.0", "@testing-library/react-native": "^11.2.0", "@types/jest": "^24.9.1", "@types/node": "13.13.12", "@types/node-fetch": "^2.6.2", "@types/promise-timeout": "^1.3.0", "@types/react": "16.9.36", "@types/react-native": "^0.60.31", "@types/react-test-renderer": "^16.9.2", "@types/uuid": "^3.4.9", "@types/yallist": "^3.0.1", "babel-cli": "6.26.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-es2015": "6.24.1", "babel-preset-react-native": "5.0.2", "babel-preset-stage-2": "6.24.1", "lodash": "4.17.21", "memoize-one": "^5.1.1", "node-fetch": "^2.6.9", "promise-timeout": "^1.3.0", "qs": "6.9.6", "react": "18.2.0", "react-native": "0.72.5", "react-native-animatable": "1.3.3", "react-native-typescript-transformer": "^1.2.13", "theming": "3.3.0", "tsc-alias": "^1.7.1", "uuid": "^3.4.0", "typescript": "5.4.3", "fs-extra": "^11.2.0", "yallist": "^4.0.0", "@ctrip/rr-react-native": "5.1.4", "zustand": "4.4.4"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@ctrip/eslint-plugin-car-linter": "1.1.0", "@testing-library/react-hooks": "^8.0.1", "@welldone-software/why-did-you-render": "^6.0.3", "commitizen": "^4.1.2", "concurrently": "^7.5.0", "cz-conventional-changelog": "^3.2.0", "esbuild": "^0.18.16", "esbuild-jest": "^0.5.0", "gh-pages": "^3.2.3", "husky": "^4.2.5", "jest": "27.5.1", "jest-junit": "^7.0.0", "jest-react-native": "^18.0.0", "lerna": "^3.10.7", "lint-staged": "^8.1.0", "madge": "^5.0.1", "randomstring": "^1.1.5", "standard-version": "^8.0.0", "ts-jest": "^24.3.0", "webpack-bundle-analyzer": "^3.8.0"}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["npm run lint:js:fix:staged", "npx prettier --write"]}, "husky": {"hooks": {"pre-commit": "npm run build:version && git add src/BuildTime.ts && lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "iconFont": ["crn_font_car_app_v1"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "cwVersion": "6.1.1", "cwBase": "/webapp/cw/rn_car_app", "cwMain": "/index.html?type=h5"}, "overrides": {"react": "18.2.0", "react-native": "0.72.5", "@ctrip/crn": "git+http://git.dev.sh.ctripcorp.com/crn/crn.git#rel/8.68.6", "@ctrip/crn-ext-react-native-svg": "13.14.0", "@ctrip/crn-ext-react-native-webview": "13.6.4", "@ctrip/crn-ext-react-native-gesture-handler": "2.15.0"}}