/* eslint-disable */

const omitData = data => {
  data.depositPayInfos = [data.depositPayInfos[0]];
  data.depositInfo = undefined;
  return data;
};

// F
const dataF = omitData({
  activityDetail: {
    status: 0,
    title: '暂无促销活动',
  },
  addOn: {
    addOnServices: [
      {
        cnyTotalPrice: 798.0,
        count: 1,
        name: '租车基本费用',
        required: true,
        uniqueCode: '1001',
        unitPrice: 399,
        vendorServiceCode: '0',
      },
      {
        cnyTotalPrice: 170.0,
        count: 2,
        desc: '',
        equipcode: '',
        name: '基础服务费',
        required: true,
        uniqueCode: '1002',
        unit: '',
        unitPrice: 85.0,
        vendorServiceCode: '1',
      },
      {
        cnyTotalPrice: 25.0,
        count: 1,
        desc: '',
        equipcode: '',
        name: '车行手续费',
        required: true,
        uniqueCode: '1003',
        unit: '',
        unitPrice: 25.0,
        vendorServiceCode: '2',
      },
      {
        cnyTotalPrice: 10000.0,
        count: 1,
        name: '租车押金',
        required: true,
        uniqueCode: '4003',
        vendorServiceCode: '4003',
      },
      {
        cnyTotalPrice: 2000.0,
        count: 1,
        name: '违章押金',
        required: true,
        uniqueCode: '4004',
        vendorServiceCode: '4004',
      },
    ],
  },
  baseResponse: {
    apiResCodes: [],
    code: '200',
    errorCode: '1001',
    extMap: {
      allCost: '70.0',
      start: '2020-12-28 13:38:42',
      end: '2020-12-28 13:38:42',
    },
    hasResult: true,
    isSuccess: true,
    message: '',
    requestId: 'e0107c8e-30e3-44a8-98b3-1dabd726e1b8',
    returnMsg: 'success',
  },
  cancelRuleInfo: {
    code: 'FreeCancel',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2020-12-28 15:00前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2020-12-28 15:00前可免费取消',
          style: '6',
        },
      ],
    },
    description: '取车时间前可免费取消。',
    items: [
      {
        description: '免费取消',
        showFree: true,
        subTitle: '取车时间前',
        title: '2020-12-28 15:00前',
      },
      {
        description: '扣订单全额',
        showFree: false,
        subTitle: '取车时间后',
        title: '2020-12-28 15:00后',
      },
    ],
    showFree: true,
    subTitle: '2020-12-28 15:00前可免费取消',
    tableTitle: '取消时间|扣费标准',
    title: '取消政策',
    type: 300,
  },
  confirmInfo: {
    code: 'ConfirmImmediately',
    description: '立即确认',
    isInstantConfirm: true,
    title: '立即确认',
    type: 301,
  },
  couponList: {
    selectedCoupon: {
      actionedDate: '2020-12-28 00:00',
      code: 'fpkvlbdegb',
      couponDesc:
        '本优惠券通过携程专车·租车-国内自驾租车频道，以预付方式（在线支付）预订枫叶出行-沃尔沃V60车型可享受首日免租金优惠； 优惠券自领取日起15天内使用有效，取、还车时间在2020年12月31日至2021年1月3日、2021年2月10日至2月18日不可使用，其余时间均可使用；不可与其他优惠叠加使用；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用，若租车费不满可优惠金额，则减免全部租车费后不继续减免； 有效期内每个用户仅限领取一张优惠券，同一设备号，手机号，uid均视为同一用户，每张订单限用一张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
      couponName: '国内租车1日免租立减券',
      couponType: 2,
      deductionAmount: 399,
      description: '',
      expiredDate: '2021-01-12 23:59',
      isEnabled: true,
      isFromCtrip: true,
      payoffName: '立减券',
      payofftype: 2,
      promotionId: 202111389,
      selected: true,
      title: '立减券',
      unionType: 0,
      unitName: '¥',
    },
    status: 1,
    title: '',
    unusableCoupons: [],
    usableCoupons: [
      {
        $ref: '$.couponList.selectedCoupon',
      },
    ],
  },
  creditVersion: '28a0f69e921a57d39fec626e05e8e7c4',
  depositInfo: {
    freezeDeposit: true,
    items: [
      {
        code: 'RentalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 10000.0,
        description: '取车时冻结10000元租车押金，若无车损，还车时解冻',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '租车押金',
        type: 305,
      },
      {
        code: 'IllegalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 2000.0,
        description: '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '违章押金',
        type: 306,
      },
      {
        code: 'DepositPayMode',
        currencyCode: '¥',
        currentTotalPrice: 0,
        description: '支付宝·微信·信用卡',
        retractable: false,
        showFree: false,
        sortNum: 1,
        title: '支付方式',
        type: 308,
      },
    ],
    showCreditCard: true,
    subTitle: '如通过支付宝/微信支付押金，请在门店扫描携程二维码进行押金支付',
    title: '押金说明',
  },
  depositLabel: {},
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        note: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '您暂不可享信用租。建议在线支付押金，到店免支付。',
              style: '1',
            },
            {
              content: '为什么我不可享',
              style: '1',
              url: 'Authentication',
            },
          ],
        },
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: false,
      isEnable: false,
      sortNum: 1,
    },
    {
      depositPayType: 1,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '取还车时在门店支付押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '1',
          stringObjs: [
            {
              content: '到店支付押金',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: true,
      sortNum: 3,
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        code: '1001',
        currenctDailyPrice: 399,
        currencyCode: '¥',
        currentTotalPrice: 798.0,
        description: '',
        size: '¥399×2天',
        sortNum: 1,
        subTitle: '',
        title: '租车基本费用',
      },
      {
        code: '1002',
        currenctDailyPrice: 85.0,
        currencyCode: '¥',
        currentTotalPrice: 170.0,
        description: '',
        sortNum: 100,
        title: '基础服务费',
      },
      {
        code: '1003',
        currenctDailyPrice: 25.0,
        currencyCode: '¥',
        currentTotalPrice: 25.0,
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        sortNum: 100,
        title: '车行手续费',
      },
    ],
    chargesSummary: {
      code: 'Summary',
      currencyCode: '¥',
      currentTotalPrice: 594.0,
      items: [
        {
          code: '',
          currenctDailyPrice: 0,
          currencyCode: '¥',
          currentTotalPrice: 0,
          description: '',
          subTitle: '',
          title: '在线支付',
        },
      ],
      notices: [],
      title: '全额',
      type: 103,
    },
    couponInfos: [
      {
        code: 'fpkvlbdegb',
        currencyCode: '¥',
        currentTotalPrice: 399,
        description:
          '本优惠券通过携程专车·租车-国内自驾租车频道，以预付方式（在线支付）预订枫叶出行-沃尔沃V60车型可享受首日免租金优惠； 优惠券自领取日起15天内使用有效，取、还车时间在2020年12月31日至2021年1月3日、2021年2月10日至2月18日不可使用，其余时间均可使用；不可与其他优惠叠加使用；优惠仅限租车费，不含手续费、基础服务费、优享服务费、异地还车费等费用，若租车费不满可优惠金额，则减免全部租车费后不继续减免； 有效期内每个用户仅限领取一张优惠券，同一设备号，手机号，uid均视为同一用户，每张订单限用一张优惠券，每张优惠券限用一次； 领取的优惠券不可累加，不可找零，不能兑换现金，仅限本人使用；',
        subTitle: '国内租车1日免租立减券',
        title: '优惠券',
      },
    ],
    equipmentInfos: [],
  },
  invoiceInfo: {
    description: '请在还车后向门店索取发票',
    title: '发票',
  },
  isSoldOut: false,
  marketingLabels: [
    {
      category: 3,
      code: '3',
      colorCode: '3',
      description: '券已减¥199/天。',
      labelCode: '3699',
      sortNum: 10000,
      title: '券已减¥199/天',
      type: 3,
    },
  ],
  orderPriceInfo: {
    currencyCode: 'CNY',
    dailyPrice: 399.0,
    deductAmount: 399,
    packagePrice: 993.0,
    payAmount: 594.0,
    priceCode: '73a4c3d139f14a19be301c1f7d1188a2',
    priceType: 1,
    priceVersion:
      'AXwADZse201vYydQPn88dY/lI6vINcXscegQuMtCAd2pqfET0Ac56gP8lFBlQwSFpgPB/oBOh+v5hi+1GpnmbAWXMge3igDtSK/i+dQtPtBmwpXf9KS1gCJDZGkODLK20DkCIsU4c5tcZsgr6tdEoNVA5yCDy9aBxXqeiseIwnwbUBU=',
    rentalPrice: 798.0,
    totalPrice: 594.0,
  },
  payModeInfos: [
    {
      choiceDesc: '已减¥399',
      choiceName: '在线支付，总价¥594',
      choiceNameAmount: '594',
      choiceNameCurrencyCode: '¥',
      choiceNameTitle: '在线支付，总价',
      currenctPriceInfo: {
        actualAmount: 594.0,
        currencyCode: 'CNY',
        totalPrice: 594.0,
      },
      description: '含车行手续费¥25，基础服务费¥170',
      discountItems: [
        {
          code: '',
          currencyCode: '¥',
          currentTotalPrice: 399,
          description: '',
          subTitle: '',
          title: '已减',
          type: 0,
        },
      ],
      isRecommended: true,
      isSelected: true,
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 594.0,
      },
      noNeedCreditCartDesc: '',
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      payType: 2,
      showPayMode: 2,
      sortNum: 20,
      subChoiceDesc: '',
      submitDesc: '',
      submitName: '去支付',
      tips: [],
      title: '在线支付',
    },
  ],
  positivePolicies: [
    {
      code: 'Activity',
      grade: 3,
      sortNum: 3,
      title: '该车辆已减¥399',
      type: 202,
    },
  ],
  promptInfos: [
    {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      subTitle: '驾驶员王顺暂不可享信用租',
      title: '抱歉，验证未通过',
      type: 10,
    },
  ],
  responseStatus: {
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8514002068955930628',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a3c83fc-446981-7092992',
      },
    ],
    timestamp: '2020-12-28 13:38:42',
  },
  riskFinal: 'F',
  trackInfo: {
    depositFreeType: 3,
    depositType: 10,
    refuseType: 'Risk_Refuse',
    riskFinal: 'F',
    riskOriginal: 'F',
    vendorPlatFrom: 10,
    zhimaResult: '0',
    zhimaResultDesc: 'UNAUTHORIZED',
  },
  zhimaInfo: {
    isSupportZhima: false,
  },
});

// T
const dataT = omitData({
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: 'b7f7dc61-5b63-4842-a641-11f1b60d16dc',
    extMap: {
      allCost: '388.0',
      start: '2020-06-29 10:35:22',
      end: '2020-06-29 10:35:22',
      key: '129618',
      pageName: 'Book',
      inputPrice: '976.0',
      outputPrice: '1181.0',
    },
    apiResCodes: [],
    hasResult: true,
  },
  ResponseStatus: {
    Timestamp: '/Date(1593398122483+0800)/',
    Ack: 'Success',
    Errors: [],
  },
  payModeInfos: [
    {
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      showPayMode: 2,
      title: '在线支付',
      description: '含车行手续费¥35，基础服务费¥170',
      isRecommended: true,
      currenctPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 1181,
        actualAmount: 1181,
      },
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 1181,
      },
      discountItems: [],
      submitName: '去支付',
      tips: ['取车前免费取消，计划有变也无需担心'],
      choiceName: '在线支付，总价¥1,181',
      choiceDesc: '',
      subChoiceDesc: '',
      sortNum: 20,
      isSelected: true,
      payType: 2,
      noNeedCreditCartDesc: '',
      choiceNameTitle: '在线支付，总价',
      choiceNameCurrencyCode: '¥',
      choiceNameAmount: '1181',
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        title: '租车基本费用',
        subTitle: '',
        description: '',
        code: '1001',
        size: '¥488×2天',
        currencyCode: '¥',
        currenctDailyPrice: 488,
        currentTotalPrice: 976,
        sortNum: 1,
      },
      {
        title: '基础服务费',
        description: '',
        code: '1002',
        currencyCode: '¥',
        currenctDailyPrice: 85,
        currentTotalPrice: 170,
        sortNum: 100,
      },
      {
        title: '车行手续费',
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        code: '1003',
        currencyCode: '¥',
        currenctDailyPrice: 35,
        currentTotalPrice: 35,
        sortNum: 100,
      },
    ],
    equipmentInfos: [],
    couponInfos: [],
    chargesSummary: {
      title: '全额',
      code: 'Summary',
      type: 103,
      currencyCode: '¥',
      currentTotalPrice: 1181,
      items: [
        {
          title: '在线支付',
          subTitle: '',
          description: '',
          code: '',
          currencyCode: '¥',
          currenctDailyPrice: 0,
          currentTotalPrice: 0,
        },
      ],
      notices: [],
    },
  },
  activityDetail: {
    title: '暂无促销活动',
    status: 0,
  },
  couponList: {
    usableCoupons: [],
    unusableCoupons: [],
    status: 0,
    title: '暂无可用优惠券',
  },
  cancelRuleInfo: {
    title: '取消政策',
    subTitle: '2020-07-22 10:30前可免费取消',
    description: '取车时间前可免费取消。',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2020-07-22 10:30前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2020-07-22 10:30前可免费取消',
          style: '6',
        },
      ],
    },
    code: 'FreeCancel',
    type: 300,
    showFree: true,
    items: [
      {
        title: '2020-07-22 10:30前',
        subTitle: '取车时间前',
        description: '免费取消',
        showFree: true,
      },
      {
        title: '2020-07-22 10:30后',
        subTitle: '取车时间后',
        description: '扣订单全额',
        showFree: false,
      },
    ],
    tableTitle: '取消时间|扣费标准',
  },
  confirmInfo: {
    title: '立即确认',
    description: '立即确认',
    code: 'ConfirmImmediately',
    type: 301,
    isInstantConfirm: true,
  },
  depositInfo: {
    title: '押金说明',
    subTitle: '如通过支付宝/微信支付押金，请在门店扫描携程二维码进行押金支付',
    items: [
      {
        title: '租车押金',
        description: '取车时冻结10000元租车押金，若无车损，还车时解冻',
        code: 'RentalDeposit',
        type: 305,
        currencyCode: '¥',
        currentTotalPrice: 10000,
        showFree: false,
        retractable: true,
        sortNum: 1,
      },
      {
        title: '违章押金',
        description: '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        code: 'IllegalDeposit',
        type: 306,
        currencyCode: '¥',
        currentTotalPrice: 2000,
        showFree: false,
        retractable: true,
        sortNum: 1,
      },
      {
        title: '支付方式',
        description: '支付宝·微信·信用卡',
        code: 'DepositPayMode',
        type: 308,
        currencyCode: '¥',
        currentTotalPrice: 0,
        showFree: false,
        retractable: false,
        sortNum: 1,
      },
    ],
    freezeDeposit: true,
    showCreditCard: true,
  },
  invoiceInfo: {
    title: '发票',
    description: '向门店索取发票',
  },
  positivePolicies: [
    {
      title: '您正在预订该地区最热门车辆',
      code: 'HotSale',
      type: 302,
      grade: 3,
      sortNum: 4,
    },
  ],
  orderPriceInfo: {
    currencyCode: 'CNY',
    packagePrice: 1181,
    dailyPrice: 488,
    totalPrice: 1181,
    deductAmount: 0,
    payAmount: 1181,
    priceType: 1,
    rentalPrice: 976,
  },
  zhimaInfo: {
    isSupportZhima: true,
    authStatus: 0,
    orderId: 'null',
    authedCountEqOne: false,
    sameDriver: false,
    promptInfo: {
      title: '',
      contents: [
        {
          stringObjs: [
            {
              content: '芝麻信用满650分',
              style: '1',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '享免押金租车',
              style: '2',
            },
          ],
        },
      ],
      button: {
        title: '立即认证',
      },
    },
    defaultInfo: {
      title: '抱歉，验证未通过',
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      button: {
        title: '知道了',
      },
    },
    authInfos: [],
  },
  addOn: {
    addOnServices: [
      {
        required: true,
        uniqueCode: '1001',
        name: '租车基本费用',
        unitPrice: 488,
        count: 1,
        cnyTotalPrice: 976,
        vendorServiceCode: '0',
      },
      {
        required: true,
        uniqueCode: '1002',
        equipcode: '',
        name: '基础服务费',
        desc: '',
        unitPrice: 85,
        count: 2,
        unit: '',
        cnyTotalPrice: 170,
        vendorServiceCode: '1',
      },
      {
        required: true,
        uniqueCode: '1003',
        equipcode: '',
        name: '车行手续费',
        desc: '',
        unitPrice: 35,
        count: 1,
        unit: '',
        cnyTotalPrice: 35,
        vendorServiceCode: '2',
      },
      {
        required: true,
        uniqueCode: '4003',
        name: '租车押金',
        count: 1,
        cnyTotalPrice: 10000,
        vendorServiceCode: '4003',
      },
      {
        required: true,
        uniqueCode: '4004',
        name: '违章押金',
        count: 1,
        cnyTotalPrice: 2000,
        vendorServiceCode: '4004',
      },
    ],
  },
  marketingLabels: [],
  depositLabel: {
    type: 1,
    title: '可享免押',
  },
  promptInfos: [
    {
      title: '抱歉，验证未通过',
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      type: 10,
      button: {
        title: '知道了',
      },
    },
  ],
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
  ],
});

// 更换驾驶员
const dataChangeDriver = omitData({
  activityDetail: {
    status: 0,
    title: '暂无促销活动',
  },
  addOn: {
    addOnServices: [
      {
        cnyTotalPrice: 800.0,
        count: 1,
        name: '租车基本费用',
        required: true,
        uniqueCode: '1001',
        unitPrice: 400,
        vendorServiceCode: '999',
      },
      {
        cnyTotalPrice: 120.0,
        count: 2,
        desc: '',
        equipcode: '',
        name: '基础服务费',
        required: true,
        uniqueCode: '1002',
        unit: '',
        unitPrice: 60.0,
        vendorServiceCode: '999',
      },
      {
        cnyTotalPrice: 35.0,
        count: 1,
        desc: '',
        equipcode: '',
        name: '车行手续费',
        required: true,
        uniqueCode: '1003',
        unit: '',
        unitPrice: 35.0,
        vendorServiceCode: '999',
      },
      {
        cnyTotalPrice: 5000.0,
        count: 1,
        name: '租车押金',
        required: true,
        uniqueCode: '4003',
        vendorServiceCode: '4003',
      },
      {
        cnyTotalPrice: 2000.0,
        count: 1,
        name: '违章押金',
        required: true,
        uniqueCode: '4004',
        vendorServiceCode: '4004',
      },
    ],
  },
  baseResponse: {
    apiResCodes: [],
    code: '200',
    extMap: {
      allCost: '136.0',
      start: '2020-07-03 20:31:44',
      end: '2020-07-03 20:31:44',
      key: '129618',
      pageName: 'Book',
      inputPrice: '800.0',
      outputPrice: '955.0',
    },
    hasResult: true,
    isSuccess: true,
    requestId: '10f6ebdd-376c-4610-b606-5b7678dfb5f9',
    returnMsg: 'success',
  },
  cancelRuleInfo: {
    code: 'FreeCancel',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2020-07-10 10:00前可免费取消，可以修改',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2020-07-10 10:00前可免费取消，可以修改',
          style: '6',
        },
      ],
    },
    description: '',
    items: [
      {
        description: '免费取消',
        showFree: true,
        subTitle: '取车时间前',
        title: '2020-07-10 10:00前',
      },
      {
        description: '扣订单全额',
        showFree: false,
        subTitle: '取车时间后',
        title: '2020-07-10 10:00后',
      },
    ],
    showFree: true,
    subTitle: '2020-07-10 10:00前可免费取消，可以修改',
    tableTitle: '取消时间|扣费标准',
    title: '取消政策',
    type: 300,
  },
  confirmInfo: {
    code: 'ConfirmImmediately',
    description: '立即确认',
    isInstantConfirm: true,
    title: '立即确认',
    type: 301,
  },
  couponList: {
    status: 0,
    title: '暂无可用优惠券',
    unusableCoupons: [],
    usableCoupons: [],
  },
  depositLabel: {
    title: '可享免押',
    type: 2,
  },
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
  ],
  driverTips: {
    contentStyle: '1',
    stringObjs: [
      {
        content: '您当前信用未达标，暂时无法享受免押服务。',
        style: '1',
      },
      {
        content: '您可尝试',
        style: '1',
      },
      {
        content: '更换驾驶员 >',
        style: '1',
        url: 'SelectPassenger',
      },
    ],
  },
  feeDetailInfo: {
    chargesInfos: [
      {
        code: '1001',
        currenctDailyPrice: 400,
        currencyCode: '¥',
        currentTotalPrice: 800.0,
        description: '',
        size: '¥400×2天',
        sortNum: 1,
        subTitle: '',
        title: '租车基本费用',
      },
      {
        code: '1002',
        currenctDailyPrice: 60.0,
        currencyCode: '¥',
        currentTotalPrice: 120.0,
        description: '',
        sortNum: 100,
        title: '基础服务费',
      },
      {
        code: '1003',
        currenctDailyPrice: 35.0,
        currencyCode: '¥',
        currentTotalPrice: 35.0,
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        sortNum: 100,
        title: '车行手续费',
      },
    ],
    chargesSummary: {
      code: 'Summary',
      currencyCode: '¥',
      currentTotalPrice: 955.0,
      items: [
        {
          code: '',
          currenctDailyPrice: 0,
          currencyCode: '¥',
          currentTotalPrice: 0,
          description: '',
          subTitle: '',
          title: '在线支付',
        },
      ],
      notices: [],
      title: '全额',
      type: 103,
    },
    couponInfos: [],
    equipmentInfos: [],
  },
  invoiceInfo: {
    description: '向门店索取发票',
    title: '发票',
  },
  marketingLabels: [],
  orderPriceInfo: {
    currencyCode: 'CNY',
    dailyPrice: 400.0,
    deductAmount: 0,
    packagePrice: 955.0,
    payAmount: 955.0,
    priceType: 1,
    rentalPrice: 800.0,
    totalPrice: 955.0,
  },
  payModeInfos: [
    {
      choiceDesc: '',
      choiceName: '在线预付，总价¥955',
      choiceNameAmount: '955',
      choiceNameCurrencyCode: '¥',
      choiceNameTitle: '在线预付，总价',
      currenctPriceInfo: {
        actualAmount: 955.0,
        currencyCode: 'CNY',
        totalPrice: 955.0,
      },
      description: '含车行手续费¥35，基础服务费¥120',
      discountItems: [],
      isRecommended: true,
      isSelected: true,
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 955.0,
      },
      noNeedCreditCartDesc: '',
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      payType: 2,
      showPayMode: 2,
      sortNum: 20,
      subChoiceDesc: '',
      submitName: '去支付',
      tips: ['取车前免费取消，计划有变也无需担心'],
      title: '在线支付',
    },
  ],
  positivePolicies: [
    {
      code: 'Underwear',
      grade: 2,
      sortNum: 7,
      title: '立即预订保证当前低价',
      type: 304,
    },
  ],
  promptInfos: [
    {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      title: '抱歉，验证未通过',
      type: 10,
    },
  ],
  responseStatus: {
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '2541600345252719442',
      },
      {
        id: 'RootMessageId',
        value: '100025527-0a068a24-442716-302584',
      },
    ],
    timestamp: '2020-07-03 20:31:44',
  },
  zhimaInfo: {
    authCount: 0,
    authInfos: [
      {
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '驾驶员测试可享免押服务。',
                style: '1',
              },
              {
                content: '您可尝试',
                style: '1',
              },
              {
                content: '更换驾驶员',
                style: '1',
                url: 'SelectPassenger',
              },
            ],
          },
        ],
        title: '免押金服务',
        type: 4,
      },
    ],
    authOrderCount: 0,
    authStatus: 1,
    authUrl: '',
    authedCountEqOne: false,
    certifyId: 'String',
    certifyUrl: 'String',
    defaultInfo: {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      title: '抱歉，验证未通过',
    },
    idNo: '352202199****257',
    isSupportZhima: true,
    orderId: '3082112077',
    promptInfo: {
      button: {
        title: '立即体验免押金租车',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '郭治华',
              style: '6',
            },
            {
              content: '的芝麻信用满足条件 可享',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '免租车＆违章押金',
              style: '5',
            },
            {
              content: '单免租车押金',
              style: '5',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '芝麻验证结果7天内有效',
              style: '4',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '免押金租车仅服务满足条件的驾驶员',
              style: '4',
            },
          ],
        },
      ],
      title: '验证通过',
    },
    requestId: 'alipay200703123144179bcgm',
    sameDriver: false,
    userName: '郭治华',
  },
});

// 芝麻 未授权
const dataZhimaUnauth = omitData({
  activityDetail: {
    status: 0,
    title: '暂无促销活动',
  },
  addOn: {
    addOnServices: [
      {
        cnyTotalPrice: 465,
        count: 1,
        name: '租车基本费用',
        required: true,
        uniqueCode: '1001',
        unitPrice: 66,
        vendorServiceCode: '0',
      },
      {
        cnyTotalPrice: 35,
        count: 1,
        desc: '',
        equipcode: '',
        name: '车行手续费',
        required: true,
        uniqueCode: '1003',
        unit: '',
        unitPrice: 35,
        vendorServiceCode: '2',
      },
      {
        cnyTotalPrice: 280,
        count: 7,
        desc: '',
        equipcode: '',
        name: '基础服务费',
        required: true,
        uniqueCode: '1002',
        unit: '',
        unitPrice: 40,
        vendorServiceCode: '1',
      },
      {
        cnyTotalPrice: 4000,
        count: 1,
        name: '租车押金',
        required: true,
        uniqueCode: '4003',
        vendorServiceCode: '4003',
      },
      {
        cnyTotalPrice: 3000,
        count: 1,
        name: '违章押金',
        required: true,
        uniqueCode: '4004',
        vendorServiceCode: '4004',
      },
    ],
  },
  baseResponse: {
    apiResCodes: [],
    code: '200',
    errorCode: '1001',
    extMap: {
      allCost: '29.0',
      start: '2020-12-25 19:09:51',
      end: '2020-12-25 19:09:52',
      key: '129618',
      pageName: 'Book',
      inputPrice: '780.0',
      outputPrice: '780.0',
      inputOffPrice: '780.0',
      outputOffPrice: '780.0',
    },
    hasResult: true,
    isSuccess: true,
    message: '',
    requestId: 'a124171b-be98-47ea-ba97-72221d0bd217',
    returnMsg: 'success',
  },
  cancelRuleInfo: {
    code: 'FreeCancel',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '6',
        },
      ],
    },
    description:
      '1、在预订取车时间前，可免费取消订单；2、过了预订取车时间取消订单，将收取订单总金额作违约金；',
    items: [
      {
        description: '免费取消',
        showFree: true,
        subTitle: '取车前0小时',
        title: '2021-01-06 12:00前',
      },
      {
        description: '扣订单全额',
        showFree: false,
        subTitle: '取车时间后',
        title: '2021-01-06 12:00后',
      },
    ],
    showFree: true,
    subTitle: '2021-01-06 12:00前可免费取消',
    tableTitle: '取消时间|扣费标准',
    title: '取消政策',
    type: 300,
  },
  confirmInfo: {
    code: 'ConfirmImmediately',
    description: '立即确认',
    isInstantConfirm: true,
    title: '立即确认',
    type: 301,
  },
  couponList: {
    status: 0,
    title: '暂无可用优惠券',
    unusableCoupons: [],
    usableCoupons: [],
  },
  depositInfo: {
    freezeDeposit: true,
    items: [
      {
        code: 'RentalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 4000,
        description: '取车时冻结4000元租车押金，若无车损，还车时解冻',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '租车押金',
        type: 305,
      },
      {
        code: 'IllegalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 3000,
        description: '还车时冻结3000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '违章押金',
        type: 306,
      },
      {
        code: 'DepositPayMode',
        currencyCode: '¥',
        currentTotalPrice: 0,
        description: '信用卡',
        retractable: false,
        showFree: false,
        sortNum: 1,
        title: '支付方式',
        type: 308,
      },
    ],
    showCreditCard: true,
    subTitle: '如果没有信用卡，可在列表页筛选免信用卡车辆',
    title: '押金说明',
  },
  depositLabel: {},
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
    {
      depositPayType: 1,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '取还车时在门店支付押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '1',
          stringObjs: [
            {
              content: '到店支付押金',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: true,
      sortNum: 3,
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        code: '1001',
        currenctDailyPrice: 66,
        currencyCode: '¥',
        currentTotalPrice: 465,
        description: '',
        size: '约¥66×7天',
        sortNum: 1,
        subTitle: '',
        title: '租车基本费用',
      },
      {
        code: '1003',
        currenctDailyPrice: 35,
        currencyCode: '¥',
        currentTotalPrice: 35,
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        sortNum: 100,
        title: '车行手续费',
      },
      {
        code: '1002',
        currenctDailyPrice: 40,
        currencyCode: '¥',
        currentTotalPrice: 280,
        description: '',
        sortNum: 100,
        title: '基础服务费',
      },
    ],
    chargesSummary: {
      code: 'Summary',
      currencyCode: '¥',
      currentTotalPrice: 780,
      items: [
        {
          code: '',
          currenctDailyPrice: 0,
          currencyCode: '¥',
          currentTotalPrice: 0,
          description: '',
          subTitle: '',
          title: '在线支付',
        },
      ],
      notices: [
        '租车基本费用日价价格来自 “租车基本费总价/整数租期” ，因存在小数点进制问题，会导致 “日价x天数≠租车基本费总价” 情况。租车基本费用请以总价为准。',
      ],
      title: '全额',
      type: 103,
    },
    couponInfos: [],
    equipmentInfos: [],
  },
  invoiceInfo: {
    description: '请在还车后向门店索取发票',
    title: '发票',
  },
  isSoldOut: false,
  marketingLabels: [],
  orderPriceInfo: {
    currencyCode: 'CNY',
    dailyPrice: 67,
    deductAmount: 0,
    packagePrice: 780,
    payAmount: 780,
    priceCode: '359e66e4c0fb41468f9df1978148e435',
    priceType: 1,
    priceVersion:
      'AXoADZse201vYydQPn88dY/lI6vINcXscegQuMtCAd2pqfET0Ac56gP8lFv8oABKdSFU/oBOh+v5hi+1GpnmbAWXMpqoNi4D1y0TQ8b9uydbO1Pf9KS1gCJDZGkODLK20DkCZ9HnpCe4cXEr6tdEoNVA5yCDy9aBxXqeYNW78brQQto=',
    rentalPrice: 465,
    totalPrice: 780,
  },
  payModeInfos: [
    {
      choiceDesc: '',
      choiceName: '在线支付，总价¥780',
      choiceNameAmount: '780',
      choiceNameCurrencyCode: '¥',
      choiceNameTitle: '在线支付，总价',
      currenctPriceInfo: {
        actualAmount: 780,
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      description: '含车行手续费¥35，基础服务费¥280',
      discountItems: [],
      isRecommended: true,
      isSelected: true,
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      noNeedCreditCartDesc: '',
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      payType: 2,
      showPayMode: 2,
      sortNum: 20,
      subChoiceDesc: '',
      submitDesc: '',
      submitName: '去支付',
      tips: [],
      title: '在线支付',
    },
  ],
  positivePolicies: [
    {
      code: 'Underwear',
      grade: 2,
      sortNum: 7,
      title: '立即预订保证当前低价',
      type: 304,
    },
  ],
  promptInfos: [
    {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      subTitle: '驾驶员李思宇暂不可享信用租',
      title: '抱歉，验证未通过',
      type: 10,
    },
  ],
  responseStatus: {
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8770510283761017050',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a3c8187-446915-1760909',
      },
    ],
    timestamp: '2020-12-25 19:09:52',
  },
  riskFinal: '0',
  trackInfo: {
    depositFreeType: 2,
    depositType: 10,
    riskFinal: '0',
    riskOriginal: '0',
    vendorPlatFrom: 0,
    zhimaResult: '0',
    zhimaResultDesc: 'UNAUTHORIZED',
  },
  zhimaInfo: {
    authCount: 0,
    authInfos: [
      {
        button: {
          title: '去授权',
        },
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '芝麻信用≥650分有机会可享',
                style: '1',
                // todo-dyy: 修改
                url: 'Authentication',
              },
            ],
          },
        ],
        title: '免押金服务',
        type: 4,
      },
    ],
    noteInfo: {
      title: '一嗨门店免押规则',
      contents: [
        {
          stringObjs: [
            {
              content: '一嗨门店车辆需要单独进行芝麻授权免押，免押标准如下：',
              style: '1',
            },
          ],
        },
      ],
      table: [
        {
          title: {
            stringObjs: [
              {
                content: '芝麻信用分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '可享减免额度',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '650分及以上',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥6000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '600分 - 649分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥3000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '550分 - 599分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥1000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '550以下',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '无法减免，需冻结押金全额',
                style: '1',
              },
            ],
          },
        },
      ],
      items: [
        {
          title: {
            stringObjs: [
              {
                content: '减免额度小于押金金额时，授权免押时需补足资金',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content:
                    '您当前所选车型押金为¥{0}元。若您的芝麻信用分对应的减免额度小于押金金额，需要您补足剩余金额至支付宝才可以享受免押',
                  style: '1',
                },
              ],
            },
            {
              stringObjs: [
                {
                  content: '补足的资金将会以消费的形式扣除',
                  style: '1',
                },
              ],
            },
          ],
        },
      ],
    },
    authOrderCount: 0,
    authStatus: 0,
    authUrl: '',
    authedCountEqOne: false,
    defaultInfo: {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      title: '抱歉，验证未通过',
    },
    idNo: '',
    isSupportZhima: true,
    orderId: '14030117125',
    promptInfo: {
      button: {
        title: '立即认证',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '芝麻信用满650分',
              style: '1',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '享免押金租车',
              style: '2',
            },
          ],
        },
      ],
      title: '',
    },
    requestId: 'alipay201225110952010r97g',
    sameDriver: true,
    userName: '',
  },
});

// 芝麻 未授权 重新授权
const dataZhimaReauth = omitData({
  activityDetail: {
    status: 0,
    title: '暂无促销活动',
  },
  addOn: {
    addOnServices: [
      {
        cnyTotalPrice: 465,
        count: 1,
        name: '租车基本费用',
        required: true,
        uniqueCode: '1001',
        unitPrice: 66,
        vendorServiceCode: '0',
      },
      {
        cnyTotalPrice: 35,
        count: 1,
        desc: '',
        equipcode: '',
        name: '车行手续费',
        required: true,
        uniqueCode: '1003',
        unit: '',
        unitPrice: 35,
        vendorServiceCode: '2',
      },
      {
        cnyTotalPrice: 280,
        count: 7,
        desc: '',
        equipcode: '',
        name: '基础服务费',
        required: true,
        uniqueCode: '1002',
        unit: '',
        unitPrice: 40,
        vendorServiceCode: '1',
      },
      {
        cnyTotalPrice: 4000,
        count: 1,
        name: '租车押金',
        required: true,
        uniqueCode: '4003',
        vendorServiceCode: '4003',
      },
      {
        cnyTotalPrice: 3000,
        count: 1,
        name: '违章押金',
        required: true,
        uniqueCode: '4004',
        vendorServiceCode: '4004',
      },
    ],
  },
  baseResponse: {
    apiResCodes: [],
    code: '200',
    errorCode: '1001',
    extMap: {
      allCost: '29.0',
      start: '2020-12-25 19:09:51',
      end: '2020-12-25 19:09:52',
      key: '129618',
      pageName: 'Book',
      inputPrice: '780.0',
      outputPrice: '780.0',
      inputOffPrice: '780.0',
      outputOffPrice: '780.0',
    },
    hasResult: true,
    isSuccess: true,
    message: '',
    requestId: 'a124171b-be98-47ea-ba97-72221d0bd217',
    returnMsg: 'success',
  },
  cancelRuleInfo: {
    code: 'FreeCancel',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '6',
        },
      ],
    },
    description:
      '1、在预订取车时间前，可免费取消订单；2、过了预订取车时间取消订单，将收取订单总金额作违约金；',
    items: [
      {
        description: '免费取消',
        showFree: true,
        subTitle: '取车前0小时',
        title: '2021-01-06 12:00前',
      },
      {
        description: '扣订单全额',
        showFree: false,
        subTitle: '取车时间后',
        title: '2021-01-06 12:00后',
      },
    ],
    showFree: true,
    subTitle: '2021-01-06 12:00前可免费取消',
    tableTitle: '取消时间|扣费标准',
    title: '取消政策',
    type: 300,
  },
  confirmInfo: {
    code: 'ConfirmImmediately',
    description: '立即确认',
    isInstantConfirm: true,
    title: '立即确认',
    type: 301,
  },
  couponList: {
    status: 0,
    title: '暂无可用优惠券',
    unusableCoupons: [],
    usableCoupons: [],
  },
  depositInfo: {
    freezeDeposit: true,
    items: [
      {
        code: 'RentalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 4000,
        description: '取车时冻结4000元租车押金，若无车损，还车时解冻',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '租车押金',
        type: 305,
      },
      {
        code: 'IllegalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 3000,
        description: '还车时冻结3000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '违章押金',
        type: 306,
      },
      {
        code: 'DepositPayMode',
        currencyCode: '¥',
        currentTotalPrice: 0,
        description: '信用卡',
        retractable: false,
        showFree: false,
        sortNum: 1,
        title: '支付方式',
        type: 308,
      },
    ],
    showCreditCard: true,
    subTitle: '如果没有信用卡，可在列表页筛选免信用卡车辆',
    title: '押金说明',
  },
  depositLabel: {},
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
    {
      depositPayType: 1,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '取还车时在门店支付押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '1',
          stringObjs: [
            {
              content: '到店支付押金',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: true,
      sortNum: 3,
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        code: '1001',
        currenctDailyPrice: 66,
        currencyCode: '¥',
        currentTotalPrice: 465,
        description: '',
        size: '约¥66×7天',
        sortNum: 1,
        subTitle: '',
        title: '租车基本费用',
      },
      {
        code: '1003',
        currenctDailyPrice: 35,
        currencyCode: '¥',
        currentTotalPrice: 35,
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        sortNum: 100,
        title: '车行手续费',
      },
      {
        code: '1002',
        currenctDailyPrice: 40,
        currencyCode: '¥',
        currentTotalPrice: 280,
        description: '',
        sortNum: 100,
        title: '基础服务费',
      },
    ],
    chargesSummary: {
      code: 'Summary',
      currencyCode: '¥',
      currentTotalPrice: 780,
      items: [
        {
          code: '',
          currenctDailyPrice: 0,
          currencyCode: '¥',
          currentTotalPrice: 0,
          description: '',
          subTitle: '',
          title: '在线支付',
        },
      ],
      notices: [
        '租车基本费用日价价格来自 “租车基本费总价/整数租期” ，因存在小数点进制问题，会导致 “日价x天数≠租车基本费总价” 情况。租车基本费用请以总价为准。',
      ],
      title: '全额',
      type: 103,
    },
    couponInfos: [],
    equipmentInfos: [],
  },
  invoiceInfo: {
    description: '请在还车后向门店索取发票',
    title: '发票',
  },
  isSoldOut: false,
  marketingLabels: [],
  orderPriceInfo: {
    currencyCode: 'CNY',
    dailyPrice: 67,
    deductAmount: 0,
    packagePrice: 780,
    payAmount: 780,
    priceCode: '359e66e4c0fb41468f9df1978148e435',
    priceType: 1,
    priceVersion:
      'AXoADZse201vYydQPn88dY/lI6vINcXscegQuMtCAd2pqfET0Ac56gP8lFv8oABKdSFU/oBOh+v5hi+1GpnmbAWXMpqoNi4D1y0TQ8b9uydbO1Pf9KS1gCJDZGkODLK20DkCZ9HnpCe4cXEr6tdEoNVA5yCDy9aBxXqeYNW78brQQto=',
    rentalPrice: 465,
    totalPrice: 780,
  },
  payModeInfos: [
    {
      choiceDesc: '',
      choiceName: '在线支付，总价¥780',
      choiceNameAmount: '780',
      choiceNameCurrencyCode: '¥',
      choiceNameTitle: '在线支付，总价',
      currenctPriceInfo: {
        actualAmount: 780,
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      description: '含车行手续费¥35，基础服务费¥280',
      discountItems: [],
      isRecommended: true,
      isSelected: true,
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      noNeedCreditCartDesc: '',
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      payType: 2,
      showPayMode: 2,
      sortNum: 20,
      subChoiceDesc: '',
      submitDesc: '',
      submitName: '去支付',
      tips: [],
      title: '在线支付',
    },
  ],
  positivePolicies: [
    {
      code: 'Underwear',
      grade: 2,
      sortNum: 7,
      title: '立即预订保证当前低价',
      type: 304,
    },
  ],
  promptInfos: [
    {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      subTitle: '驾驶员李思宇暂不可享信用租',
      title: '抱歉，验证未通过',
      type: 10,
    },
  ],
  responseStatus: {
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8770510283761017050',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a3c8187-446915-1760909',
      },
    ],
    timestamp: '2020-12-25 19:09:52',
  },
  riskFinal: '0',
  trackInfo: {
    depositFreeType: 2,
    depositType: 10,
    riskFinal: '0',
    riskOriginal: '0',
    vendorPlatFrom: 0,
    zhimaResult: '0',
    zhimaResultDesc: 'UNAUTHORIZED',
  },
  zhimaInfo: {
    authCount: 0,
    authInfos: [
      {
        button: {
          title: '去授权',
        },
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '芝麻信用≥650分有机会可享',
                style: '1',
                // todo-dyy: 修改
                url: 'Authentication',
              },
              {
                content: '已享一笔一嗨免押，本次需重新授权',
                style: '1',
              },
            ],
          },
        ],
        title: '免押金服务',
        type: 4,
      },
    ],
    noteInfo: {
      title: '一嗨门店免押规则',
      contents: [
        {
          stringObjs: [
            {
              content: '一嗨门店车辆需要单独进行芝麻授权免押，免押标准如下：',
              style: '1',
            },
          ],
        },
      ],
      table: [
        {
          title: {
            stringObjs: [
              {
                content: '芝麻信用分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '可享减免额度',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '650分及以上',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥6000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '600分 - 649分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥3000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '550分 - 599分',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '减免¥1000',
                style: '1',
              },
            ],
          },
        },
        {
          title: {
            stringObjs: [
              {
                content: '550以下',
                style: '1',
              },
            ],
          },
          desc: {
            stringObjs: [
              {
                content: '无法减免，需冻结押金全额',
                style: '1',
              },
            ],
          },
        },
      ],
      items: [
        {
          title: {
            stringObjs: [
              {
                content: '减免额度小于押金金额时，授权免押时需补足资金',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content:
                    '您当前所选车型押金为¥{0}元。若您的芝麻信用分对应的减免额度小于押金金额，需要您补足剩余金额至支付宝才可以享受免押',
                  style: '1',
                },
              ],
            },
            {
              stringObjs: [
                {
                  content: '补足的资金将会以消费的形式扣除',
                  style: '1',
                },
              ],
            },
          ],
        },
      ],
    },
    authOrderCount: 0,
    authStatus: 0,
    authUrl: '',
    authedCountEqOne: false,
    defaultInfo: {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      title: '抱歉，验证未通过',
    },
    idNo: '',
    isSupportZhima: true,
    orderId: '14030117125',
    promptInfo: {
      button: {
        title: '立即认证',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '芝麻信用满650分',
              style: '1',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '享免押金租车',
              style: '2',
            },
          ],
        },
      ],
      title: '',
    },
    requestId: 'alipay201225110952010r97g',
    sameDriver: true,
    userName: '',
  },
});

// 芝麻 未授权 额度不足
const dataZhimaNotEnough = omitData({
  activityDetail: {
    status: 0,
    title: '暂无促销活动',
  },
  addOn: {
    addOnServices: [
      {
        cnyTotalPrice: 465,
        count: 1,
        name: '租车基本费用',
        required: true,
        uniqueCode: '1001',
        unitPrice: 66,
        vendorServiceCode: '0',
      },
      {
        cnyTotalPrice: 35,
        count: 1,
        desc: '',
        equipcode: '',
        name: '车行手续费',
        required: true,
        uniqueCode: '1003',
        unit: '',
        unitPrice: 35,
        vendorServiceCode: '2',
      },
      {
        cnyTotalPrice: 280,
        count: 7,
        desc: '',
        equipcode: '',
        name: '基础服务费',
        required: true,
        uniqueCode: '1002',
        unit: '',
        unitPrice: 40,
        vendorServiceCode: '1',
      },
      {
        cnyTotalPrice: 4000,
        count: 1,
        name: '租车押金',
        required: true,
        uniqueCode: '4003',
        vendorServiceCode: '4003',
      },
      {
        cnyTotalPrice: 3000,
        count: 1,
        name: '违章押金',
        required: true,
        uniqueCode: '4004',
        vendorServiceCode: '4004',
      },
    ],
  },
  baseResponse: {
    apiResCodes: [],
    code: '200',
    errorCode: '1001',
    extMap: {
      allCost: '29.0',
      start: '2020-12-25 19:09:51',
      end: '2020-12-25 19:09:52',
      key: '129618',
      pageName: 'Book',
      inputPrice: '780.0',
      outputPrice: '780.0',
      inputOffPrice: '780.0',
      outputOffPrice: '780.0',
    },
    hasResult: true,
    isSuccess: true,
    message: '',
    requestId: 'a124171b-be98-47ea-ba97-72221d0bd217',
    returnMsg: 'success',
  },
  cancelRuleInfo: {
    code: 'FreeCancel',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2021-01-06 12:00前可免费取消',
          style: '6',
        },
      ],
    },
    description:
      '1、在预订取车时间前，可免费取消订单；2、过了预订取车时间取消订单，将收取订单总金额作违约金；',
    items: [
      {
        description: '免费取消',
        showFree: true,
        subTitle: '取车前0小时',
        title: '2021-01-06 12:00前',
      },
      {
        description: '扣订单全额',
        showFree: false,
        subTitle: '取车时间后',
        title: '2021-01-06 12:00后',
      },
    ],
    showFree: true,
    subTitle: '2021-01-06 12:00前可免费取消',
    tableTitle: '取消时间|扣费标准',
    title: '取消政策',
    type: 300,
  },
  confirmInfo: {
    code: 'ConfirmImmediately',
    description: '立即确认',
    isInstantConfirm: true,
    title: '立即确认',
    type: 301,
  },
  couponList: {
    status: 0,
    title: '暂无可用优惠券',
    unusableCoupons: [],
    usableCoupons: [],
  },
  depositInfo: {
    freezeDeposit: true,
    items: [
      {
        code: 'RentalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 4000,
        description: '取车时冻结4000元租车押金，若无车损，还车时解冻',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '租车押金',
        type: 305,
      },
      {
        code: 'IllegalDeposit',
        currencyCode: '¥',
        currentTotalPrice: 3000,
        description: '还车时冻结3000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        retractable: true,
        showFree: false,
        sortNum: 1,
        title: '违章押金',
        type: 306,
      },
      {
        code: 'DepositPayMode',
        currencyCode: '¥',
        currentTotalPrice: 0,
        description: '信用卡',
        retractable: false,
        showFree: false,
        sortNum: 1,
        title: '支付方式',
        type: 308,
      },
    ],
    showCreditCard: true,
    subTitle: '如果没有信用卡，可在列表页筛选免信用卡车辆',
    title: '押金说明',
  },
  depositLabel: {},
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
    {
      depositPayType: 1,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '取还车时在门店支付押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '1',
          stringObjs: [
            {
              content: '到店支付押金',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: true,
      sortNum: 3,
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        code: '1001',
        currenctDailyPrice: 66,
        currencyCode: '¥',
        currentTotalPrice: 465,
        description: '',
        size: '约¥66×7天',
        sortNum: 1,
        subTitle: '',
        title: '租车基本费用',
      },
      {
        code: '1003',
        currenctDailyPrice: 35,
        currencyCode: '¥',
        currentTotalPrice: 35,
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        sortNum: 100,
        title: '车行手续费',
      },
      {
        code: '1002',
        currenctDailyPrice: 40,
        currencyCode: '¥',
        currentTotalPrice: 280,
        description: '',
        sortNum: 100,
        title: '基础服务费',
      },
    ],
    chargesSummary: {
      code: 'Summary',
      currencyCode: '¥',
      currentTotalPrice: 780,
      items: [
        {
          code: '',
          currenctDailyPrice: 0,
          currencyCode: '¥',
          currentTotalPrice: 0,
          description: '',
          subTitle: '',
          title: '在线支付',
        },
      ],
      notices: [
        '租车基本费用日价价格来自 “租车基本费总价/整数租期” ，因存在小数点进制问题，会导致 “日价x天数≠租车基本费总价” 情况。租车基本费用请以总价为准。',
      ],
      title: '全额',
      type: 103,
    },
    couponInfos: [],
    equipmentInfos: [],
  },
  invoiceInfo: {
    description: '请在还车后向门店索取发票',
    title: '发票',
  },
  isSoldOut: false,
  marketingLabels: [],
  orderPriceInfo: {
    currencyCode: 'CNY',
    dailyPrice: 67,
    deductAmount: 0,
    packagePrice: 780,
    payAmount: 780,
    priceCode: '359e66e4c0fb41468f9df1978148e435',
    priceType: 1,
    priceVersion:
      'AXoADZse201vYydQPn88dY/lI6vINcXscegQuMtCAd2pqfET0Ac56gP8lFv8oABKdSFU/oBOh+v5hi+1GpnmbAWXMpqoNi4D1y0TQ8b9uydbO1Pf9KS1gCJDZGkODLK20DkCZ9HnpCe4cXEr6tdEoNVA5yCDy9aBxXqeYNW78brQQto=',
    rentalPrice: 465,
    totalPrice: 780,
  },
  payModeInfos: [
    {
      choiceDesc: '',
      choiceName: '在线支付，总价¥780',
      choiceNameAmount: '780',
      choiceNameCurrencyCode: '¥',
      choiceNameTitle: '在线支付，总价',
      currenctPriceInfo: {
        actualAmount: 780,
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      description: '含车行手续费¥35，基础服务费¥280',
      discountItems: [],
      isRecommended: true,
      isSelected: true,
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 780,
      },
      noNeedCreditCartDesc: '',
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      payType: 2,
      showPayMode: 2,
      sortNum: 20,
      subChoiceDesc: '',
      submitDesc: '',
      submitName: '去支付',
      tips: [],
      title: '在线支付',
    },
  ],
  positivePolicies: [
    {
      code: 'Underwear',
      grade: 2,
      sortNum: 7,
      title: '立即预订保证当前低价',
      type: 304,
    },
  ],
  promptInfos: [
    {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      subTitle: '驾驶员李思宇暂不可享信用租',
      title: '抱歉，验证未通过',
      type: 10,
    },
  ],
  responseStatus: {
    ack: 'Success',
    errors: [],
    extension: [
      {
        id: 'CLOGGING_TRACE_ID',
        value: '8770510283761017050',
      },
      {
        id: 'RootMessageId',
        value: '921822-0a3c8187-446915-1760909',
      },
    ],
    timestamp: '2020-12-25 19:09:52',
  },
  riskFinal: '0',
  trackInfo: {
    depositFreeType: 2,
    depositType: 10,
    riskFinal: '0',
    riskOriginal: '0',
    vendorPlatFrom: 0,
    zhimaResult: '0',
    zhimaResultDesc: 'UNAUTHORIZED',
  },
  zhimaInfo: {
    authCount: 0,
    authInfos: [
      {
        button: {
          title: '去授权',
        },
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '芝麻信用≥650分有机会可享',
                style: '1',
                // todo-dyy: 修改
                url: 'Authentication',
              },
              {
                content: '您当前仅授权¥2000免押额度，不足本单要求的¥6000额度，需重新授权',
                style: '2',
              },
              {
                content: '重新授权后上笔授权将自动解除',
                style: '2',
              },
            ],
          },
        ],
        title: '免押金服务',
        type: 4,
      },
    ],
    noteInfo: {
      title: '一嗨门店免押规则',
      contents: [
        {
          stringObjs: [
            {
              content: '一嗨门店需要单独进行芝麻授权免押，免押标准如下：',
              style: '1',
            },
          ],
        },
      ],
      table: [
        {
          title: {
            contentStyle: '2', // 灰色背景
            stringObjs: [
              {
                content: '芝麻信用分',
                style: '3', // 加粗
              },
            ],
          },
          desc: [
            {
              contentStyle: '2', // 灰色背景
              stringObjs: [
                {
                  content: '可享减免额度',
                  style: '3', // 加粗
                },
              ],
            },
          ],
        },
        {
          title: {
            stringObjs: [
              {
                content: '650分及以上',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content: '减免¥6000',
                  style: '4', // 绿色
                },
              ],
            },
          ],
        },
        {
          title: {
            stringObjs: [
              {
                content: '600分 - 649分',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content: '减免¥3000',
                  style: '4',
                },
              ],
            },
          ],
        },
        {
          title: {
            stringObjs: [
              {
                content: '550分 - 599分',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content: '减免¥1000',
                  style: '4',
                },
              ],
            },
          ],
        },
        {
          title: {
            stringObjs: [
              {
                content: '550以下',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content: '无法减免，需冻结押金全额',
                  style: '1',
                },
              ],
            },
          ],
        },
      ],
      items: [
        {
          title: {
            stringObjs: [
              {
                content: '减免额度小于押金金额时，授权免押时需补足资金',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content:
                    '若您预订成功，资金预计还车后60天芝麻授权解除的同时退还至您的支付账户',
                  style: '2',
                },
              ],
            },
            {
              stringObjs: [
                {
                  content:
                    '若您未预订或取消订单，资金会在授权1小时后芝麻授权解除的同时一起退还',
                  style: '2',
                },
              ],
            },
          ],
        },
        {
          title: {
            stringObjs: [
              {
                content:
                  '减免额度小于押金金额时，授权免押时需补足资金减免额度小于押金金额时，授权免押时需补足资金',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content:
                    '若您预订成功，资金预计还车后60天芝麻授权解除的同时退还至您的支付账户',
                  style: '2',
                },
              ],
            },
            {
              stringObjs: [
                {
                  content:
                    '若您未预订或取消订单，资金会在授权1小时后芝麻授权解除的同时一起退还',
                  style: '2',
                },
              ],
            },
          ],
        },
      ],
      button: {
        title: '去授权',
      },
    },
    authOrderCount: 0,
    authStatus: 0,
    authUrl: '',
    authedCountEqOne: false,
    defaultInfo: {
      button: {
        title: '知道了',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      title: '抱歉，验证未通过',
    },
    idNo: '',
    isSupportZhima: true,
    orderId: '14030117125',
    promptInfo: {
      button: {
        title: '立即认证',
      },
      contents: [
        {
          stringObjs: [
            {
              content: '芝麻信用满650分',
              style: '1',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '享免押金租车',
              style: '2',
            },
          ],
        },
      ],
      title: '',
    },
    requestId: 'alipay201225110952010r97g',
    sameDriver: true,
    userName: '',
  },
});

// 芝麻 已授权 资金冻结
const dataZhimaFundFreeze = omitData({
  baseResponse: {
    isSuccess: true,
    code: '200',
    returnMsg: 'success',
    requestId: 'b7f7dc61-5b63-4842-a641-11f1b60d16dc',
    extMap: {
      allCost: '388.0',
      start: '2020-06-29 10:35:22',
      end: '2020-06-29 10:35:22',
      key: '129618',
      pageName: 'Book',
      inputPrice: '976.0',
      outputPrice: '1181.0',
    },
    apiResCodes: [],
    hasResult: true,
  },
  ResponseStatus: {
    Timestamp: '/Date(1593398122483+0800)/',
    Ack: 'Success',
    Errors: [],
  },
  payModeInfos: [
    {
      packageId: 200,
      payMode: 2,
      payModeName: '在线支付',
      showPayMode: 2,
      title: '在线支付',
      description: '含车行手续费¥35，基础服务费¥170',
      isRecommended: true,
      currenctPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 1181,
        actualAmount: 1181,
      },
      localPriceInfo: {
        currencyCode: 'CNY',
        totalPrice: 1181,
      },
      discountItems: [],
      submitName: '去支付',
      tips: ['取车前免费取消，计划有变也无需担心'],
      choiceName: '在线支付，总价¥1,181',
      choiceDesc: '',
      subChoiceDesc: '',
      sortNum: 20,
      isSelected: true,
      payType: 2,
      noNeedCreditCartDesc: '',
      choiceNameTitle: '在线支付，总价',
      choiceNameCurrencyCode: '¥',
      choiceNameAmount: '1181',
    },
  ],
  feeDetailInfo: {
    chargesInfos: [
      {
        title: '租车基本费用',
        subTitle: '',
        description: '',
        code: '1001',
        size: '¥488×2天',
        currencyCode: '¥',
        currenctDailyPrice: 488,
        currentTotalPrice: 976,
        sortNum: 1,
      },
      {
        title: '基础服务费',
        description: '',
        code: '1002',
        currencyCode: '¥',
        currenctDailyPrice: 85,
        currentTotalPrice: 170,
        sortNum: 100,
      },
      {
        title: '车行手续费',
        description: '用于车辆清洁、车辆保养、单据制作、人员服务等',
        code: '1003',
        currencyCode: '¥',
        currenctDailyPrice: 35,
        currentTotalPrice: 35,
        sortNum: 100,
      },
    ],
    equipmentInfos: [],
    couponInfos: [],
    chargesSummary: {
      title: '全额',
      code: 'Summary',
      type: 103,
      currencyCode: '¥',
      currentTotalPrice: 1181,
      items: [
        {
          title: '在线支付',
          subTitle: '',
          description: '',
          code: '',
          currencyCode: '¥',
          currenctDailyPrice: 0,
          currentTotalPrice: 0,
        },
      ],
      notices: [],
    },
  },
  activityDetail: {
    title: '暂无促销活动',
    status: 0,
  },
  couponList: {
    usableCoupons: [],
    unusableCoupons: [],
    status: 0,
    title: '暂无可用优惠券',
  },
  cancelRuleInfo: {
    title: '取消政策',
    subTitle: '2020-07-22 10:30前可免费取消',
    description: '取车时间前可免费取消。',
    complexSubTitle: {
      contentStyle: '1',
      stringObjs: [
        {
          content: '2020-07-22 10:30前可免费取消',
          style: '5',
        },
        {
          content: '，',
          style: '6',
        },
        {
          content: '2020-07-22 10:30前可免费取消',
          style: '6',
        },
      ],
    },
    code: 'FreeCancel',
    type: 300,
    showFree: true,
    items: [
      {
        title: '2020-07-22 10:30前',
        subTitle: '取车时间前',
        description: '免费取消',
        showFree: true,
      },
      {
        title: '2020-07-22 10:30后',
        subTitle: '取车时间后',
        description: '扣订单全额',
        showFree: false,
      },
    ],
    tableTitle: '取消时间|扣费标准',
  },
  confirmInfo: {
    title: '立即确认',
    description: '立即确认',
    code: 'ConfirmImmediately',
    type: 301,
    isInstantConfirm: true,
  },
  depositInfo: {
    title: '押金说明',
    subTitle: '如通过支付宝/微信支付押金，请在门店扫描携程二维码进行押金支付',
    items: [
      {
        title: '租车押金',
        description: '取车时冻结10000元租车押金，若无车损，还车时解冻',
        code: 'RentalDeposit',
        type: 305,
        currencyCode: '¥',
        currentTotalPrice: 10000,
        showFree: false,
        retractable: true,
        sortNum: 1,
      },
      {
        title: '违章押金',
        description: '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
        code: 'IllegalDeposit',
        type: 306,
        currencyCode: '¥',
        currentTotalPrice: 2000,
        showFree: false,
        retractable: true,
        sortNum: 1,
      },
      {
        title: '支付方式',
        description: '支付宝·微信·信用卡',
        code: 'DepositPayMode',
        type: 308,
        currencyCode: '¥',
        currentTotalPrice: 0,
        showFree: false,
        retractable: false,
        sortNum: 1,
      },
    ],
    freezeDeposit: true,
    showCreditCard: true,
  },
  invoiceInfo: {
    title: '发票',
    description: '向门店索取发票',
  },
  positivePolicies: [
    {
      title: '您正在预订该地区最热门车辆',
      code: 'HotSale',
      type: 302,
      grade: 3,
      sortNum: 4,
    },
  ],
  orderPriceInfo: {
    currencyCode: 'CNY',
    packagePrice: 1181,
    dailyPrice: 488,
    totalPrice: 1181,
    deductAmount: 0,
    payAmount: 1181,
    priceType: 1,
    rentalPrice: 976,
  },
  zhimaInfo: {
    isSupportZhima: true,
    authStatus: 1,
    orderId: 'null',
    authedCountEqOne: false,
    sameDriver: false,
    promptInfo: {
      title: '',
      contents: [
        {
          stringObjs: [
            {
              content: '芝麻信用满650分',
              style: '1',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '享免押金租车',
              style: '2',
            },
          ],
        },
      ],
      button: {
        title: '立即认证',
      },
    },
    defaultInfo: {
      title: '抱歉，验证未通过',
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.芝麻信用未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过支付宝安全认证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询支付宝客服',
              style: '7',
            },
          ],
        },
      ],
      button: {
        title: '知道了',
      },
    },
    authInfos: [
      {
        button: {
          title: '',
        },
        contents: [
          {
            contentStyle: '1',
            stringObjs: [
              {
                content: '授权免押时，有补足资金',
                style: '1',
                url: 'EhiRefundIntroModal',
              },
            ],
          },
        ],
        title: '免押金服务',
        type: 4,
      },
    ],
    complementaryAmount: 100, // 补足资金
    noteInfo: {
      title: '补足资金退还说明',
      subTitle: '',
      items: [
        {
          title: {
            stringObjs: [
              {
                content: '您授权免押时，在支付宝中补足资金¥{0}',
                style: '1',
              },
            ],
          },
          desc: [
            {
              stringObjs: [
                {
                  content:
                    '若您预订成功，资金预计还车后60天芝麻授权解除的同时退还至您的支付账户',
                  style: '2',
                },
              ],
            },
            {
              stringObjs: [
                {
                  content:
                    '若您未预订或取消订单，资金会在授权1小时后芝麻授权解除的同时一起退还',
                  style: '2',
                },
              ],
            },
          ],
          note: {
            stringObjs: [
              {
                content: '若您需要立即退还资金，您可立即  ',
                style: '1',
              },
              {
                content: '解除芝麻授权',
                style: '1',
                url: 'CancelZhima',
              },
              {
                content: '，解除后您需重新授权才可享免押。',
                style: '1',
              },
            ],
          },
        },
      ],
    },
  },
  addOn: {
    addOnServices: [
      {
        required: true,
        uniqueCode: '1001',
        name: '租车基本费用',
        unitPrice: 488,
        count: 1,
        cnyTotalPrice: 976,
        vendorServiceCode: '0',
      },
      {
        required: true,
        uniqueCode: '1002',
        equipcode: '',
        name: '基础服务费',
        desc: '',
        unitPrice: 85,
        count: 2,
        unit: '',
        cnyTotalPrice: 170,
        vendorServiceCode: '1',
      },
      {
        required: true,
        uniqueCode: '1003',
        equipcode: '',
        name: '车行手续费',
        desc: '',
        unitPrice: 35,
        count: 1,
        unit: '',
        cnyTotalPrice: 35,
        vendorServiceCode: '2',
      },
      {
        required: true,
        uniqueCode: '4003',
        name: '租车押金',
        count: 1,
        cnyTotalPrice: 10000,
        vendorServiceCode: '4003',
      },
      {
        required: true,
        uniqueCode: '4004',
        name: '违章押金',
        count: 1,
        cnyTotalPrice: 2000,
        vendorServiceCode: '4004',
      },
    ],
  },
  marketingLabels: [],
  depositLabel: {
    type: 1,
    title: '可享免押',
  },
  promptInfos: [
    {
      title: '抱歉，验证未通过',
      contents: [
        {
          stringObjs: [
            {
              content: '可能原因：',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '1.程信分未达650分',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '2.未通过携程程信分安全验证',
              style: '3',
            },
          ],
        },
        {
          stringObjs: [
            {
              content: '详情请咨询携程金融-程信分客服',
              style: '7',
            },
          ],
        },
      ],
      type: 10,
      button: {
        title: '知道了',
      },
    },
  ],
  depositPayInfos: [
    {
      depositPayType: 5,
      depositTypeInfo: {
        desc: [
          {
            contentStyle: '3',
            stringObjs: [
              {
                content: '免¥4,000租车押金，免¥3,000违章押金',
                style: '1',
              },
            ],
          },
        ],
        title: {
          contentStyle: '2',
          stringObjs: [
            {
              content: '信用租',
              style: '2',
            },
            {
              content: '·押金双免',
              style: '1',
            },
          ],
        },
      },
      isCheck: false,
      isClickable: true,
      isEnable: false,
      sortNum: 1,
    },
  ],
});

// mock 芝麻阶梯规则
const depositInfo = {
  freezeDeposit: true,
  items: [
    {
      code: 'RentalDeposit',
      currencyCode: '¥',
      currentTotalPrice: 10000.0,
      description: '取车时冻结10000元租车押金，若无车损，还车时解冻',
      retractable: true,
      showFree: false,
      sortNum: 1,
      title: '租车押金',
      type: 305,
    },
    {
      code: 'IllegalDeposit',
      currencyCode: '¥',
      currentTotalPrice: 2000.0,
      description: '还车时冻结2000元违章押金，若无违章，银行会在还车后30天左右退还押金',
      retractable: true,
      showFree: false,
      sortNum: 1,
      title: '违章押金',
      type: 306,
    },
    {
      code: 'DepositPayMode',
      currencyCode: '¥',
      currentTotalPrice: 0,
      description: '支付宝·微信·信用卡',
      retractable: false,
      showFree: false,
      sortNum: 1,
      title: '支付方式',
      type: 308,
    },
  ],
  showCreditCard: true,
  // "subTitle": "如通过支付宝/微信支付押金，请在门店扫描携程二维码进行押金支付",
  // "title": "押金说明"
};

module.exports = {
  dataF,
  dataT,
  dataChangeDriver,
  dataZhimaUnauth,
  dataZhimaReauth,
  dataZhimaNotEnough,
  dataZhimaFundFreeze,
  depositInfo,
};
