/* eslint-disable import/no-extraneous-dependencies */
import React from 'react';
import {
  render,
  act,
  waitFor,
  RenderResult,
  fireEvent,
} from '@testing-library/react-native';
import { AppContext } from '../src/Util/Index';

export const TEST_TIMEOUT = 60 * 1000;

export const updateStore = state => {
  if (state) {
    const curState = {};
    const updateState = { ...curState };
    Object.keys(state).forEach(key => {
      updateState[key] = {
        ...updateState[key],
        ...state[key],
      };
    });
  }
};

const getCurrentTestId = () => {
  const testName = expect.getState().currentTestName;
  const expectTestId = testName.match(/{(\d+)}/)?.[1];
  return Number(expectTestId) || '';
};

export async function createPage(
  WrappedComponent,
  props = {},
  initialState = null,
): Promise<RenderResult> {
  // 基础默认 Props 透传
  const defaultProps = {
    navigation: {
      getCurrentRoutes: () => {},
    },
    ...props,
  };
  // @ts-ignore
  const APP_TYPE = props?.app?.urlQuery?.apptype;
  // @ts-ignore
  global.TESTING_APP_TYPE = APP_TYPE;

  AppContext.setCarEnv({ appType: APP_TYPE });

  const testId = getCurrentTestId();

  // 初始化Store
  // @ts-ignore
  if (!global.TA_CASE_ID_STORE[testId]) {
    // @ts-ignore
    global.TA_CASE_ID_STORE[testId] = true;
  }

  updateStore(initialState);

  // eslint-disable-next-line react/jsx-props-no-spreading
  const PageContainer = <WrappedComponent {...defaultProps} />;
  let Page: RenderResult;
  await act(async () => {
    await waitFor(
      () => {
        // 渲染页面
        Page = render(PageContainer);
      },
      {
        timeout: TEST_TIMEOUT,
      },
    );
  });
  // @ts-ignore
  return Page;
}

interface InterTestName {
  /**
   * 用例ID，支持单个与多个TestID形式，多个通过数组形式传入，第一个TestID 为主ID，用于接口数据请求的唯一ID
   */
  testId: number | number[];
  /**
   * testHub 用例对应标题
   */
  name: string;
}

export const createInterTestName = ({ testId, name }: InterTestName) => {
  let testNameString;
  if (Array.isArray(testId)) {
    const testIds = testId.map(id => `{${id}}`).join(' ');
    testNameString = `${testIds} ${name}`;
  } else {
    testNameString = `{${testId}} ${name}`;
  }
  return testNameString;
};

export const pressWithTestId = async (Page: RenderResult, testId: string) => {
  await act(async () => {
    const btn = await Page.findByTestId(testId);
    fireEvent.press(btn);
  });
};
