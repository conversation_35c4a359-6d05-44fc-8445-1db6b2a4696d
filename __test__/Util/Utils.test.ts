import { ENV_TYPE, DOMAIN_URL } from '../../src/Common/src/CarFetch';
import Utils from '../../src/Util/Utils';
import {
  APP_TYPE,
  APP_ID,
  BUSINESS_TYPE,
  COMPONENT_CHANNEL,
  RENTAL_GAP,
  BUS_TYPE,
  DROPOFF_INTERVAL,
  LIST_SHOW_VENDOR_NUM,
} from '../../src/Constants/Platform';
import AppContext from '../../src/Util/AppContext';
import dayjs from '../../src/Common/src/Dayjs/src';
import { BBK_IMAGE_PATH } from '../../src/Constants/ImageUrl';
import uuid from 'uuid';

describe('Utils', () => {
  let func: jest.Mock;
  let pasuse: Function;

  const OSDTypes = [
    APP_TYPE.OSD_C_APP,
    APP_TYPE.OSD_C_H5,
    APP_TYPE.OSD_Q_APP,
    APP_TYPE.OSD_ZUCHE_APP,
  ];

  const ISDTypes = [
    APP_TYPE.ISD_C_APP,
    APP_TYPE.ISD_C_H5,
    APP_TYPE.ISD_Q_APP,
    APP_TYPE.ISD_ZUCHE_APP,
  ];

  beforeEach(() => {
    global['__crn_appId'] = ''; // 清空赋值
    func = jest.fn();
    pasuse = time => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve('done');
        }, time);
      });
    };
  });

  test('getEnvType', async () => {
    const env = await Utils.getEnvType();
    expect(env).toEqual(ENV_TYPE.PROD);
  });


  test('getAppType', () => {
    expect(Utils.getAppType(APP_TYPE.ISD_C_APP)).toEqual(APP_TYPE.ISD_C_APP);
    expect(Utils.getAppType(APP_TYPE.OSD_C_APP)).toEqual(APP_TYPE.OSD_C_APP);
    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getAppType()).toEqual(APP_TYPE.OSD_T_APP);
  });

  test('getNewAppType', () => {
    expect(Utils.getNewAppType(true, APP_TYPE.ISD_C_APP)).toEqual(
      APP_TYPE.ISD_C_APP,
    );
  });


  test('getBusinessType', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.ISD);

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getBusinessType()).toEqual(BUSINESS_TYPE.ISD);
  });

  test('getCurrentEnv', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.ISD);

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getCurrentEnv()).toEqual(COMPONENT_CHANNEL.ISD);
  });

  test('getOtherEnv', () => {
    AppContext.setCarEnv({ appType: '' });

    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.OSD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getOtherEnv()).toEqual(COMPONENT_CHANNEL.OSD);
  });

  test('getParams', () => {
    const mockData = {
      rentalDate: {
        pickUp: {
          dateTime: '20200612100000',
        },
        dropOff: {
          dateTime: '20200614100000',
        },
      },
      rentalLocation: {
        pickUp: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        dropOff: {
          country: '中国',
          area: {
            lng: 110.343315,
            lat: 19.984078,
            id: '',
            name: '海口东站',
            type: '2',
          },

          cname: '海口',
          cid: '42',
        },
        isShowDropOff: false,
      },
    };
    const dataStr = encodeURIComponent(JSON.stringify(mockData));
    expect(Utils.getParams(dataStr)).toEqual(mockData);
  });

  test('isCtripOsd', () => {
    AppContext.setCarEnv({ appType: '' });

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
  });

  test('isCtripIsd', () => {
    AppContext.setCarEnv({ appType: '' });


    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
  });

  test('isCtripIsdByType', () => {
    ISDTypes.map(m => {
      expect(Utils.isCtripIsdByType(m)).toBeTruthy();
    });
  });


  test('dateTimeFormat', () => {
    expect(Utils.dateTimeFormat('20200612100000')).toEqual(
      '2020-06-12 10:00:00',
    );

    expect(Utils.dateTimeFormat('20220719155000')).toEqual(
      '2022-07-19 15:50:00',
    );

    expect(Utils.dateTimeFormat('20220719155')).toEqual('');
  });
  test('getRentalGap', () => {
    AppContext.setCarEnv({ appType: '' });

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalGap()).toEqual(RENTAL_GAP.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalGap()).toEqual(RENTAL_GAP.ISD);

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getRentalGap()).toEqual(RENTAL_GAP.ISD);
  });


  test('openUrlWithTicket', () => {
    // NONE:
  });


  test('openPDF', () => {
    // NONE:
  });


  test('isObjectArray', () => {
    // NONE:
  });

  test('objectArrayToArray', () => {
    // NONE:
  });

  test('cloneDeep', () => {
    const obj = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };
    expect(Utils.cloneDeep(obj)).not.toBe(obj);
  });

  test('getDropOffInterval', () => {
    AppContext.setCarEnv({ appType: '' });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.ISD);

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.ISD);

    global['__crn_appId'] = APP_ID.TRIP;
    expect(Utils.getDropOffInterval()).toEqual(DROPOFF_INTERVAL.ISD);
  });

  test('getRentalPickerOption', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalPickerOption()).toEqual({
      Dropoff_Interval: 0.25,
      Default_Interval_Days: 2,
    });

    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalPickerOption()).toEqual({
      Dropoff_Interval: 0.25,
      Default_Interval_Days: 2,
    });
  });

  test('isHigherAppVersion', () => {
    // NONE:
  });

  test('isWeb', () => {
    // NONE:
  });


  test('getQueryParams', () => {
    const url =
      'http://***********:5389/index.ios.bundle?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=3178239795';
    const url2 =
      'http://************:5389/index.ios.bundle?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=ser&fromurl=common&landingto=Home&apptype=ISD_C_APP&pcid=43&pcid=42';
    expect(Utils.getQueryParams(url)).toEqual({
      orderId: '3178239795',
      CRNModuleName: 'rn_car_app',
      CRNType: '1',
      apptype: 'ISD_C_APP',
      initialPage: 'OrderDetail',
    });

    expect(Utils.getQueryParams(url2)).toEqual({
      CRNModuleName: 'rn_car_app',
      CRNType: '1',
      apptype: 'ISD_C_APP',
      initialPage: 'Market',
      st: 'ser',
      fromurl: 'common',
      landingto: 'Home',
      pcid: '42',
    });

    expect(Utils.getQueryParams('')).toEqual({});
  });



  test('getHost', () => {
    expect(Utils.getHost('fat')).toEqual(
      'https://m.fat10668.qa.nt.ctripcorp.com',
    );
    expect(Utils.getHost('uat')).toEqual('https://m.uat.qa.nt.ctripcorp.com');
    expect(Utils.getHost('prd')).toEqual('https://m.ctrip.com');
    expect(Utils.getHost('')).toEqual('https://m.ctrip.com');
  });

  test('getRentalMaxMonth', () => {
    AppContext.setCarEnv({ appType: APP_TYPE.ISD_C_APP });
    expect(Utils.getRentalMaxMonth()).toEqual(6);

    AppContext.setCarEnv({ appType: APP_TYPE.OSD_C_APP });
    expect(Utils.getRentalMaxMonth()).toEqual(6);
  });


  test('colorLog', () => {
    // NONE:
  });

  test('isIsdCountry', () => {
    expect(Utils.isIsdCountry({ isDomestic: true })).toBeTruthy();
    expect(Utils.isIsdCountry({ isDomestic: false })).toBeFalsy();
    expect(Utils.isIsdCountry({ country: '中国' })).toBeTruthy();
    expect(Utils.isIsdCountry({ country: '美国' })).toBeFalsy();
    // TODO: ERROR CASE
    expect(
      Utils.isIsdCountry({ country: '美国', isDomestic: true }),
    ).toBeTruthy();
    expect(Utils.isIsdCountry({})).toBeFalsy();
  });

  test('compareProps', () => {
    const prev = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 19,
    };

    const next2 = {
      HelloWorld: 'helloworld',
      Year: 2022,
      MONTH: 'July',
      day: 0,
    };

    expect(Utils.compareProps(prev, next)).toBeTruthy();
    expect(Utils.compareProps(prev, next2)).toBeFalsy();
  });



  test('getPhoneList', () => {
    expect(Utils.getPhoneList('13817008899;0563-8175678，17755556666')).toEqual(
      ['13817008899', '0563-8175678', '17755556666'],
    );
  });

  test('wrapDebounce', () => {
    // TODO:
  });

  test('getComponentByChannel', () => {
    // TODO:
  });

  test('toNumber', () => {
    const trulyData = ['1.1', 0.9, '222', 1.234];
    const falsyData = ['hell', 'a.o.0'];
    trulyData.map(m => {
      expect(Utils.toNumber(m)).not.toBe(0);
    });

    falsyData.map(m => {
      expect(Utils.toNumber(m)).toEqual(0);
    });
  });

  test('compareVersion', () => {
    expect(Utils.compareVersion('8.11.0', '8.11')).toEqual(1);
    expect(Utils.compareVersion('8.11.0', '8.11.0')).toEqual(0);
    expect(Utils.compareVersion('8.11.0', '8.12')).toEqual(-1);
    expect(Utils.compareVersion('8.11.0', '')).toEqual(1);
    expect(Utils.compareVersion('', '')).toEqual(0);
  });

  test('compatImgUrlWithWebp', () => {
    const urlA = 'https://dimg04.c-ctrip.com/images/0zg6z120009hxl5q9CCF7.jpg';
    expect(Utils.compatImgUrlWithWebp(urlA)).toEqual(urlA + '_.webp');
  });



  test('getCurPageId', () => {
    expect(Utils.getCurPageId()).toEqual('');
  });

  test('getLogStrValue', () => {
    expect(Utils.getLogStrValue('a')).toEqual('0');
    expect(Utils.getLogStrValue('1')).toEqual('1');
    expect(Utils.getLogStrValue(0)).toEqual('0');
  });

  test('hasLocationPermissionPromise', async () => {
    expect(await Utils.hasLocationPermissionPromise()).toEqual({
      status: undefined,
      result: undefined,
    });
  });

  test('getByteLength', () => {
    expect(Utils.getByteLength('hello')).toEqual(5);
    expect(Utils.getByteLength('你好，中国')).toEqual(10);
    expect(Utils.getByteLength('hello 中国')).toEqual(10);
  });

  test('isBetween', () => {
    // NONE: dayjs库功能不用单测
  });

  test('dayJsUtc', () => {
    // NONE: dayjs库功能不用单测
  });

  test('getDayDiff', () => {
    // NONE: dayjs库功能不用单测
  });


  test('isShowHomeHeader', () => {
    expect(Utils.isShowHomeHeader()).toEqual('');
  });

  test('composeError2String', () => {
    expect(Utils.composeError2String(new Error('this is an error'))).toContain(
      'Error: this is an error',
    );
    expect(Utils.composeError2String({})).toEqual('{}');
    expect(Utils.composeError2String([])).toEqual('[]');
    expect(Utils.composeError2String(0)).toEqual('0');
    expect(Utils.composeError2String('')).toEqual('""');
  });
  test('getErrorMessage', () => {
    expect(Utils.getErrorMessage(new Error('this is an error'))).toEqual(
      `this is an error`,
    );
    expect(Utils.getErrorMessage({})).toEqual('{}');
  });

  test('isEqual', () => {
    expect(Utils.isEqual(1, 1)).toBeTruthy();
    expect(Utils.isEqual(1, 0)).toBeFalsy();
  });

  test('isFixedTelephone', () => {
    expect(Utils.isFixedTelephone('02161898298,45542')).toBeTruthy();
    expect(Utils.isFixedTelephone('61898298,45542')).toBeFalsy();
  });

  test('getGoodFixedTelephone', () => {
    expect(Utils.getGoodFixedTelephone('02161898298,45542')).toEqual(
      '02161898298,45542',
    );
    expect(Utils.getGoodFixedTelephone('61898298,45542')).toEqual(
      `61898298,45542`,
    );
  });

  test('should return true when pos and offsetY are equal', () => {
    const pos = 100;
    const offsetY = 100;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(true);
  });

  test('should return true when offsetY is within the POS_DIFF range of pos', () => {
    const pos = 100;
    const offsetY = 125;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(true);
  });

  test('should return false when offsetY is outside the POS_DIFF range of pos', () => {
    const pos = 100;
    const offsetY = 200;
    expect(Utils.isScrollReachPos(pos, offsetY)).toEqual(false);
  });


  it('should return the correct host for the "fat" environment', () => {
    // Mocking the input parameter
    const env = 'fat';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.fat10668.qa.nt.ctripcorp.com');
  });

  it('should return the correct host for the "uat" environment', () => {
    // Mocking the input parameter
    const env = 'uat';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.uat.qa.nt.ctripcorp.com');
  });

  it('should return the correct host for the "prd" environment', () => {
    // Mocking the input parameter
    const env = 'prd';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });

  it('should return the default host for unknown environments', () => {
    // Mocking the input parameter
    const env = 'unknown';

    // Calling the function to be tested
    const result = Utils.getUrlHost(env);

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });

  it('should return the default host when called without an environment parameter', () => {
    // Calling the function to be tested
    const result = Utils.getUrlHost();

    // Assertion
    expect(result).toBe('https://m.ctrip.com');
  });
});
