import ABKey, {
  GetABValueType,
} from '../../../src/Util/CarABTesting/ABKey';

describe('CarABTesting/ABKey', () => {
  test('Keys', () => {
    Object.keys(ABKey).map(key => {
      const result = Object.keys(AB<PERSON>ey[key]).sort();
      expect(Object.keys(ABKey[key]).sort()).toEqual(
        ['key', 'newVersionCode', 'defaultVersionCode', 'isCache'].sort(),
      );
    });
  });

  test('GetABValueType', () => {
    Object.keys(ABKey).forEach(objKey => {
      const result = GetABValueType(ABKey[objKey].key);
      expect(result).toEqual(ABKey[objKey]);
    });
  });
});
