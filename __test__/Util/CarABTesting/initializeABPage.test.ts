import {
  initializeABPage,
  initializeABHomePage,
} from '../../../src/Util/CarABTesting/InitializeAB';
import ABKey from '../../../src/Util/CarABTesting/ABKey';
import { Utils } from '../../../src/Util/Index';
jest.mock('../../../src/Util/CarABTesting/ABTesting', () => ({
  getABTestingInfoSync: keys => {
    const results: any = [];
    for (let i = 0; i < keys.length; i++) {
      results.push({
        ExpCode: keys[i]?.expCode,
        ExpVersion: 'B',
      });
    }
    return results;
  },
}));

describe('InitializeAB', () => {
  test('initializeABPage', () => {
    const keys = [];
    const isdKeys = [];
    const osdKeys = [{ expCode: ABKey.LicenseApprove.key }];
    const isdResult = initializeABPage({
      keys,
      isdKeys,
      osdKeys,
    });
    expect(isdResult).toEqual([]);
    const osdResult = initializeABPage({
      keys,
      isdKeys,
      osdKeys,
    });
    expect(osdResult).toEqual([]);
  });

  test('initializeABHomePage', () => {
    const isdResult = initializeABHomePage();
    expect(isdResult).toEqual([]);
    const osdResult = initializeABHomePage();
    expect(osdResult).toEqual([]);
  });

});
