import { ABTesting, Pay } from '@ctrip/crn';
import AppContext from '../../../src/Util/AppContext';
import getABTestingInfoSync from '../../../src/Util/CarABTesting/ABTesting';
import {
  getMockABTestingInfoSyncOutput,
  getMockMultiABTestingInfoSyncOutput,
} from '../../../src/Util/CarABTesting/MockData';

const ABKey = {

};

beforeEach(() => {
  jest.resetAllMocks();
  AppContext.setABTesting(null);
});

describe('CarABTesting/ABTesting', () => {
  test('getABTestingInfoSync/Multi', () => {
    jest
      .spyOn(ABTesting, 'getMultiABTestingInfoSync')
      .mockReturnValue(getMockMultiABTestingInfoSyncOutput);

    // 新KEY，走api获取实验值（第一次调用 getABTestingInfoSync）
    expect(ABTesting.getMultiABTestingInfoSync).toHaveBeenCalledTimes(0);
  });
});
