import { Util } from '@ctrip/crn';
import { isLogin, isLoginSync } from '../../src/Util/User';
import { AppContext } from '../../src/Util/Index';

// User.getUserInfoSync() debug模式下不可用

beforeEach(() => {
  Util.isInChromeDebug = true;
});

describe('isLogin', () => {
  test('isLogin 有UrlQuery', async () => {
    const urlQuery = {
      snapshotOid: 'snapshotOid',
    };
    jest.spyOn(AppContext, 'UrlQuery', 'get').mockReturnValue(urlQuery);
    const result = await isLogin();
    expect(result).toEqual(true);
  });

  test('isLogin 无UrlQuery 有UserInfo', async () => {
    const userInfo = {
      data: {
        data: {
          Address: '',
          Auth: '5570D4B993AE242B6734E1858A6004FDB9E0FC60CF15E20DDAC79632AC71F4CA',
          BindMobile: '16000000001',
          Birthday: '',
          Email: '',
          Experience: 8859,
          ExpiredTime: 1696735045733,
          Gender: 2,
          IsNonUser: false,
          IsRemember: 0,
          LoginErrMsg: '登录成功！',
          Mobile: '',
          NickName: '阿雷格里港徐达',
          PostCode: '',
          SAuth: '',
          UserID: 'M927076223',
          UserName: '',
          VipGrade: 30,
          VipGradeRemark: '钻石贵宾',
          headIcon:
            'https://dimg.fws.qa.nt.ctripcorp.com/images/0AS1k120009l60yh36D69_C_100_100.jpg',
        },
        timeby: '1',
        timeout: '2023/10/08',
      },
    };
    jest.spyOn(AppContext, 'UrlQuery', 'get').mockReturnValue({});
    AppContext.setUserInfo(userInfo);
    const result = await isLogin();
    expect(result).toEqual(true);
  });

  test('isLogin 无UrlQuery 无UserInfo 是debug模式', async () => {
    jest.spyOn(AppContext, 'UrlQuery', 'get').mockReturnValue({});
    jest.spyOn(AppContext, 'UserInfo', 'get').mockReturnValue({});
    const result = await isLogin();
    expect(result).toEqual(false);
  });

  test('isLogin 无UrlQuery 无UserInfo 不是debug模式 trip', async () => {
    jest.spyOn(AppContext, 'UrlQuery', 'get').mockReturnValue({});
    jest.spyOn(AppContext, 'UserInfo', 'get').mockReturnValue({});
    global['nativeCallSyncHook'] = true;
    const result = await isLogin();
    expect(result).toEqual(false);
  });
});

describe('isLoginSync', () => {
  test('isLoginSync 有Auth', () => {
    const userInfo = {
      Auth: '5570D4B993AE242B6734E1858A6004FDB9E0FC60CF15E20DDAC79632AC71F4CA',
    };
    jest.spyOn(AppContext, 'UserInfo', 'get').mockReturnValue(userInfo);
    expect(isLoginSync()).toEqual(true);
  });
  test('isLoginSync 是Debug模式无Auth', () => {
    jest.spyOn(AppContext, 'UserInfo', 'get').mockReturnValue({});
    expect(isLoginSync()).toEqual(false);
  });
  test('isLoginSync 不是Debug模式无Auth', () => {
    global['nativeCallSyncHook'] = true;
    jest.spyOn(AppContext, 'UserInfo', 'get').mockReturnValue({});
    expect(isLoginSync()).toEqual(false);
  });
});
