import { Business } from '@ctrip/crn';
import { getPreloadKey } from '../../src/Util/PreloadNextContainer';

describe('PreloadNextContainer', () => {
  test('getPreloadKey', () => {
    jest
      .spyOn(Business, 'preloadRunCRNApplication')
      .mockImplementation(() => 'key123');
    expect(getPreloadKey('123')).toEqual({ key: '', url: '123' });
    expect(getPreloadKey('')).toEqual({ key: '', url: '' });
    // jest.spyOn(Business, 'preloadRunCRNApplication').mockReturnValue(null);
    // expect(getPreloadKey('123')).toEqual('');
  });
});
