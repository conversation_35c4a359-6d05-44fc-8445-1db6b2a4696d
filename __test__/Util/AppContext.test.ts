import { AppContext } from '../../src/Util/Index';
import { AppContextType } from '../../src/Util/AppContext';
import initPageInstance from '../../src/Util/PageInstance';
import { Channel, Event } from '@ctrip/crn';
import { Platform } from '../../src/Constants/Index';

describe('AppContext get函数', () => {
  const baseContext: AppContextType = {
    ABTesting: {
      trace: '',
      datas: {},
      values: {
        newOrderDetail: false,
        newOsdOrderDetailVersion: '',
        newIsdOrderDetailVersion: '',
      },
      // 新版列表全部跳转新版详情页
      newDetail: true,
      listPageIsEffect: true,
      // 两车tab拆分实验
      homeTabInfo: {
        key: '',
        val: '',
      },
    },
    MarketInfo: {
      channelId: '',
      childChannelId: '',
      sId: Channel.sId || '',
      aId: Channel.alianceId || '',
      visitortraceId: '',
      awakeTime: '',
      vid: '',
    },
    Cache: {},
    CarEnv: { buildTime: '', appType: '' },
    SharkKeys: { lang: {}, messages: {} },
    LanguageInfo: {
      language: 'cn',
      locale: 'zh_cn',
      site: 'zh',
      currency: 'CNY',
    },
    UserInfo: {},
    UrlQuery: {},
    OriginUrlQuery: {},
    ContentUrlQuery: {},
    Url: '',
    // PageInstance: initPageInstance,
    // UserTrace: { queryVid: uuid(), queryRecommendCount: 0 },
    ListCacheInterval: { homeReady: new Date(), listCacheBuild: new Date() },
    UserFetchCacheId: { queryListCacheId: '' },
    PreFetchCache: {},
    // RouterLoader: { List: null },
    ISDIMurl: '',
    CallCityAreaPageInfo: {
      pageName: '',
    },
    HomeSearchFormRef: null,
    LocationDistance: {
      oneKm: 1000,
      pWalkDistance: 0,
      rWalkDistance: 0,
      pWalkInfo: '',
      pDriveInfo: '',
      rWalkInfo: '',
      rDriveInfo: '',
    },
    InsuranceRules: {
      insuranceRequestId: '',
      insuranceSelectedIds: [],
    },
    Booking: {
      traceCode: '',
    },
    hasLogBadTime: false,
    encryptUid: '',
    fromType: '',
    originOrderId: '',
    modifyVendorOrderCode: '',
    channelId: '',
    eid: '',
    isModifyOrderRebook: false,
    isHomeCombine: false,
    isHomeCombineEventSwitch: false,
    isListCombineEventSwitch: false,
    Flutter: {
      routers: [],
    },
    driveTime: 0,
    hasUpdatePickupOnDoorCode: false,
    pickCityTimezone: 8,
    hasPreLoadCommentList: false,
    queryProductsRequestId: '',
    hasUseHomeTabLocationData: false,
    platHomeModuleInfo: '',
  };
  test('ABTesting', () => {
    expect(AppContext.ABTesting).toEqual(baseContext.ABTesting);
  });
  test('MarketInfo', () => {
    expect(AppContext.MarketInfo).toEqual(baseContext.MarketInfo);
  });
  test('CarEnv', () => {
    expect(AppContext.CarEnv).toEqual(baseContext.CarEnv);
  });
  test('LanguageInfo', () => {
    expect(AppContext.LanguageInfo).toEqual(baseContext.LanguageInfo);
  });
  test('UserInfo', () => {
    expect(AppContext.UserInfo).toEqual(baseContext.UserInfo);
  });
  test('Url', () => {
    expect(AppContext.Url).toEqual(baseContext.Url);
  });
  test('UrlQuery', () => {
    expect(AppContext.UrlQuery).toEqual(baseContext.UrlQuery);
  });
  test('OriginUrlQuery', () => {
    expect(AppContext.OriginUrlQuery).toEqual(baseContext.OriginUrlQuery);
  });
  test('ContentUrlQuery', () => {
    expect(AppContext.ContentUrlQuery).toEqual(baseContext.ContentUrlQuery);
  });
  test('UserFetchCacheId', () => {
    expect(AppContext.UserFetchCacheId).toEqual(baseContext.UserFetchCacheId);
  });
  test('Env', () => {
    expect(AppContext.Env).toEqual(baseContext.env);
  });


  test('hasLogBadTime', () => {
    expect(AppContext.hasLogBadTime).toEqual(baseContext.hasLogBadTime);
  });
  test('encryptUid', () => {
    expect(AppContext.encryptUid).toEqual(baseContext.encryptUid);
  });
  test('fromType', () => {
    expect(AppContext.fromType).toEqual(baseContext.fromType);
  });
  test('originOrderId', () => {
    expect(AppContext.originOrderId).toEqual(baseContext.originOrderId);
  });


  test('channelId', () => {
    expect(AppContext.channelId).toEqual(baseContext.channelId);
  });

  test('isHomeCombine', () => {
    expect(AppContext.isHomeCombine).toEqual(baseContext.isHomeCombine);
  });
  test('isHomeCombineEventSwitch', () => {
    expect(AppContext.isHomeCombineEventSwitch).toEqual(
      baseContext.isHomeCombineEventSwitch,
    );
  });
  test('isListCombineEventSwitch', () => {
    expect(AppContext.isListCombineEventSwitch).toEqual(
      baseContext.isListCombineEventSwitch,
    );
  });
  test('isOp', () => {
    expect(AppContext.isOp).toEqual(false);
  });

  test('hasUpdatePickupOnDoorCode', () => {
    expect(AppContext.hasUpdatePickupOnDoorCode).toEqual(
      baseContext.hasUpdatePickupOnDoorCode,
    );
  });
  test('queryProductsRequestId', () => {
    expect(AppContext.queryProductsRequestId).toEqual(
      baseContext.queryProductsRequestId,
    );
  });
  test('homeTabAbInfo', () => {
    expect(AppContext.homeTabAbInfo).toEqual(
      baseContext.ABTesting?.homeTabInfo,
    );
  });
  test('hasUseHomeTabLocationData', () => {
    expect(AppContext.hasUseHomeTabLocationData).toEqual(
      baseContext.hasUseHomeTabLocationData,
    );
  });
  test('platHomeModuleInfo', () => {
    expect(AppContext.platHomeModuleInfo).toEqual(
      baseContext.platHomeModuleInfo,
    );
  });
});

describe('AppContext set函数', () => {
  const fakeUrl =
    'file:///var/mobile/Containers/Data/Application/063A4B87-27BF-4EDC-B6F7-702283C3324D/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&isHomeCombine=true&abVersion=E&cmapping_origin_url=L3JuX2lndF9haXJwb3J0L19jcm5fY29uZmlnP0NSTk1vZHVsZU5hbWU9cm5faWd0X2FpcnBvcnQmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPW5ld2luZGV4JmJpenR5cGU9MzImY2hhbm5lbGlkPTcyMjImcHR0eXBlPTE4JnB0Z3JvdXA9MTYmc3Rubm89QzM4NjImc3RuYWR0PTIwMjItMDgtMjYlMjAxNDoxMCZzdG5kZHQ9MjAyMi0wOC0yNiUyMDEzOjIwJnN0bmNkPTI1JnN0bm5tPSVFNSU4RCU5NyVFNCVCQSVBQyVFNyVBQiU5OSZjaWQ9MTImY25tPSVFNSU4RCU5NyVFNCVCQSVBQyZjdGlkPTEmY3RubT0lRTQlQjglQUQlRTUlOUIlQkQmZXh0X3B0dHlwZT0xNyZleHRfcHRncm91cD0xNiZleHRfc3Rubm89QzM4NjImZXh0X3N0bmFkdD0yMDIyLTA4LTI2JTIwMTQ6MTAmZXh0X3N0bmRkdD0yMDIyLTA4LTI2JTIwMTM6MjAmZXh0X3N0bmNkPTk5JmV4dF9zdG5ubT0lRTYlODklQUMlRTUlQjclOUUlRTclQUIlOTkmZXh0X2NpZD0xNSZleHRfY25tPSVFNiU4OSVBQyVFNSVCNyU5RSZleHRfY3RpZD0xJmV4dF9jdG5tPSVFNCVCOCVBRCVFNSU5QiVCRCZzZXJ2aWNlc291cmNlPTNBM0NFNkFFNzU0MDM2REZCRjU2MjAxMjY1QjREQkQ4NjE2MDZCQUNBRDEzNjdFMDJGMTlDNDA2Mzc4OEY2QTNDRDZENDgzODVDMkMxQzNDM0YwOUQ3NDU1RDg0QzMzNzdERjcxODE3MzdEOTRDNkM0NzQ4QjdEOTM4MUQ3NEVGNzQ4Mzk5MDVGMTkxOUFENg==&pageChannel=ctrip_home_page&abtforsubtabKey=220714_VAC_tab6&tabId=train&abtforsubtab=C&groupId=PlatHomeCar';
  const baseContext = {
    ABTesting: {
      trace: '220627_DSJT_xqysp|A',
      datas: {
        trace: '',
        datas: {},
        '220627_DSJT_xqysp': {
          ExpVersion: 'A',
          ExpCode: '220627_DSJT_xqysp',
        },
        values: {},
        listPageIsEffect: false,
      },
      values: {
        newOrderDetail: false,
        newOsdOrderDetailVersion: '',
        newIsdOrderDetailVersion: '',
      },
      newDetail: true,
      listPageIsEffect: true,
      homeTabInfo: {
        key: '',
        val: '',
      },
    },
    MarketInfo: {
      channelId: '17671',
      childChannelId: '',
      sId: Channel.sId || '',
      aId: Channel.alianceId || '',
      visitortraceId: '',
      awakeTime: '',
      vid: '',
    },
    Cache: {},
    CarEnv: { buildTime: '', appType: '' },
    SharkKeys: { lang: {}, messages: {} },
    LanguageInfo: {
      locale: 'zh_cn',
      site: 'cn',
      currency: 'CNY',
      language: 'cn',
      standardLocale: 'zh-CN',
    },
    UserInfo: {},
    UrlQuery: {},
    OriginUrlQuery: {},
    ContentUrlQuery: {},
    Url: fakeUrl,
    PageInstance: initPageInstance,
    ListCacheInterval: { homeReady: new Date(), listCacheBuild: new Date() },
    UserFetchCacheId: { queryListCacheId: '' },
    PreFetchCache: {},
    ISDIMurl: '',
    CallCityAreaPageInfo: {
      pageName: '',
    },
    HomeSearchFormRef: null,
    LocationDistance: {
      oneKm: 1000,
      pWalkDistance: 0,
      rWalkDistance: 0,
      pWalkInfo: '',
      pDriveInfo: '',
      rWalkInfo: '',
      rDriveInfo: '',
    },
    InsuranceRules: {
      insuranceRequestId: '',
      insuranceSelectedIds: ['1', '2'],
    },
    Booking: {
      traceCode: '',
    },
    hasLogBadTime: false,
    encryptUid: '',
    fromType: '',
    originOrderId: '',
    modifyVendorOrderCode: '',
    channelId: '',
    eid: '',
    isModifyOrderRebook: false,
    isHomeCombine: false,
    isHomeCombineEventSwitch: false,
    isListCombineEventSwitch: false,
    Flutter: {
      routers: [],
    },
    driveTime: 0,
    hasUpdatePickupOnDoorCode: false,
    pickCityTimezone: 8,
    hasPreLoadCommentList: false,
    queryProductsRequestId: '',
    hasUseHomeTabLocationData: false,
    platHomeModuleInfo: '',
  };
  const validQuery = Object.keys(fakeUrl ?? {}).reduce((pre, cur) => {
    if (Platform.FIELD_TO_KEEP.includes(cur.toLocaleLowerCase())) {
      pre[cur] = fakeUrl[cur];
    }
    return pre;
  }, {});
  test('setMarketInfo', () => {
    AppContext.setMarketInfo(baseContext.MarketInfo);
    expect(AppContext.MarketInfo).toEqual(baseContext.MarketInfo);
  });
  test('setABTesting', () => {
    const ABTesting = {
      trace: '',
      datas: {},
      '220627_DSJT_xqysp': {
        ExpVersion: 'A',
        ExpCode: '220627_DSJT_xqysp',
      },
      values: {},
      listPageIsEffect: false,
    };
    AppContext.setABTesting(ABTesting);
    expect(AppContext.ABTesting).toEqual(baseContext.ABTesting);
  });
  test('setABNewDetail', () => {
    AppContext.setABNewDetail(baseContext.ABTesting.newDetail);
    expect(AppContext.ABTesting.newDetail).toEqual(
      baseContext.ABTesting.newDetail,
    );
  });
  test('setUserInfo', () => {
    const userInfo = {
      data: {
        Address: '',
        Auth: '5570D4B993AE242B6734E1858A6004FDB9E0FC60CF15E20DDAC79632AC71F4CA',
        BindMobile: '***********',
        Birthday: '',
        Email: '',
        Experience: 8859,
        ExpiredTime: 1696735045733,
        Gender: 2,
        IsNonUser: false,
        IsRemember: 0,
        LoginErrMsg: '登录成功！',
        Mobile: '',
        NickName: '阿雷格里港徐达',
        PostCode: '',
        SAuth: '',
        UserID: 'M927076223',
        UserName: '',
        VipGrade: 30,
        VipGradeRemark: '钻石贵宾',
        headIcon:
          'https://dimg.fws.qa.nt.ctripcorp.com/images/0AS1k120009l60yh36D69_C_100_100.jpg',
      },
      timeby: '1',
      timeout: '2023/10/08',
    };
    AppContext.setUserInfo(userInfo);
    expect(AppContext.UserInfo).toEqual(userInfo.data);
  });
  test('setUrl', () => {
    AppContext.setUrl(baseContext.Url);
    expect(AppContext.Url).toEqual(baseContext.Url);
  });
  test('setUrlQuery', () => {
    AppContext.setUrlQuery(validQuery);
    expect(AppContext.UrlQuery).toEqual(baseContext.UrlQuery);
  });
  test('setOriginUrlQuery', () => {
    AppContext.setOriginUrlQuery(validQuery);
    expect(AppContext.OriginUrlQuery).toEqual(baseContext.OriginUrlQuery);
  });
  test('setContentUrlQuery', () => {
    AppContext.setContentUrlQuery(baseContext.ContentUrlQuery);
    expect(AppContext.ContentUrlQuery).toEqual(baseContext.ContentUrlQuery);
  });
  test('setLanguageInfo', () => {
    AppContext.setLanguageInfo(baseContext.LanguageInfo);
    expect(AppContext.LanguageInfo).toEqual(baseContext.LanguageInfo);
  });
  test('setPageInstance', () => {
    AppContext.setPageInstance(baseContext.PageInstance);
    expect(AppContext.PageInstance).toEqual(baseContext.PageInstance);
  });

  test('setEncryptUid', () => {
    AppContext.setEncryptUid(baseContext.encryptUid);
    expect(AppContext.encryptUid).toEqual(baseContext.encryptUid);
  });
  test('setFromType', () => {
    AppContext.setFromType(baseContext.fromType);
    expect(AppContext.fromType).toEqual(baseContext.fromType);
  });
  test('setOriginOrderId', () => {
    AppContext.setOriginOrderId(baseContext.originOrderId);
    expect(AppContext.originOrderId).toEqual(baseContext.originOrderId);
  });

  test('setHomeTabInfo', () => {
    const homeTabInfo = { key: '220714_VAC_tab6', val: '' };
    AppContext.setHomeTabInfo(homeTabInfo);
    expect(AppContext.ABTesting.homeTabInfo).toEqual(homeTabInfo);
  });
  test('setHasUseHomeTabLocationData', () => {
    AppContext.setHasUseHomeTabLocationData(true);
    expect(AppContext.hasUseHomeTabLocationData).toEqual(true);
  });
});
