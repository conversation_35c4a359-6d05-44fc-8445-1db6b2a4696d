import {
  checkUrl
} from '../../src/Util/UrlCheck';
import { CarLog, Utils } from '../../src/Util/Index';

jest.mock('../../src/Constants/Index', () => ({
  LogKeyDev: {
    c_car_trace_url_check: '217208',
  }
}));
describe('Util UrlCheck', () => {
  let logInfo = {};
  const fakeUrl1 = 'file:///var/mobile/Containers/Data/Application/33632BEF-0F7E-4C41-BB37-28F67C11AEC2/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=Area&apptype=ISD_C_APP&isHomeCombine=true&pageChannel=ctrip_home_page&abVersion=E&channelId=17671&aid=1066139&sid=1941542&backPageName=CtqHome20220824092735899&location={"country":"中国","cname":"大理市","cid":36,"area":{"id":"31258","name":"大理站","ename":"","lat":"25.589254","lng":"100.250006","type":2,"typename":"火车"}}';
  const fakeUrl2 = 'file:///var/mobile/Containers/Data/Application/702C3AF3-FD30-4EAC-AA1B-8CE177FB2BD3/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=list&apptype=ISD_C_APP&pageChannel=ctrip_home_page&abVersion=E&channelId=14495&aid=&sid=1&channelId=14495&aid=&sid=1&homeTabAbVersionInfo=220714_VAC_tab6|D&data=%7B%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220220827010000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220220829234500%22%7D%7D%2C%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22cid%22%3A559%2C%22cname%22%3A%22%E9%83%91%E5%B7%9E%22%2C%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22isDomestic%22%3Atrue%2C%22version%22%3A3%2C%22area%22%3A%7B%22id%22%3A%229042763%22%2C%22name%22%3A%22%E6%96%B0%E9%83%91%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2234.525890%22%2C%22lng%22%3A%22113.848812%22%2C%22type%22%3A1%2C%22typename%22%3A%22%E6%9C%BA%E5%9C%BA%22%7D%7D%2C%22dropOff%22%3A%7B%22cid%22%3A559%2C%22cname%22%3A%22%E9%83%91%E5%B7%9E%22%2C%22country%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22realcountry%22%3A%22%E4%B8%AD%E5%9B%BD%22%2C%22isDomestic%22%3Atrue%2C%22version%22%3A3%2C%22area%22%3A%7B%22id%22%3A%229042763%22%2C%22name%22%3A%22%E6%96%B0%E9%83%91%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2234.525890%22%2C%22lng%22%3A%22113.848812%22%2C%22type%22%3A1%2C%22typename%22%3A%22%E6%9C%BA%E5%9C%BA%22%7D%7D%2C%22isShowDropOff%22%3Afalse%7D%2C%22age%22%3A%2230~60%22%2C%22adultSelectNum%22%3A2%2C%22childSelectNum%22%3A0%7D&filters=%5B%5D&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl3 ='/data/user/10/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&apptype=ISD_C_APP&data={"rentalDate":{"pickUp":{"dateTime":"20220824210000"},"dropOff":{"dateTime":"20220831210000"}},"rentalLocation":{"pickUp":{"cid":30,"cname":"深圳","country":"中国","isDomestic":true,"area":{"id":"","name":"宝安国际机场","lat":22.62739,"lng":113.811928,"type":1}},"dropOff":{"cid":30,"cname":"深圳","country":"中国","isDomestic":true,"area":{"id":"","name":"宝安国际机场","lat":22.62739,"lng":113.811928,"type":1}},"isShowDropOff":false,"isNotShowDropOff":false},"isShowDropOff":false}&landingto=rebookhome&ctripOrderId=18751222162&vendorId=13037&vehicleId=186297&ctripVehicleCode=115170&storeCode=187953&encryptUid=&fromType=&originOrderId=18751222162&channelId=&eid=&env=undefined&modifyVendorOrderCode=3385637&originVendorId=13037&originalCouponCode=&passenger={"mobile":"","certificateList":[{"certificateType":"undefined"}]}';
  const fakeUrl4 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=list&fromPage=12345&apptype=ISD_C_APP&pageChannel=ctrip_home_page&abVersion=undefined&channelId=17671&aid22=&sid=1&channelId=17671&aid=&sid=1&homeTabAbVersionInfo=220714_VAC_tab6|B&data={"rentalDate":{"pickUp":{"dateTime":"20220824190000"},"dropOff":{"dateTime":"20220825190000"}},"rentalLocation":{"pickUp":{"cid":451,"cname":"沈阳","country":"中国","realcountry":"","province":"辽宁省","isDomestic":"true","area":{"id":"","locationName":"四季嘉园-二期","countryShortName":"CN","name":"四季嘉园-二期","ename":"","lat":"41.80170269577447","lng":"123.36954175820316","type":"","typename":""},"isFromPosition":false,"sortIndex":1},"dropOff":{"cid":451,"cname":"沈阳","country":"中国","realcountry":"","province":"辽宁省","isDomestic":true,"area":{"id":"","locationName":"四季嘉园-二期","countryShortName":"CN","name":"四季嘉园-二期","ename":"","lat":"41.80170269577447","lng":"123.36954175820316","type":"","typename":""},"isFromPosition":false,"sortIndex":1},"isShowDropOff":false,"historyCityId":451},"age":"30~60","adultSelectNum":2,"childSelectNum":0}&filters=[]&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl5 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&groupId=PlatHomeCar&fromurl=common&landingto=list&fromPage=12345&apptype=ISD_C_APP&pageChannel=ctrip_home_page&abVersion=undefined&channelId=17671&aid22=&sid=1&channelId=17671&aid=&sid=1&homeTabAbVersionInfo=220714_VAC_tab6|B&data={"rentalDate":{{"pickUp":{"dateTime":"20220824190000"},"dropOff":{"dateTime":"20220825190000"}},"rentalLocation":{"pickUp":{"cid":451,"cname":"沈阳","country":"中国","realcountry":"","province":"辽宁省","isDomestic":"false","area":{"id":"","locationName":"四季嘉园-二期","countryShortName":"CN","name":"四季嘉园-二期","ename":"","lat":"41.80170269577447","lng":"123.36954175820316","type":"","typename":""},"isFromPosition":false,"sortIndex":1},"dropOff":{"cid":451,"cname":"沈阳","country":"中国","realcountry":"","province":"辽宁省","isDomestic":true,"area":{"id":"","locationName":"四季嘉园-二期","countryShortName":"CN","name":"四季嘉园-二期","ename":"","lat":"41.80170269577447","lng":"123.36954175820316","type":"","typename":""},"isFromPosition":false,"sortIndex":1},"isShowDropOff":false,"historyCityId":451},"age":"30~60","adultSelectNum":2,"childSelectNum":0}&filters=[]&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl6 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=18751494918&from=CtqHome&dragBack=false';
  const fakeUrl7 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=18761165232&directOpen=undefined';
  const fakeUrl8 = 'file:///var/mobile/Containers/Data/Application/DD43D5AA-319C-4CD0-A567-B72D8D678272/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&landingto=list&st=ser&fromurl=common&apptype=ISD_C_APP&pcid=32&rcid=32&ptime=20220827100000&rtime=20220830100000&filters=["Brand_奥迪_奥迪A6L","Vendor_80395"]&channelId=235313&vehicleid=10043&isShowSearchConditionTip=true&&ctm_ref=vactang_page_26376';
  const fakeUrl9 = 'file:///var/mobile/Containers/Data/Application/063A4B87-27BF-4EDC-B6F7-702283C3324D/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&isHomeCombine=true&abVersion=E&cmapping_origin_url=L3JuX2lndF9haXJwb3J0L19jcm5fY29uZmlnP0NSTk1vZHVsZU5hbWU9cm5faWd0X2FpcnBvcnQmQ1JOVHlwZT0xJmluaXRpYWxQYWdlPW5ld2luZGV4JmJpenR5cGU9MzImY2hhbm5lbGlkPTcyMjImcHR0eXBlPTE4JnB0Z3JvdXA9MTYmc3Rubm89QzM4NjImc3RuYWR0PTIwMjItMDgtMjYlMjAxNDoxMCZzdG5kZHQ9MjAyMi0wOC0yNiUyMDEzOjIwJnN0bmNkPTI1JnN0bm5tPSVFNSU4RCU5NyVFNCVCQSVBQyVFNyVBQiU5OSZjaWQ9MTImY25tPSVFNSU4RCU5NyVFNCVCQSVBQyZjdGlkPTEmY3RubT0lRTQlQjglQUQlRTUlOUIlQkQmZXh0X3B0dHlwZT0xNyZleHRfcHRncm91cD0xNiZleHRfc3Rubm89QzM4NjImZXh0X3N0bmFkdD0yMDIyLTA4LTI2JTIwMTQ6MTAmZXh0X3N0bmRkdD0yMDIyLTA4LTI2JTIwMTM6MjAmZXh0X3N0bmNkPTk5JmV4dF9zdG5ubT0lRTYlODklQUMlRTUlQjclOUUlRTclQUIlOTkmZXh0X2NpZD0xNSZleHRfY25tPSVFNiU4OSVBQyVFNSVCNyU5RSZleHRfY3RpZD0xJmV4dF9jdG5tPSVFNCVCOCVBRCVFNSU5QiVCRCZzZXJ2aWNlc291cmNlPTNBM0NFNkFFNzU0MDM2REZCRjU2MjAxMjY1QjREQkQ4NjE2MDZCQUNBRDEzNjdFMDJGMTlDNDA2Mzc4OEY2QTNDRDZENDgzODVDMkMxQzNDM0YwOUQ3NDU1RDg0QzMzNzdERjcxODE3MzdEOTRDNkM0NzQ4QjdEOTM4MUQ3NEVGNzQ4Mzk5MDVGMTkxOUFENg==&pageChannel=ctrip_home_page&abtforsubtabKey=220714_VAC_tab6&tabId=train&abtforsubtab=C&groupId=PlatHomeCar';
  const fakeUrl10 = 'file:///var/mobile/Containers/Data/Application/55C42414-AEA4-4BDC-AB7A-509D7E5C8FB8/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&isHomeCombine=true&apptype=ISD_C_APP&st=ser&cmapping_origin_url=L3JuX2Nhcl9hcHAvX2Nybl9jb25maWc/Q1JOTW9kdWxlTmFtZT1ybl9jYXJfYXBwJkNSTlR5cGU9MSZpbml0aWFsUGFnZT1NYXJrZXQmc3Q9c2VyJmZyb211cmw9Y29tbW9uJmxhbmRpbmd0bz1Ib21lJmFwcHR5cGU9SVNEX0NfQVBQJmNoYW5uZWxJRD0xNzUyMCZwY2lkPTEmcGxuYW1lPQ==&tabId=car&channelID=17520&fromurl=common&pageChannel=ctrip_home_page&plname=&abVersion=E&pcid=1&abtforsubtab=C&groupId=PlatHomeCar&abtforsubtabKey=220714_VAC_tab6';
  const fakeUrl11 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=18761415726&from=CtqHome&dragBack=false';
  const fakeUrl12 = 'file:///var/mobile/Containers/Data/Application/40D941F2-29E1-45C0-913A-9C5A66A88AF7/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&apptype=ISD_C_APP&initialPage=OrderDetail&orderId=18761476130&directOpen=undefined';
  const fakeUrl13 = 'file:///var/mobile/Containers/Data/Application/66BD6B42-08CF-43F0-B5F1-65878A6FF71A/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=list&apptype=OSD_C_APP&pageChannel=ctrip_home_page&abVersion=undefined&channelId=17671&aid=3281918&sid=12525501&channelId=17671&aid=3281918&sid=12525501&homeTabAbVersionInfo=220714_VAC_tab6|B&data=%7B%22rentalDate%22%3A%7B%22pickUp%22%3A%7B%22dateTime%22%3A%2220220905100000%22%7D%2C%22dropOff%22%3A%7B%22dateTime%22%3A%2220220906100000%22%7D%7D%2C%22rentalLocation%22%3A%7B%22pickUp%22%3A%7B%22cid%22%3A633%2C%22cname%22%3A%22%E7%BA%BD%E7%BA%A6%22%2C%22country%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22realcountry%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22isDomestic%22%3Afalse%2C%22version%22%3A3%2C%22area%22%3A%7B%22id%22%3A%22LGA%22%2C%22name%22%3A%22%E6%8B%89%E7%93%9C%E8%BF%AA%E6%9C%BA%E5%9C%BA%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2240.776927%22%2C%22lng%22%3A%22-73.873966%22%2C%22type%22%3A1%2C%22typename%22%3A%22%E6%9C%BA%E5%9C%BA%22%7D%2C%22sortIndex%22%3A1%2C%22isFromPosition%22%3Afalse%7D%2C%22dropOff%22%3A%7B%22cid%22%3A347%2C%22cname%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%22%2C%22country%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22realcountry%22%3A%22%E7%BE%8E%E5%9B%BD%22%2C%22isDomestic%22%3Afalse%2C%22version%22%3A3%2C%22area%22%3A%7B%22id%22%3A%22LAX%22%2C%22name%22%3A%22%E6%B4%9B%E6%9D%89%E7%9F%B6%E5%9B%BD%E9%99%85%E6%9C%BA%E5%9C%BA%22%2C%22ename%22%3A%22%22%2C%22lat%22%3A%2233.941589%22%2C%22lng%22%3A%22-118.408530%22%2C%22type%22%3A1%2C%22typename%22%3A%22%E6%9C%BA%E5%9C%BA%22%7D%7D%2C%22isShowDropOff%22%3Atrue%2C%22isHasInfoFlow%22%3Atrue%7D%2C%22age%22%3A%2230~60%22%2C%22adultSelectNum%22%3A2%2C%22childSelectNum%22%3A0%7D&filters=%5B%5D&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl14 = '	/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=list&apptype=ISD_C_APP&pageChannel=ctrip_home_page&abVersion=E&channelId=17671&aid=&sid=1&channelId=17671&aid=&sid=1&homeTabAbVersionInfo=|&data={"rentalDate":{"pickUp":{"dateTime":"20220826190000"},"dropOff":{"dateTime":"20220828190000"}},"rentalLocation":{"pickUp":{"cid":375,"cname":"宁波","country":"中国","realcountry":"中国","isDomestic":true,"version":3,"area":{"id":"9094890","name":"栎社国际机场","ename":"","lat":"29.818281","lng":"121.466590","type":1,"typename":"机场"},"sortIndex":3,"isFromPosition":false},"dropOff":{"cid":375,"cname":"宁波","country":"中国","realcountry":"中国","isDomestic":true,"version":3,"area":{"id":"9094890","name":"栎社国际机场","ename":"","lat":"29.818281","lng":"121.466590","type":1,"typename":"机场"}},"isShowDropOff":false,"historyCityId":37},"age":"30~60","adultSelectNum":2,"childSelectNum":0}&filters=[]&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl15 = '/data/user/0/ctrip.android.view/app_ctripwebapp2/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Market&st=client&fromurl=common&landingto=list&apptype=ISD_C_APP&pageChannel=ctrip_home_page&abVersion=E&channelId=17671&aid=&sid=1&channelId=17671&aid=&sid=1&data={"rentalDate":{"pickUp":{"dateTime":"20220930190000"},"dropOff":{"dateTime":"20221007190000"}},"rentalLocation":{"pickUp":{"cid":206,"cname":"长沙","country":"中国","realcountry":"中国","isDomestic":true,"version":3,"area":{"id":"9231","name":"长沙南站","ename":"","lat":"28.147145","lng":"113.065108","type":2,"typename":"火车"}},"dropOff":{"cid":206,"cname":"长沙","country":"中国","realcountry":"中国","isDomestic":true,"version":3,"area":{"id":"9231","name":"长沙南站","ename":"","lat":"28.147145","lng":"113.065108","type":2,"typename":"火车"}},"isShowDropOff":false},"age":"30~60","adultSelectNum":2,"childSelectNum":0}&filters=[]&homeSearchFormRefEvent=searchFormFilter&isHomeCombine=true&queryListCacheId=';
  const fakeUrl16 = 'file:///var/mobile/Containers/Data/Application/4CD9B10B-4D6E-4A83-9320-3E23782CF9B3/Documents/share-v2-webapp_work/rn_car_app/_crn_config?CRNModuleName=rn_car_app&CRNType=1&initialPage=Location&apptype=ISD_C_APP&showType=present&cityId=43&pickType=pickup&pageType=City&cityName=三亚&areaId=&areaName=凤凰国际机场T1航站楼';
  const mockMap = [
    {
      fakeUrl: fakeUrl9,
      expected: {
        key: "217208",
        info: {
          isValidate: true,
          isMarket: false,
          isValueFieldError: 0,
          isObjectFieldError: 0,
          valueFieldError: [],
          objectFieldError: [],
          initialPage: undefined,
          landingto: undefined,
          isHomeCombine: 'true',
          fakeUrl: fakeUrl9,
          hasParams: false,
          hasData: false,
        },
      },
    },
    {
      fakeUrl: fakeUrl10,
      expected: {
        key: "217208",
        info: {
          isValidate: true,
          isMarket: false,
          isValueFieldError: 0,
          isObjectFieldError: 0,
          valueFieldError: [],
          objectFieldError: [],
          initialPage: undefined,
          landingto: undefined,
          isHomeCombine: 'true',
          fakeUrl: fakeUrl10,
          hasParams: false,
          hasData: false,
        },
      },
    },
  ];
  jest.spyOn(CarLog, "LogTraceDev").mockImplementation((info) => {
    logInfo = info;
  });
  test.each(mockMap)(
    '测试 checkUrl',
    async ({ fakeUrl, expected }) => {
      await checkUrl(fakeUrl);
      expect(logInfo).toEqual(expected);
    },
  );
});
