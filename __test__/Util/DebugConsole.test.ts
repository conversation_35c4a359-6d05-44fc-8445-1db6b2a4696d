import DebugConsole from '../../src/Util/DebugConsole';

describe('DebugConsole', () => {
  const debugConsole = new DebugConsole('testName', {
    style: {
      color: 'white',
      backgroundColor: 'green',
    },
    /* eslint-disable */
    disabled: false,
  });

  test('subjectLog', () => {
    console.log = jest.fn();
    debugConsole.subjectLog('test', '12345');
    expect(console.log).toHaveBeenCalledWith(
      `%c testName/TEST `,
      `background: green; color:white`,
      '12345',
    );
  });
});

describe('DebugConsole disabled', () => {
  const debugConsole = new DebugConsole('testName', {
    style: {
      color: 'white',
      backgroundColor: 'green',
    },
    /* eslint-disable */
    disabled: true,
  });

  test('subjectLog', () => {
    expect(debugConsole.subjectLog('test', '12345')).toEqual(undefined);
  });
});
