module.exports = {
  preset: 'react-native',
  globals: {
    _window: {},
    __DEV__: false,
  },
  setupFilesAfterEnv: ['./jest.setup.js'],
  roots: ["<rootDir>/__test__"],
  moduleNameMapper: {
    '@ctrip/rn_com_car/dist/(.*)(\\.d)$': '<rootDir>/src/Common/$1',
    '@ctrip/rn_com_car/dist/(.*)$': '<rootDir>/src/Common/$1',
  },
  transform: {
    "^.+\\.tsx?$": "esbuild-jest"
  },
  transformIgnorePatterns: [
    'node_modules/(?!(|react-native|react-redux|@react-native|redux-saga|pretty-format|@testing-library|@react-native-community|react-native-(.*)|@ctrip/(.*))/)'
  ],
  testEnvironment: "jsdom",
  testMatch: [
    '<rootDir>/__test__/**/*.test.(ts|tsx)'
  ],
  testPathIgnorePatterns: [
    '\\.snap$',
    '<rootDir>/node_modules/',
    '<rootDir>/build/',
    '<rootDir>/src/Common/',
    // '<rootDir>/__test__/Pages/',
    // '<rootDir>/__test__/Components/'
  ],
  coverageDirectory: "<rootDir>/coverage",
  cacheDirectory: '.jest/cache',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  collectCoverage: true,
  collectCoverageFrom: ['<rootDir>/src/**/*'],
  coverageReporters: ['json-summary', 'lcov', 'text-summary', 'html', 'clover'],
  reporters: [ "default", [ "jest-junit", { outputDirectory: "coverage/" }]],
  maxWorkers: 1,
  testTimeout: 60000,
};
