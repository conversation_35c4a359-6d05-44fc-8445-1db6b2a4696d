module.exports = {
  preset: 'react-native',
  globals: {
    _window: {},
    __DEV__: false,
  },
  setupFilesAfterEnv: ['./jest.setup.js'],
  roots: ["<rootDir>/__test__"],
  moduleNameMapper: {
    '@ctrip/rn_com_car/dist/(.*)(\\.d)$': '<rootDir>/src/Common/$1',
    '@ctrip/rn_com_car/dist/(.*)$': '<rootDir>/src/Common/$1',
  },
  transform: {
    "^.+\\.tsx?$": "esbuild-jest"
  },
  transformIgnorePatterns: [
    'node_modules/(?!(|react-native|react-redux|@react-native|redux-saga|pretty-format|@testing-library|@react-native-community|react-native-(.*)|@ctrip/(.*))/)'
  ],
  testEnvironment: "jsdom",
  testMatch: [
    '<rootDir>/__test__/Pages/**/*.test.tsx'
  ],
  testPathIgnorePatterns: [
    '\\.snap$',
    '<rootDir>/node_modules/',
    '<rootDir>/build/',
    '<rootDir>/src/Common/',
  ],
  coverageDirectory: "<rootDir>/coverage",
  cacheDirectory: '.jest/cache',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
  collectCoverage: true,
  collectCoverageFrom: [
    '<rootDir>/src/State/**/*.ts',
    '<rootDir>/src/Util/**/*.ts',
    '<rootDir>/src/Hooks/**/*.ts',
    '<rootDir>/src/Helpers/**/*.ts',
    '<rootDir>/src/Global/**/*.ts',
    '<rootDir>/src/Routers/**/*.ts',
    '<rootDir>/src/Components/LocationSelector/**/*.ts',
    '!<rootDir>/src/State/Debug/*.ts', // 开发使用不需要单测
    '!<rootDir>/src/State/Reviews/*.ts', // 页面已下线
    '!<rootDir>/src/Util/CarFetch.ts', // CarFetch 默认不做单测
    '!**/node_modules/**',
  ],
  coverageReporters: ['json-summary', 'lcov', 'text-summary', 'html', 'clover'],
  reporters: [ "default", [ "jest-junit", { outputDirectory: "coverage/" }]],
  maxWorkers: 1,
  testTimeout: 60000,
};
