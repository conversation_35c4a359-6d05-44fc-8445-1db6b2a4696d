<!DOCTYPE html>
<html>
  <style>
    #chart {
      position: absolute;
      top: 100px;
      left: 600px;
      bottom: 100px;
      right: 100px;
    }
    #datas{
      position: absolute;
      width: 500px;
      border-right: 1px;
      height: 100%;
      overflow: scroll;
      background: #fff3f3;
    }
    id, recievetime, pkgname, moduleid {
      display: none;
    }
    module {
      display: block;
    }
  </style>
  <body>
    <div id="chart"></div>
    <div id="datas">
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
    <script src="http://echarts.baidu.com/gallery/vendors/echarts/echarts.min.js"></script>
    <script>
      var url = 'http://crn.site.ctripcorp.com:5380/?deviceId=634af24fabb12805&appVersion=8.31.0&isDelete=0';

      var myChart = echarts.init(document.getElementById('chart'));
      myChart.showLoading();

      fetchData()

      function getRoot() {
        const $root = $('#datas instanceid').prevAll('module').filter((i, item) => {
          return $(item).children('modulename').text().includes('rn_car_app')
        })
        return $root
      }

      function fetchData(cb) {
        $.getJSON(url, (data) => {
          (data.xmls || []).forEach(item => {
            if (item.module.includes('rn_car_app')) {
              $('#datas').html(item.xml)
            }
          })
        })
      }

      $('#datas').bind('DOMSubtreeModified', function(){
        $('#datas').unbind()
        setTimeout(() => {
          const $root = getRoot()
          if ($root[0]) {
            fetchSuccess()
            $root.siblings().hide()
          }
        })
      });

      function fetchSuccess() {
        var getRandomColor = function(){
          return 'hsla('+ Math.floor(Math.random()*360) +',100%,50%,0.3)'; 
        }
        
        function getData ($parent, deep) {
          ++deep
          return  [...$parent.map((i, element) => {
            var $element = $(element)
            var $modulename = $element.children('modulename');
            var res = {
              name: $modulename.text(),
              value: +$element.children('time').text(),
            }
            if ($element.children('module')[0]) {
              res.children = getData($element.children('module'), deep)
            }
            if (res.name) {
              $modulename.prepend(document.createTextNode(new Array(deep).fill('-').join('')))
            } else {
              $element.children('time').hide()
            }
            if (deep === 1) {
              $element.css('background-color', getRandomColor())
            }
            return res
          })].filter(item => item.name);
        }
        
        var diskData = getData(getRoot().children('module'), 0)

        function colorMappingChange(value) {
            var levelOption = getLevelOption(value);
            chart.setOption({
                series: [{
                    levels: levelOption
                }]
            });
        }

        var formatUtil = echarts.format;

        function getLevelOption() {
          return [{
                    itemStyle: {
                        borderColor: '#555',
                        borderWidth: 4,
                        gapWidth: 4
                    }
                },
                {
                    colorSaturation: [0.3, 0.6],
                    itemStyle: {
                        borderColorSaturation: 0.7,
                        gapWidth: 2,
                        borderWidth: 2
                    }
                },
                {
                    colorSaturation: [0.3, 0.5],
                    itemStyle: {
                        borderColorSaturation: 0.6,
                        gapWidth: 1
                    }
                },
                {
                    colorSaturation: [0.3, 0.5]
                }]
        }

        myChart.hideLoading();
        myChart.setOption(option = {
          title: {
              text: 'CRN profile 分析',
              left: 'leafDepth'
          },
          tooltip: {},
          series: [{
              name: 'option',
              type: 'treemap',
              // visibleMin: 300,
              data: diskData,
              leafDepth: 1,
              levels: [
                  {
                      itemStyle: {
                          borderColor: '#555',
                          borderWidth: 4,
                          gapWidth: 4
                      }
                  },
                  {
                      colorSaturation: [0.3, 0.6],
                      itemStyle: {
                          borderColorSaturation: 0.7,
                          gapWidth: 2,
                          borderWidth: 2
                      }
                  },
                  {
                      colorSaturation: [0.3, 0.5],
                      itemStyle: {
                          borderColorSaturation: 0.6,
                          gapWidth: 1
                      }
                  },
                  {
                      colorSaturation: [0.3, 0.5]
                  }
              ]
          }]
        });
      }
    </script>
  </body>
</html>

