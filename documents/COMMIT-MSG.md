# 参考
[commitizen](https://github.com/commitizen/cz-cli#making-your-repo-commitizen-friendly)    
[commitlint](https://commitlint.js.org/#/guides-local-setup)

# 规范`commit-msg`步骤
- 添加commitizen 
- 配置husky
- 本地提交时检测输入是否符合要求
- merge master时自动升级版本并生成`change-log`
- 提交代码时 `git commit` 改为 `git cz`

# 安装依赖     

```sh
npm install commitizen --save-dev

npm install --save-dev @commitlint/{cli,config-conventional}

echo "module.exports = {extends: ['@commitlint/config-conventional']};" > commitlint.config.js

commitizen init cz-conventional-changelog --save-dev --save-exact

npm install husky --save-dev

npm install standard-version --save-dev
```

- package.json中添加配置
```json
"husky": {
    "hooks": {
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  }
```

# husky hooks 没生效
[husky-aren't-running](https://github.com/typicode/husky#hooks-arent-running)

- 检查 `husky` 版本，`"husky": "^4.2.5"`
- 本项目中修复，执行以下脚本
```sh
cp ./hooks/commit-msg ./.git/hooks/commit-msg
```


# todo
- hooks 指定分支检测
- changelog 指定版本生成 