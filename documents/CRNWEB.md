## 安装

```sh
npm install -g @ctrip/cw@6.1.1 --registry="http://registry.npm.release.ctripcorp.com/"
```

## 运行

```sh
npm run cw
```

## 本地访问环境控制

#### UAT

http://*************:9000/webapp/cw/rn_car_app/List.html?cw_env=uat

#### FAT

http://*************:9000/webapp/cw/rn_car_app/List.html?cw_env=fat

#### PRD

http://localhost:9000/webapp/cw/rn_car_app/List.html?cw_env=prd

## 本地登录态获取

1. 在 `UAT` 环境登录http://m.uat.qa.nt.ctripcorp.com/html5/
2. 配置 host

```
127.0.0.1 m.uat.qa.nt.ctripcorp.com
```

3. 域名换成 UAT 域名，访问本地页面：
   http://m.uat.qa.nt.ctripcorp.com:9000/webapp/cw/rn_car_app/OrderDetail.html?cw_env=uat&apptype=ISD_C_H5&orderId=3178087184

## 不支持

- 芝麻免押

## 需要注意

- 循环依赖杜绝
- ComponentBusiness 以真实路径引入
- <></>替换为<Fragment></Fragment>
- memo 优化放在组件最外层
- 文案不能写死
- react-native-deprecated-custom-components 使用 git 地址 crnweb 发布拉不到

## cw 文档

http://git.dev.sh.ctripcorp.com/fx-front-end/cw

## 支付文档

### h5 老支付

http://conf.ctripcorp.com/pages/viewpage.action?pageId=68780967

### h5 新支付

http://conf.ctripcorp.com/pages/viewpage.action?pageId=175012945

### 预授权 快捷 H5 文档

http://conf.ctripcorp.com/pages/viewpage.action?pageId=153228317
