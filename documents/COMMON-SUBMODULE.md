## 参考
[git submodule](https://git-scm.com/book/zh/v2/Git-%E5%B7%A5%E5%85%B7-%E5%AD%90%E6%A8%A1%E5%9D%97)    

## `rn_com_car` 公共子仓库开发方式
- 以下父仓库指`rn_car_app`， 子仓库指`rn_com_car`
- 父仓库下执行`git submodule init`（初始化子仓库）
- 父仓库下执行`git submodule update`（拉取子仓库更新)
- `cd src/Common` 进入子仓库
- 切换子仓库分支
   - **不需修改子仓库内容**，分支名为父仓库的`package.json`里`@ctrip/rn_com_car`地址中的分支，举例如`package.json`中地址为`"@ctrip/rn_com_car": "git+http://git.dev.sh.ctripcorp.com/open-source/rn_com_car.git#rel/8.37"`,则子仓库分支切换为`rel/8.37`
   - **需修改子仓库内容**，
      - 开发阶段
         - 基于子仓库`master`分支创建开发分支, 分支名和父仓库开发分支相同
         - 父仓库开发分支的`package.json`中`@ctrip/rn_com_car`地址修改为：
            ```json
            "@ctrip/rn_com_car": "git+http://git.dev.sh.ctripcorp.com/open-source/rn_com_car.git#开发分支名"
            ```
      - 测试阶段
         - 基于子仓库`master`分支创建release分支, 分支名和父仓库release分支相同，
         - 子仓库开发分支提交mr到release分支， 
         - 父仓库release分支的`package.json`中`@ctrip/rn_com_car`地址修改为：
            ```json
            "@ctrip/rn_com_car": "git+http://git.dev.sh.ctripcorp.com/open-source/rn_com_car.git#release分支名",
            ```
      - 发布阶段
         - 发布完毕后将子仓库release分支合到`master`分支
- 父仓库下执行 `clean`和`ts:watch`
- 子仓库下执行 `clean`和`ts:watch` （**必须按照先父仓库后子仓库顺序**）
- **修改提交时先提交子仓库， 再提交父仓库**

## git submodule注意点
- 当子仓库有提交的时候,没有push到远程仓库, 父级引用子仓库的commit更新,并提交到远程仓库,
当别人拉取代码的时候就会报出子仓库的commit不存在 fatal: reference isn’t a tree。

- **父仓库提交时需关注Common的diff变化,如diff的hash出现了`0c28b2b3365b7b4ad1f31d77a177c48342881093-dirty`这种有`-dirty`的情况，说明子仓库并未提交，请先提交子仓库再提交父仓库**

- **如果你仅仅引用了别人的子仓库的游离分支,然后在主仓库修改了子仓库的代码,之后使用git submodule update拉取了最新代码,那么你在子仓库游离分支做出的修改会被覆盖掉。**

- 父仓库拉取更新时，看到Common有diff变化是正常的，是因为子仓库内容被修改了，不应该撤销该diff

- 拉取子仓库更新既可`git submodule update`，也可以直接进入子仓库`git pull`

- 根据crn框架要求，公共子仓库中的文件**只可以修改、增加，不可以删除、更改目录结构，重命名**，如修改了文件结构，发布后会报错，**需找crn框架的人手动清空mapping文件方可发布**。

- 父仓库`package.json`中`@ctrip/rn_com_car`引用地址分支名/父仓库分支名/子仓库分支名, **三者需保持一致**

## 关于本地开发 `rn_com_car` 
因为线上实际使用的`rn_com_car`引用的是`node_modules/@ctrip/rn_com_car`，本地开发应该指向本地Common文件夹，利用node先在本地目录查找模块，找不到再去node_modules中查找的机制，`@ctrip/rn_com_car`在本地实际直接指向Common文件夹。

## 关于当前开发分支还在使用bbk组件的处理方法
- 父仓库`rn_car_app`执行 `npm run transRnComCar`
- bbk仓库中组件发生了修改并且未发布生产的处理方法:
  - 从`dev/wkw/moveCode`分支拉取出一个分支后merge当前开发分支
  - 执行`npm run build:createRnComCar` 生成 `rn_com_car` 文件夹
  - 将`rn_com_car`文件夹中内容拷贝到子仓库`rn_com_car`的src文件夹下，做覆盖替换
- 搜索项目中是否还有使用的`@ctrip/bbk`代码，没有说明bbk组件替换完毕
