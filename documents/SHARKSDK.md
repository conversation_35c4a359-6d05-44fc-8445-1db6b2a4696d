## Environment Vars

* `Shark` - configure the port number which the application runs on (5000 by default)

* `SHUTDOWN_GRACE` - Milliseconds to wait before force-closing connections (5000ms by default)

* `FAB_TEST_PLATFORM_HOST` - Megatron host (optional)

* `GW_SVC_PATH` - Megatron path, useful to point to megatron in another environment (optional)

* `GW_SVC_SESSION_PATH` - Megatron session path, useful to point to megatron in another environment (optional)