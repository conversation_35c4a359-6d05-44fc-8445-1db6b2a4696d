import '@testing-library/jest-native/extend-expect';
// react native mock 文件
// https://github.com/facebook/react-native/blob/main/packages/react-native/jest/setup.js

// Mock RN 宿主环境
const mockNavigator = {
  userAgent:
    // eslint-disable-next-line max-len
    'Mozilla/5.0 (iPhone; CPU iPhone OS 15_4_1 like Mac OS X)AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.4 Mobile/15E148 Safari/604.1',
};

global.navigator = mockNavigator;
global.window = global;
global.__TESTING_AUTOMATION__ = true; // Testing Automation 环境标识
global.showCalendar = false;
global.pushPageName = '';
global.setImmediate = global.setTimeout;
global.TA_CASE_ID_STORE = [];
const IGNORE_LOG = false;
/**
 * 忽略部分日志
 */
const ignoreError = [
  'was not wrapped in act',
  'unique "key" prop',
  'React.Fragment',
  'Cannot update a component',
  'children with the same key',
  'Failed prop type',
  'You called act',
  'Failed %s type: %s%s',
];
const ignoreWarn = [
  'DatePickerIOS',
  'useNativeDriver',
  'withTheme',
  '@react-native-clipboard/clipboard'
];
const ignoreLog = [
  'BFFRespose',
  'BFFRequest'
];

const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

console.error = function (...args) {
  if (IGNORE_LOG) return;
  const error = args[0];
  let message = typeof error === 'object' ? error?.message : error;
  if (!ignoreError.some(item => message?.includes?.(item))) {
    originalConsoleError.apply(console, args);
  }
}

console.warn = function (...args) {
  if (IGNORE_LOG) return;
  const message = args[0];
  if (!ignoreWarn.some(item => message?.includes?.(item))) {
    originalConsoleWarn.apply(console, args);
  }
}

console.log = function (...args) {
  if (IGNORE_LOG) return;
  const message = args[0];
  if (!ignoreLog.some(item => message?.includes?.(item))) {
    originalConsoleLog.apply(console, args);
  }
}

jest.mock('@ctrip/crn', () => {
  // eslint-disable-next-line no-underscore-dangle
  const __Storage__ = {};

  // eslint-disable-next-line global-require
  const React = require('react');
  const { View, Text, TouchableOpacity, ScrollView } = require('react-native');

  class Page extends React.Component {
    static registerPage = jest.fn();
    static enableNativeDragBack = jest.fn();
    static disableNativeDragBack = jest.fn();
    push(pageName, ...props) {
      console.log('page push:', pageName, props);
      global.pushPageName = pageName;
      return jest.fn();
    }
    pop() {
      console.log('page pop');
      global.popCount = (global.popCount || 0) + 1;
      return jest.fn();
    }
  }

  class CustomScrollView extends React.Component {
    STATE_REST = 1; //不显示
    STATE_PULL = 2; //拖拽中
    STATE_RELEASE = 3; //释放可触发刷新
    STATE_LOADING = 4; //正在刷新
    STATE_FINISH = 5; //刷新完成

    render() {
      const { children } = this.props;
      return <View>{children}</View>;
    }
  }

  class CRNMapViewV3 extends React.Component {
    constructor(props) {
      super(props);
    }
    calculateRouteETAForProxyView = async () => {
      return Promise.resolve({ etaTime: 261});
    };
    calculateRouteDistanceForProxyView = async () => {
      return Promise.resolve({ distance: 265});
    };
    addMapAnnotationForProxyView = (point) => {
    };
    showCalloutViewWithModelForProxyView = (point, icon) => {
      global.mapCard = point;
    };
    refreshCurrentUserLocationActionForProxyView = () => {
      global.mapLocationCount = (global.mapLocationCount || 0) + 1;
    }
    drawRouteForProxyViewV2 = () => {

    };
    zoomToFitMapAnnotationForProxyView = () => {

    };
    drawPolygonForProxyView = () => {

    };
   removeAllMapAnnotationsForProxyView = () => {

   };
    componentDidMount () {
      const { onMapReady = () => {} } = this.props;
      onMapReady();
      this.current = true;
    }
    render() {
      const { children } = this.props;
      return <View style={{height: 1040, width: 750, position: 'absolute'}}>{children}</View>;
    }
  }

  class VideoPlayerView extends React.Component {
    constructor(props) {
      super(props);
    }
    play = () => {
      global.playVideo = true;
    };
    render() {
      const { children } = this.props;
      return <View>{children}</View>;
    }
  }

  CustomScrollView.Header = View;
  CustomScrollView.Footer = View;

  return {
    ScrollView: ScrollView,
    Page,
    RefreshControl: View,
    CRNMapViewV3,
    CRNMapViewV3Biztype: {
      registerBiztype: jest.fn(),
    },
    FlowView: View,
    Application: {
      version: '8.53.0',
      getApplicationInfo: jest.fn(),
    },
    Device: {
      setStatusBarStyle() {},
      isiPhoneX: false,
    },
    lazyRequire() {},
    Location: {
      locate: () => {
        return Promise.resolve({});
      },
    },
    ABTesting: {
      getABTestingInfo: (name, a, callback) => {
        const cb = callback.bind(this, {
          ExpVersion: 'B',
          ExpCode: name,
        });
        setTimeout(cb, 0);
      },
      getABTestingInfoSync: () => {},
      getMultiABTestingInfoSync: () => {},
    },
    Log: {
      logCode: (key, obj) => {
        return {
          key,
          obj,
        };
      },
      logTrace: (key, obj) => {
        return {
          key,
          obj,
        };
      },
      logDevTrace: jest.fn(),
      logPromiseError: jest.fn(),
    },
    Share: {
      customShare: (/* obj, callback */) => {
        return '';
      },
    },
    User: {
      getUserInfo(callback) {
        setTimeout(() => {
          callback(0, null);
        });
      },
      userLogin(callback) {
        callback(
          {},
          {
            data: {
              Address: '',
              Auth: '5570D4B993AE242B6734E1858A6004FDB9E0FC60CF15E20DDAC79632AC71F4CA',
              BindMobile: '16000000001',
              Birthday: '',
              Email: '',
              Experience: 8859,
              ExpiredTime: 1696735045733,
              Gender: 2,
              IsNonUser: false,
              IsRemember: 0,
              LoginErrMsg: '登录成功！',
              Mobile: '',
              NickName: '阿雷格里港徐达',
              PostCode: '',
              SAuth: '',
              UserID: 'M927076223',
              UserName: '',
              VipGrade: 30,
              VipGradeRemark: '钻石贵宾',
              headIcon:
                'https://dimg.fws.qa.nt.ctripcorp.com/images/0AS1k120009l60yh36D69_C_100_100.jpg',
            },
          },
        );
      },
    },
    Storage: {
      // mock @ctrip/crn Storage Object
      load(params, callback) {
        return Promise.resolve([]);
      },
      loadSync() {
        return Promise.resolve([]);
      },
      loadPromise() {
        return Promise.resolve([]);
      },
      save(params) {
        if (typeof __Storage__[params.domain] === 'undefined') {
          __Storage__[params.domain] = {};
        }
        __Storage__[params.domain][params.key] = params.value;
      },
      remove(params) {
        delete __Storage__[params.domain][params.key];
      },
    },
    Event: {
      removeEventListener() {},
      addEventListener() {},
      sendEvent() {},
    },
    // Animated: require.requireActual('Animated'),
    fetch(url, params) {
      // Fake Fetch Method
      return new Promise((resolve, reject) => {
        if (params.body) {
          if (params.body.reject) {
            // 模拟Fetch失败的
            reject();
            return;
          }

          if (params.body.soa) {
            // 模拟SOA接口返回出错
            resolve({ ResponseStatus: { Ack: 'Error' } });
            return;
          }
        }

        resolve({});
      });
    },
    Platform: {
      OS: 'ios',
    },
    URL: {
      openURL: jest.fn(),
      openURLWithPreloadKey: jest.fn(),
      openURLWithCallback: jest.fn(),
    },
    Call: {
      makeCall: jest.fn(),
    },
    MapView: {},
    Bridge: {
      callNativeWithCallback: (pluginName, type, obj, callback) => {
        callback();
      },
    },
    Permission: {
      checkPermissions: (params, callback) => callback(),
    },
    LinearGradient: () => null,
    LottieAnimation: () => {},
    // Button: require.requireActual('Button'),
    Env: {
      getEnvType: callback => {
        const env = 'prd';
        // let env = Application.env || 'prd'
        // eslint-disable-next-line no-unused-expressions
        callback && callback(env);
        return env;
      },
      getNetworkType: () => {
        return 'WIFI';
      },
    },
    BlurView: ({children}) => (
      <View elementName="BlurView">{children}</View>
    ),
    Channel: {
      sId: '',
      alianceId: '',
    },
    Util: {
      isInChromeDebug: true,
      base64Encode: data => data,
    },
    Toast: {
      show: jest.fn(msg => console.log(`MockToast: ${msg}`)),
    },
    Loading: {
      show: jest.fn(),
      hide: jest.fn(),
      showMaskLoading: jest.fn(),
      hideMaskLoading: jest.fn(),
      showIconicLoading: jest.fn(),
      hideIconicLoading: jest.fn(),
    },
    PlatHome: {
      updateSettings: jest.fn(),
    },
    Business: {
      preloadCRNInstanceForBusiness: jest.fn(),
      preloadRunCRNApplication: jest.fn(),
    },
    LottieAnimation: () => (
      <View elementName="LottieAnimation">LottieAnimation</View>
    ),
    ViewPort: ({ children }) => <View elementName="ViewPort">{children}</View>,
    CRNText: ({ children, ...props }) => (
      <Text elementName="CRNText" {...props}>
        {children}
      </Text>
    ),
    LinearGradient: ({ children }) => (
      <View elementName="LinearGradient">{children}</View>
    ),
    SideToolBox: () => <View elementName="SideToolBox">SideToolBox</View>,
    CRNModal: ({ children, ...props }) => (
      <View elementName="CRNModal" {...props} testID={props.testID || 'car_testid_crn_modal'}>
        {children}
      </View>
    ),
    LoadingView: ({ children }) => (
      <View elementName="LoadingView">{children}</View>
    ),
    VideoPlayerView,
    CustomScrollView,
    Permission: {
      checkPermissions: (params, callback) => callback(),
    },
    Pay: {
      middlePay: jest.fn().mockImplementation((params, callback) => callback({
        status: 0
      })),
      regularPay2: (params, callback) => callback(),
    },
    SegmentedControl: ({ children }) => (
      <View elementName="SegmentedControl">{children}</View>
    ),
    LoadControl: View,
    FlowView: View,
    Calendar: {
      showCalendar: () => {
        global.showCalendar = true;
      },
    },
    PhotoBrowser: {
      showWithScrollCallback: (params, params2, index, meta, callback) => {
        global.photoData = params;
        callback();
      },
    },
    IBURoundCheckBox: TouchableOpacity,
    AlertDialog: {
      checkLocationPermissionTimeRestrictPromise: jest.fn().mockResolvedValue({
        granted: 0,
      }),
    }
  };
});

jest.mock('@ctrip/ttd-marketing-rn/lib/components/popCoupon', () => {
  const React = require('react');
  class HomePopCoupon extends React.Component {
    render() {
      return <></>
    };
  }
  return HomePopCoupon;
});

jest.mock('@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src', () => {
  return {
    show: jest.fn(msg => {
      console.log(`MockToast: ${msg}`);
    }),
  };
});

jest.mock('@ctrip/rn_com_car/dist/src/Hooks', () => ({
  __esModule: true,
  useWindowSizeChanged: jest.fn().mockImplementation(() => ({ width: 100, })),
  default: () => jest.fn(),
 }));


// Mock CarFetch的 getLottieJson 方法
jest.mock('./src/ComponentBusiness/LottieAnimation/src/Index', () => {
  const React = require('react');
  const { View } = require('react-native');
  const LottieAnimation = () => <View elementName="LottieAnimation"></View>;
  return LottieAnimation;
});


jest.mock('./src/Util/CarLog.ts', () => ({
  __esModule: true,
  default: {
    LogCode: jest.fn(),
    LogTrace: jest.fn(),
    LogTraceDev: jest.fn(),
    LogTraceDevNoState: jest.fn(),
    LogError: (expPoint, { error }) => {
      console.error(error);
    },
    logPromiseError: jest.fn(),
    LogExposure: jest.fn(),
    LogWithUbt: jest.fn(),
    LogCdata: jest.fn(),
    createExposureId: jest.fn(),
  },
}));


// 广告组件mock
jest.mock('@ctrip/crn-ext-adsdk', () => {
  const { View } = require('react-native');
  const React = require('react');
  return {
    AdSlider: () => <View></View>,
  };
});

jest.mock('./src/Util/User', () => {
  const origin = jest.requireActual('./src/Util/User');
  return {
    __esModule: true,
    ...origin,
  };
});

jest.mock('./src/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('./src/Util/CarABTesting/GetAB'),
}));



/**
 * Mock CarFetchBase
 *
 */
jest.mock('./src/Util/CarFetch/CarFetchBase', () => {
  const origin = jest.requireActual('./src/Util/CarFetch/CarFetchBase');
  const getMockData = jest.requireActual('./jest.mock.data');

  const getCurrentTestId = () => {
    const testName = expect.getState().currentTestName;
    const expectTestId = testName.match(/{(\d+)}/)?.[1];
    return Number(expectTestId) || '';
  };

  const fetchMock = async ({ path, sourceFrom = 'ISD_C_APP', testId = '' }) => {
    const url = `http://offline.fx.fws.qa.nt.ctripcorp.com/soa2/26037/mock?path=${path}&testid=${testId}&sourceFrom=${sourceFrom}&mock=all`;
    const queries = {
      path,
      sourceFrom,
      testId,
    };
    return new Promise(resolve => {
      const response = getMockData(queries);
      // console.warn(`🔔 Mock数据获取成功 ${url} ✅`);
      resolve(response);
    });
  };

  const getFetchMock = async (url, param) => {
    const testId = getCurrentTestId();
    let res = null;
    if (testId) {
      res = await fetchMock({
        path: url.split('?')[0],
        testId,
        sourceFrom: global.TESTING_APP_TYPE,
      });
    } else {
      res = Promise.resolve({
        baseResponse: { isSuccess: true },
      });
    }
    return res;
  };

  const getFetchWitchCatchMock = async (url, param) => {
    const testId = getCurrentTestId();
    let response = null;
    if (testId) {
      response = await fetchMock({
        path: url,
        testId,
      });
    } else {
      response = Promise.resolve({
        baseResponse: { isSuccess: true },
      });
    }
    return { response };
  }

  class FetchBase {
    getFetchObjectWithCatch = getFetchWitchCatchMock;
    getFetchObject = getFetchMock;
  }

  return {
    __esModule: true,
    ...origin,
    default: FetchBase,
  };
});

jest.mock('./src/Util/CarFetch/GraphqlFetch', () => {
  const getCurrentTestId = () => {
    const testName = expect.getState().currentTestName;
    if (!testName) return '';
    const expectTestId = testName.match(/{(\d+)}/)?.[1];
    return Number(expectTestId) || '';
  };

  const getMockData = jest.requireActual('./jest.mock.data');

  const graphqlFetch = async () => {
    const queries = {
      path: 'graphql',
      testId: getCurrentTestId(),
      mock: 'all',
    };
    return getMockData(queries);
  };

  const { setVariablesBaseRequest, graphqlPath, domains, getGraphqlServerUrl } =
    jest.requireActual('./src/Util/CarFetch/GraphqlFetch');

  return {
    graphqlFetch,
    getGraphqlServerUrl,
    domains,
    graphqlPath,
    setVariablesBaseRequest,
  };
});

jest.mock('./src/Util/CarABTesting/GetAB', () => ({
  __esModule: true,
  ...jest.requireActual('./src/Util/CarABTesting/GetAB'),
}));

jest.mock('react-native/Libraries/AppState/AppState', () => ({
  __esModule: true,
  removeEventListener: jest.fn(),
  addEventListener: jest.fn(),
}));
