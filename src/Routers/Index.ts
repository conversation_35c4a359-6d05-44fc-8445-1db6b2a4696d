import { lazyRequire } from '@ctrip/crn';
import { Channel } from '../Util/Index';

const Debug = lazyRequire('../Containers/DebugerContainer');
const Home = lazyRequire('../Containers/HomePageContainer');
const HomeHeader = lazyRequire('../Containers/HomeHeaderPageContainer');

// const ListRecommondTest = lazyRequire('../Pages/List/ListRecommondTest');
// const EasylifePrivilege = lazyRequire('../Containers/EasylifePrivilegeContainer');
// const CrossPlacePage = lazyRequire('../Containers/CrossPlaceContainer');
// const OrderReviews = lazyRequire('../Containers/OrderReviewsContainer');
// const OrderDetailReplenishPay = lazyRequire('../Containers/OrderDetailReplenishPayModal');
// const QunarSubWay = lazyRequire('../Containers/QunarSubwayContainer');// @qunar-change
export interface PageRouteType {
  component: any;
  name: string;
  isInitialPage?: boolean;
  sceneConfig?: any;
}

const Pages: Array<PageRouteType> = [
  {
    component: Home,
    name: 'Home',
    isInitialPage: true,
  },
  {
    component: HomeHeader,
    name: Channel.getPageId().HomeHeader.EN,
  },
  {
    component: Debug,
    name: 'Debug',
  },
];

export default Pages;
