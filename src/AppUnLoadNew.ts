import _ from 'lodash';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { AppContext, CarStorage, Utils } from './Util/Index';
import { StorageKey, AgeConfig } from './Constants/Index';
import { driverAgeAndNumberStore, environmentStore } from './State/Index';
import { packageListBaseReqParam } from './State/List';

/* eslint-disable */
/**
 * 设置列表页请求参数缓存
 * -> 当前环境的数据，取locationAndDate reducer的最新数据
 * -> 其它环境的数据，取env对应的数据
 */
export const setListParamHistory = () => {
  try {
    // 1、设置当前环境
    const curEnvParam = packageListBaseReqParam();
    const listParamHistory = {};
    listParamHistory[AppContext.CarEnv.appType] = curEnvParam;

    // 2、设置其它环境(当前环境是国内,其它环境就是境外;当前环境是境外,其它环境就是国内)

    const otherEnv = Utils.getOtherEnv();
    /* eslint-disable */
    const otherEnvAppType = Utils.getNewAppType(false);
    const environmentState = environmentStore.getState();
    const otherLocationAndDate = _.get(
      environmentState[otherEnv],
      'LocationAndDate',
    );
    if (otherLocationAndDate) {
      const {
        age: curAge,
        adultNumbers,
        childNumbers,
      } = driverAgeAndNumberStore.getState();
      const otherEnvParam = {
        age:
          curAge === AgeConfig.DEFAULT_AGE.getVal()
            ? AgeConfig.DEFAULT_AGE.min
            : Number(curAge),
        adultNumbers,
        childrenNumbers: childNumbers,
        pickupPointInfo: {
          cityId: _.get(otherLocationAndDate, 'rentalLocation.pickUp.cid'),
          date: dayjs(
            _.get(otherLocationAndDate, 'rentalDate.pickUp.dateTime'),
          ).format('YYYY-MM-DD HH:mm:ss'),
          locationCode: _.get(
            otherLocationAndDate,
            'rentalLocation.pickUp.area.id',
          ),
          locationName: _.get(
            otherLocationAndDate,
            'rentalLocation.pickUp.area.name',
          ),
          locationType: _.get(
            otherLocationAndDate,
            'rentalLocation.pickUp.area.type',
          ),
          poi: {
            latitude: Number(
              _.get(otherLocationAndDate, 'rentalLocation.pickUp.area.lat'),
            ),
            longitude: Number(
              _.get(otherLocationAndDate, 'rentalLocation.pickUp.area.lng'),
            ),
            radius: 0,
          },
          pickupOnDoor: 0, // 国内免费上门取车 1 true 0 false
          dropOffOnDoor: 0, // 国内免费送车上门 1 true 0 false
        },
        returnPointInfo: {
          cityId: _.get(otherLocationAndDate, 'rentalLocation.dropOff.cid'),
          date: dayjs(
            _.get(otherLocationAndDate, 'rentalDate.dropOff.dateTime'),
          ).format('YYYY-MM-DD HH:mm:ss'),
          locationCode: _.get(
            otherLocationAndDate,
            'rentalLocation.dropOff.area.id',
          ),
          locationName: _.get(
            otherLocationAndDate,
            'rentalLocation.dropOff.area.name',
          ),
          locationType: _.get(
            otherLocationAndDate,
            'rentalLocation.dropOff.area.type',
          ),
          poi: {
            latitude: Number(
              _.get(otherLocationAndDate, 'rentalLocation.dropOff.area.lat'),
            ),
            longitude: Number(
              _.get(otherLocationAndDate, 'rentalLocation.dropOff.area.lng'),
            ),
            radius: 0,
          },
          pickupOnDoor: 0, // 国内免费上门取车 1 true 0 false
          dropOffOnDoor: 0, // 国内免费送车上门 1 true 0 false
        },
        searchType: 1,
        appType: otherEnvAppType,
      };
      listParamHistory[otherEnvAppType] = otherEnvParam;
    }

    CarStorage.saveNoLocal(
      StorageKey.CAR_LIST_PARAM_HISTORY,
      JSON.stringify(listParamHistory),
    );
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn(e);
  }
};

const appUnLoad = () => {
  try {
    setListParamHistory();
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn(e);
  }
};

export default appUnLoad;
