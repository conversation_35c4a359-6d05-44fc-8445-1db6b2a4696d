import button from './tokens/button';
import setOpacity from './src/setOpacity';
import color from './tokens/color';
import font from './tokens/font';
import * as fontCommon from './tokens/font.common';
import * as label from './tokens/label';
import * as radius from './tokens/radius';
import * as space from './tokens/space';
import * as border from './tokens/border';
import icon from './tokens/icon';
import * as tokenType from './tokens/tokenType';
import * as druation from './tokens/druation';
import * as layout from './tokens/layout';

/**
 * 均以750设计稿为准
 */
export {
  radius,
  icon,
  font,
  fontCommon,
  layout,
  setOpacity,
  space,
  border,
  button,
  label,
  druation,
  tokenType,
  color,
};
// @ts-ignore
module.exports = {
  // @ts-ignore
  get icon() {
    return require('./tokens/icon.ctrip');
  },
  // @ts-ignore
  get font() {
    return require('./tokens/font.ctrip');
  },
  // @ts-ignore
  get fontCommon() {
    return require('./tokens/font.common');
  },
  // @ts-ignore
  get color() {
    // @ts-ignore
    return require('./tokens/color.ctrip');
  },
  radius,
  layout,
  setOpacity,
  space,
  border,
  button,
  label,
  druation,
  tokenType,
};
