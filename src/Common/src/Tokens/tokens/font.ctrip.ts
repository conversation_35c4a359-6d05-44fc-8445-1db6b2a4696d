import { Platform, TextStyle } from 'react-native';
import { BbkUtils } from '../../Utils';

const { isAndroid } = BbkUtils;
// /**
//  * http://cds.ued.ctripcorp.com/?cat=145
//  */

export const fontCnIos = 'PingFangSC-Regular';
export const fontCnIosBold = 'PingFangSC-Semibold';
export const fontCnIosMedium = 'PingFangSC-Medium';
export const fontCnIosLight = 'PingFangSC-Light';
// export const fontTripNumberBold = 'TripNumber-Bold';
// export const fontTripNumberMedium = 'TripNumber-Medium';
// export const fontTripNumberRegular = 'TripNumber-Regular';
export const fontTripNumberSemiBold = 'TripNumber-SemiBold';
// export const fontTripNumberTFRegular = 'TripNumberTF-Regular';
// export const fontTripNumberTFMedium = 'TripNumberTF-Medium';
// export const fontTripNumberTFBold = 'TripNumberTF-Bold';
// export const fontTripNumberTFSemiBold = 'TripNumberTF-SemiBold';

export enum TripNumberFamily {
  TripNumberBold = 'TripNumber-Bold',
  TripNumberMedium = 'TripNumber-Medium',
  TripNumberRegular = 'TripNumber-Regular',
  TripNumberSemiBold = 'TripNumber-SemiBold',
  TripNumberTFBold = 'TripNumberTF-Bold',
  TripNumberTFMedium = 'TripNumberTF-Medium',
  TripNumberTFRegular = 'TripNumberTF-Regular',
  TripNumberTFSemiBold = 'TripNumberTF-SemiBold',
}

const iosFontFamily = {
  normal: fontCnIosMedium,
  medium: fontCnIosMedium,
  bold: fontCnIosBold,
  light: fontCnIosLight,
  regular: fontCnIos,
  100: fontCnIosLight,
  200: fontCnIosLight,
  300: fontCnIosMedium,
  400: fontCnIosMedium,
  500: fontCnIos,
  600: fontCnIos,
  700: fontCnIosBold,
  800: fontCnIosBold,
  900: fontCnIosBold,
};

const androidFontWeight = {
  normal: '700',
  medium: '700',
  bold: 'bold',
  regular: '500',
};

const getFontFamilyFromWeight = (
  fontWeight: string | number,
  fontFamily: string = fontCnIos,
): TextStyle => {
  if (Platform.OS === 'ios') {
    return {
      fontFamily: iosFontFamily[fontWeight] || fontCnIos,
    };
  }
  return {
    fontFamily,
    fontWeight: androidFontWeight[fontWeight] || fontWeight,
  };
};

const getFont = (
  fontSize: number,
  lineHeightPlus: number,
  fontWeight: string | number = 'normal',
  fontFamily?: TripNumberFamily,
  iosFixTop?: number,
  androidFixTop?: number,
): TextStyle => {
  const lineHeight = fontSize + lineHeightPlus;
  const fontFamilyStyle = !!fontFamily ? { fontFamily } : {};
  const iosFixTopStyle = !!iosFixTop
    ? { top: BbkUtils.getPixel(iosFixTop) }
    : {};
  const androidFixTopStyle = !!androidFixTop
    ? { top: BbkUtils.getPixel(androidFixTop) }
    : {};
  const fixTop = isAndroid ? androidFixTopStyle : iosFixTopStyle;
  return {
    ...getFontFamilyFromWeight(fontWeight),
    fontSize: BbkUtils.getPixel(fontSize),
    lineHeight: BbkUtils.getPixel(lineHeight),
    ...fontFamilyStyle,
    ...fixTop,
  };
};

// export const F_16_10 = getFont(16, 10);
// export const F_16_10_regular = getFont(16, 10, 'regular');
// export const F_16_10_medium = getFont(16, 10, 'medium');
export const F_18_6_regular = getFont(18, 6, 'regular');
export const F_18_8_bold = getFont(18, 8, 'bold');
// export const F_20_4_bold = getFont(20, 4, 'bold');
export const F_20_8_regular = getFont(20, 8, 'regular');
// export const F_20_8_medium = getFont(20, 8, 'medium');
// export const F_20_8_bold = getFont(20, 8, 'bold');
export const F_20_10 = getFont(20, 10);
export const F_20_10_regular = getFont(20, 10, 'regular');
export const F_20_10_medium = getFont(20, 10, 'medium');
export const F_22_10_regular = getFont(22, 10, 'regular');
export const F_22_10_medium = getFont(22, 10, 'medium');
export const F_22_10_bold = getFont(22, 10, 'bold');
// export const F_22_10_regular_TripNumberBold = getFont(22, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_22_10_regular_TripNumberMedium = getFont(22, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_22_10_regular_TripNumberRegular = getFont(22, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_22_10_regular_TripNumberSemiBold = getFont(22, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_22_12_regular = getFont(22, 12, 'regular');
// export const F_22_12_bold = getFont(22, 12, 'bold');
export const F_24_6_regular = getFont(24, 6, 'regular');
export const F_24_8_medium = getFont(24, 8, 'medium');
export const F_24_10 = getFont(24, 10);
export const F_24_10_regular = getFont(24, 10, 'regular');
export const F_24_10_medium = getFont(24, 10, 'medium');
export const F_24_10_bold = getFont(24, 10, 'bold');
export const F_24_12_regular = getFont(24, 12, 'regular');
export const F_26_8_regular = getFont(26, 8, 'regular');
export const F_26_10 = getFont(26, 10);
export const F_26_10_regular = getFont(26, 10, 'regular');
// export const F_26_10_regular_TripNumberBold = getFont(26, 10, 'bold', TripNumberFamily.TripNumberBold);
export const F_26_10_regular_TripNumberMedium = getFont(
  26,
  10,
  'medium',
  TripNumberFamily.TripNumberMedium,
);
export const F_26_10_regular_TripNumberRegular = getFont(
  26,
  10,
  'regular',
  TripNumberFamily.TripNumberRegular,
);
// export const F_26_10_regular_TripNumberSemiBold = getFont(26, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_26_10_regular_TripNumberTFBold = getFont(26, 10, 'bold', TripNumberFamily.TripNumberTFBold);
// export const F_26_10_regular_TripNumberTFMedium = getFont(26, 10, 'medium', TripNumberFamily.TripNumberTFMedium);
// export const F_26_10_regular_TripNumberTFRegular = getFont(26, 10, 'regular', TripNumberFamily.TripNumberTFRegular);
// export const F_26_10_regular_TripNumberTFSemiBold = getFont(26, 10, 'bold', TripNumberFamily.TripNumberTFSemiBold);
export const F_26_10_medium = getFont(26, 10, 'medium');
export const F_26_10_bold = getFont(26, 10, 'bold');
// export const F_26_12_regular = getFont(26, 12, 'regular');
// export const F_26_12_medium = getFont(26, 12, 'medium');
// export const F_26_12_bold = getFont(26, 12, 'bold');
// export const F_26_16_regular = getFont(26, 16, 'regular');
// export const F_26_16_medium = getFont(26, 16, 'medium');
// export const F_26_16_bold = getFont(26, 16, 'bold');
// export const F_28_8_regular = getFont(28, 8, 'regular');
export const F_28_8_bold = getFont(28, 8, 'bold');
// export const F_28_8_medium = getFont(28, 8, 'medium');
// export const F_28_10 = getFont(28, 10);
export const F_28_10_regular = getFont(28, 10, 'regular');
export const F_28_10_medium = getFont(28, 10, 'medium');
export const F_28_10_bold = getFont(28, 10, 'bold');
// export const F_28_10_regular_TripNumberBold = getFont(28, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_28_10_regular_TripNumberMedium = getFont(28, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_28_10_regular_TripNumberRegular = getFont(28, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_28_10_regular_TripNumberSemiBold = getFont(28, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_28_16 = getFont(28, 16);
export const F_28_16_regular = getFont(28, 16, 'regular');
export const F_28_16_medium = getFont(28, 16, 'medium');
// export const F_28_12 = getFont(28, 12);
// export const F_28_12_regular = getFont(28, 12, 'regular');
export const F_28_12_medium = getFont(28, 12, 'medium');
// export const F_28_18_regular = getFont(28, 18, 'regular');
// export const F_28_18_medium = getFont(28, 18, 'medium');
// export const F_28_18_bold = getFont(28, 18, 'bold');
export const F_30_8_medium = getFont(30, 8, 'medium');
export const F_30_10 = getFont(30, 10);
export const F_30_10_regular = getFont(30, 10, 'regular');
export const F_30_10_medium = getFont(30, 10, 'medium');
export const F_30_10_bold = getFont(30, 10, 'bold');
// export const F_30_10_regular_TripNumberBold = getFont(30, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_30_10_regular_TripNumberMedium = getFont(30, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_30_10_regular_TripNumberRegular = getFont(30, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_30_10_regular_TripNumberSemiBold = getFont(30, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_30_14_regular = getFont(30, 14, 'regular');
// export const F_30_14_medium = getFont(30, 14, 'medium');
// export const F_30_14_bold = getFont(30, 14, 'bold');
export const F_30_16 = getFont(30, 16);
export const F_30_16_regular = getFont(30, 16, 'regular');
// export const F_30_16_medium = getFont(30, 16, 'medium');
// export const F_32_8_bold = getFont(32, 8, 'bold');
// export const F_32_10 = getFont(32, 10);
export const F_32_10_regular = getFont(32, 10, 'regular');
export const F_32_10_medium = getFont(32, 10, 'medium');
export const F_32_10_bold = getFont(32, 10, 'bold');
// export const F_32_10_regular_TripNumberBold = getFont(32, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_32_10_regular_TripNumberMedium = getFont(32, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_32_10_regular_TripNumberRegular = getFont(32, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_32_10_regular_TripNumberSemiBold = getFont(32, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_34_10 = getFont(34, 10);
// export const F_34_10_regular = getFont(34, 10, 'regular');
export const F_34_10_medium = getFont(34, 10, 'medium');
export const F_34_10_bold = getFont(34, 10, 'bold');
// export const F_34_10_regular_TripNumberBold = getFont(34, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_34_10_regular_TripNumberMedium = getFont(34, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_34_10_regular_TripNumberRegular = getFont(34, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_34_10_regular_TripNumberSemiBold = getFont(34, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_36_8_medium = getFont(36, 8, 'medium');
// export const F_36_10_regular_TripNumberBold = getFont(36, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_36_10_regular_TripNumberMedium = getFont(36, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_36_10_regular_TripNumberRegular = getFont(36, 10, 'regular', TripNumberFamily.TripNumberRegular);
export const F_36_10_regular_TripNumberSemiBold = getFont(
  36,
  10,
  'bold',
  TripNumberFamily.TripNumberSemiBold,
);
// export const F_36_12 = getFont(36, 12);
export const F_36_12_regular = getFont(36, 12, 'regular');
export const F_36_12_medium = getFont(36, 12, 'medium');
export const F_36_12_bold = getFont(36, 12, 'bold');
// export const F_36_16 = getFont(36, 16);
// export const F_36_16_regular = getFont(36, 16, 'regular');
// export const F_36_16_medium = getFont(36, 16, 'medium');
// export const F_36_16_bold = getFont(36, 16, 'bold');
// export const F_36_48 = getFont(36, 48);
// export const F_38_10 = getFont(38, 10);
// export const F_38_10_regular = getFont(38, 10, 'regular');
export const F_38_10_medium = getFont(38, 10, 'medium');
// export const F_38_10_bold = getFont(38, 10, 'bold');
// export const F_38_10_regular_TripNumberBold = getFont(38, 10, 'bold', TripNumberFamily.TripNumberBold);
// export const F_38_10_regular_TripNumberMedium = getFont(38, 10, 'medium', TripNumberFamily.TripNumberMedium);
// export const F_38_10_regular_TripNumberRegular = getFont(38, 10, 'regular', TripNumberFamily.TripNumberRegular);
// export const F_38_10_regular_TripNumberSemiBold = getFont(38, 10, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_40_8_bold = getFont(40, 8, 'bold');
export const F_40_12 = getFont(40, 12);
export const F_40_12_medium = getFont(40, 12, 'medium');
// export const F_40_12_bold = getFont(40, 12, 'bold');
// export const F_42_12_bold = getFont(42, 12, 'bold');
// export const F_40_12_TripNumberSemiBold = getFont(40, 12, 'bold', TripNumberFamily.TripNumberSemiBold);
// export const F_40_12_medium_TripNumberMedium = getFont(40, 12, 'medium', TripNumberFamily.TripNumberMedium);
export const F_44_0_bold = getFont(44, 0, 'bold');
// export const F_48_12 = getFont(48, 12);
// export const F_48_12_regular = getFont(48, 12, 'regular');
export const F_48_12_medium = getFont(48, 12, 'medium');
export const F_48_12_bold = getFont(48, 12, 'bold');
export const F_52_21_bold = getFont(52, 21, 'bold');
export const F_56_12_medium = getFont(56, 12, 'medium');
// export const F_60_16_bold = getFont(60, 16, 'bold');
export const F_64_12_bold = getFont(64, 12, 'bold');
// export const F_132_10 = getFont(132, 10);

// /**
//  * head
//  */
export const head1Style = F_64_12_bold;
export const head2Style = F_56_12_medium;
// // ---------- trip fix ----------
// export const head3Style = F_48_12;
export const head3MediumStyle = F_48_12_medium;
export const head3BoldStyle = F_48_12_bold;
// export const head3LightStyle = F_48_12_regular;
export const subHeadStyle = F_40_12;
export const subHeadMediumStyle = F_40_12_medium;
// export const subHeadBoldStyle = F_40_12_bold;
// /**
//  * title
//  */
// export const title0Style = F_38_10;
export const title0MediumStyle = F_38_10_medium;
// export const title0BoldStyle = F_38_10_bold;
// export const title0LightStyle = F_38_10_regular;
// export const title1Style = F_36_12;
export const title1MediumStyle = F_36_12_medium;
// export const title1MediumFlatStyle = F_36_8_medium;
export const title1BoldStyle = F_36_12_bold;
export const title1LightStyle = F_36_12_regular;
// export const title2Style = F_32_10;
export const title2LightStyle = F_32_10_regular;
export const title2MediumStyle = F_32_10_medium;
export const title2BoldStyle = F_32_10_medium;
// export const title2BoldStyle = F_32_10_bold;
// export const title2BoldFlatStyle = F_32_8_bold;
export const title3BoldStyle = F_28_10_bold;
// export const title3Style = F_28_10;
export const title3MediumStyle = F_28_10_medium;
export const title3LightStyle = F_28_10_regular;
// export const title4Style = F_34_10;
// export const title4LightStyle = F_34_10_regular;
export const title4MediumStyle = F_34_10_medium;
export const title4BoldStyle = F_34_10_bold;
export const title44BoldStyle = F_44_0_bold;

export const subTitle1Style = F_30_10;
export const subTitle1BoldStyle = F_30_10_bold;
export const subTitle1MediumStyle = F_30_10_medium;
export const subTitle1MediumFlatStyle = F_30_8_medium;
export const subTitle1RegularStyle = F_30_10_regular;
// export const subTitle2Style = F_28_12;
export const subTitle2BoldStyle = F_28_12_medium;
// export const subTitle2RegularStyle = F_28_12_regular;
// /**
//  * body
//  */
export const body1Style = F_30_16;
// export const body1BoldStyle = F_30_16_medium;
export const body1LightStyle = F_30_16_regular;
// export const body2Style = F_28_16;
export const body3MediumStyle = F_28_16_medium;
// export const body2BoldStyle = F_28_16_medium;
export const body2LightStyle = F_28_16_regular;

// // ---------- trip fix ----------
export const body3Style = F_26_10;
export const body3BoldStyle = F_26_10_bold; // gly-todo: 这里应当为Bold而不是Medium，待确认！
export const body3Medium2Style = F_26_10_medium;
export const body3LightStyle = F_26_10_regular;
export const body3LightFlatStyle = F_26_8_regular;
export const body4Style = F_24_10;
export const body4LightStyle = F_24_10_regular;
// export const body30BoldStyle = F_60_16_bold;

// /**
//  * caption
//  */
// export const caption1Style = F_24_10;
export const caption1BoldStyle = F_24_10_medium;
export const caption1BoldFlatStyle = F_24_8_medium;
export const caption1LightStyle = F_24_10_regular;
export const caption1LightPlus12Style = F_24_12_regular;
export const caption1LightPlus6Style = F_24_6_regular;
// export const caption2Style = caption1Style;
export const caption2BoldStyle = F_24_10_bold;
// export const caption2LightStyle = caption1LightStyle;
// /**
//  * label or tag
//  */
export const labelXLStyle = F_24_10_regular;
export const labelLStyle = F_22_10_regular;
export const labelLBoldStyle = F_22_10_medium;
export const labelLLightStyle = F_22_10_regular;
export const labelBoldPlusStyle = F_22_10_bold;
export const labelSStyle = F_20_10;
export const labelSBoldStyle = F_20_10_medium;
// export const labelSBoldPlus4Style = F_20_4_bold;
export const labelSLightStyle = F_20_10_regular;
export const labelSLightFlatStyle = F_20_8_regular;
// export const labelXSStyle = F_16_10;
// export const labelXSBoldStyle = F_16_10_medium;
// export const labelXSLightStyle = F_16_10_regular;
// /**
//  *  Guide used
//  */
// export const horizontalStyle = body1Style;
// export const horizontalSelectedStyle = body1BoldStyle;
// /**
//  * icon
//  */
// export const iconXXXL = F_132_10;
// /**
//  * dialog
//  */
export const dialogTitle = title4MediumStyle;
export const dialogSubTitle = body3LightStyle;

// order
export const selfHelpTitle = F_28_8_bold;
export const rcFont = F_26_10_regular;
// export const verbLayerTitle = F_32_10_medium;
// export const vehicleTitle = F_36_12_medium;
export const vehicleTitleBold = F_36_12_bold;
// export const replenishTitle = F_48_12_medium;
// export const replenishSubTitle = F_24_10_regular;
// export const replenishItemTitle = F_30_10_medium;

// // area
// export const areaLabel = F_28_8_regular;

// // location
// export const stationTitle = F_36_48;
export const productTitle = title4MediumStyle;

// // izuche
// export const userName = F_40_12_bold;

// export const feeDetailTotalPrice = F_40_8_bold;
// export const feeDetailTotalPriceCurrency = F_22_12_bold;

// ItineraryCard
export const ItineraryCardTitle = F_24_10_bold;

// //couponBook
// export const couponBookTitle = F_22_12_bold;

export const buttonSideLabel = F_18_6_regular;

export const cornerLabel = F_18_8_bold;

// // 出境免押
// export const depositFreeProcedureIndex = F_42_12_bold;
export const homeFlowTitle = F_52_21_bold;

// export const cancelTipText = F_22_12_regular;

// // Ios与Andord偏移高度待UED在Debug页面确认
// /**
//  * 通用20号字体，lineHeight高度30,自适应Android与Ios
//  */
// export const F_20_regular = getFont(20, 10, 'regular', null, 0, 0);
// /**
//  * 通用20号字体，lineHeight高度30,自适应Android与Ios
//  */
// export const F_20_medium = getFont(20, 10, 'medium', null, 0, 0);
// /**
//  * 通用20号字体，lineHeight高度30,自适应Android与Ios
//  */
// export const F_20_bold = getFont(20, 10, 'bold', null, 0, 0);
// /**
//  * 通用22号字体，lineHeight高度32,自适应Android与Ios
//  */
// export const F_22_regular = getFont(22, 10, 'regular', null, 0, 0);
// /**
//  * 通用22号字体，lineHeight高度32,自适应Android与Ios
//  */
// export const F_22_medium = getFont(22, 10, 'medium', null, 0, 0);
// /**
//  * 通用22号字体，lineHeight高度32,自适应Android与Ios
//  */
// export const F_22_bold = getFont(22, 10, 'bold', null, 0, 0);
// /**
//  * 通用24号字体，lineHeight高度34,自适应Android与Ios
//  */
// export const F_24_regular = getFont(24, 10, 'regular', null, 0, 0);
// /**
//  * 通用24号字体，lineHeight高度34,自适应Android与Ios
//  */
// export const F_24_medium = getFont(24, 10, 'medium', null, 0, 0);
// /**
//  * 通用24号字体，lineHeight高度34,自适应Android与Ios
//  */
// export const F_24_bold = getFont(24, 10, 'bold', null, 0, 0);
// /**
//  * 通用26号字体，lineHeight高度36,自适应Android与Ios
//  */
// export const F_26_regular = getFont(26, 10, 'regular', null, 0, 0);
// /**
//  * 通用26号字体，lineHeight高度36,自适应Android与Ios
//  */
// export const F_26_medium = getFont(26, 10, 'medium', null, 0, 0);
// /**
//  * 通用26号字体，lineHeight高度36,自适应Android与Ios
//  */
// export const F_26_bold = getFont(26, 10, 'bold', null, 0, 0);
// /**
//  * 通用28号字体，lineHeight高度38,自适应Android与Ios
//  */
// export const F_28_regular = getFont(28, 10, 'regular', null, 0, 0);
// /**
//  * 通用28号字体，lineHeight高度38,自适应Android与Ios
//  */
// export const F_28_medium = getFont(28, 10, 'medium', null, 0, 0);
// /**
//  * 通用28号字体，lineHeight高度38,自适应Android与Ios
//  */
// export const F_28_bold = getFont(28, 10, 'bold', null, 0, 0);
// /**
//  * 通用30号字体，lineHeight高度40,自适应Android与Ios
//  */
// export const F_30_regular = getFont(30, 10, 'regular', null, 0, 0);
// /**
//  * 通用30号字体，lineHeight高度40,自适应Android与Ios
//  */
// export const F_30_medium = getFont(30, 10, 'medium', null, 0, 0);
// /**
//  * 通用30号字体，lineHeight高度40,自适应Android与Ios
//  */
// export const F_30_bold = getFont(30, 10, 'bold', null, 0, 0);
// /**
//  * 通用32号字体，lineHeight高度42,自适应Android与Ios
//  */
// export const F_32_regular = getFont(32, 10, 'regular', null, 0, 0);
// /**
//  * 通用32号字体，lineHeight高度42,自适应Android与Ios
//  */
// export const F_32_medium = getFont(32, 10, 'medium', null, 0, 0);
// /**
//  * 通用32号字体，lineHeight高度42,自适应Android与Ios
//  */
// export const F_32_bold = getFont(32, 10, 'bold', null, 0, 0);
// /**
//  * 通用34号字体，lineHeight高度44,自适应Android与Ios
//  */
// export const F_34_regular = getFont(34, 10, 'regular', null, 0, 0);
// /**
//  * 通用34号字体，lineHeight高度44,自适应Android与Ios
//  */
// export const F_34_medium = getFont(34, 10, 'medium', null, 0, 0);
// /**
//  * 通用34号字体，lineHeight高度44,自适应Android与Ios
//  */
// export const F_34_bold = getFont(34, 10, 'bold', null, 0, 0);
// /**
//  * 通用36号字体，lineHeight高度46,自适应Android与Ios
//  */
// export const F_36_regular = getFont(36, 10, 'regular', null, 0, 0);
// /**
//  * 通用36号字体，lineHeight高度46,自适应Android与Ios
//  */
// export const F_36_medium = getFont(36, 10, 'medium', null, 0, 0);
// /**
//  * 通用36号字体，lineHeight高度46,自适应Android与Ios
//  */
// export const F_36_bold = getFont(36, 10, 'bold', null, 0, 0);
// /**
//  * 通用38号字体，lineHeight高度48,自适应Android与Ios
//  */
// export const F_38_regular = getFont(38, 10, 'regular', null, 0, 0);
// /**
//  * 通用38号字体，lineHeight高度48,自适应Android与Ios
//  */
// export const F_38_medium = getFont(38, 10, 'medium', null, 0, 0);
// /**
//  * 通用38号字体，lineHeight高度48,自适应Android与Ios
//  */
// export const F_38_bold = getFont(38, 10, 'bold', null, 0, 0);
