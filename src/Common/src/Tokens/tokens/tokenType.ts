export enum ButtonSize {
  L = 'L',
  M = 'M',
  S = 'S',
}

export enum ButtonType {
  Gradient = 'gradient',
  Default = 'default',
  Spray = 'spray',
}

export interface ButtonProps {
  buttonSize?: ButtonSize;
  buttonType?: ButtonType;
}

export enum ColorType {
  Blue = 'blue',
  DeepBlue = 'deepBlue',
  Green = 'green',
  Orange = 'orange',
  Red = 'red',
  Gray = 'gray',
  Black = 'black',
  BlueGray = 'blueGray',
  Transparent = 'transParent',
  LightRed = 'lightRed',
}

export interface ColorProps {
  colorType?:
    | ColorType.Blue
    | ColorType.Green
    | ColorType.Orange
    | ColorType.Red
    | ColorType.Gray
    | ColorType.Black
    | ColorType.BlueGray;
}

export enum LabelSize {
  L = 'L',
  S = 'S',
}

export enum LabelType {
  Base = 'base',
  Radius = 'radius',
  Bubble = 'bubble',
}

export interface labelProps {
  labelType?: 'base' | 'radius' | 'bubble';
  labelSize?: 'L' | 'S';
}
