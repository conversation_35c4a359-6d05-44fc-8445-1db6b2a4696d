// /**
//  * 统一 trip & ctrip 的 fontSize & lineHeight
//  * http://cdp.release.ctripcorp.com/project/sketch/TRIP-text%20guide/
//  */
import { Platform } from 'react-native';// @ts-ignore upgrade 072
// import { IBUFont } from '@ctrip/crn';
import { BbkUtils, BbkChannel } from '../../Utils';
import {
  fontCnIos,
  fontCnIosBold,
  fontCnIosMedium,
  fontCnIosLight,
} from './font.ctrip';

const iosFontFamily = {
  Medium: fontCnIosMedium,
  Bold: fontCnIosBold,
  Regular: fontCnIos,
  100: fontCnIosLight,
  200: fontCnIosLight,
  300: fontCnIosMedium,
  400: fontCnIosMedium,
  500: fontCnIos,
  600: fontCnIos,
  700: fontCnIosBold,
  800: fontCnIosBold,
  900: fontCnIosBold,
};
const androidFontWeight = {
  Medium: '700',
  Bold: 'bold',
  Regular: '500',
};
const ctripFontMap = {
  Tag_11: {
    fontSize: 22,
    lineHeight: 32,
  },
  Caption_12: {
    fontSize: 24,
    lineHeight: 34,
  },
  Body_13: {
    fontSize: 26,
    lineHeight: 36,
  },
  Body_14: {
    fontSize: 28,
    lineHeight: 44,
  },
  Body_15: {
    fontSize: 30,
    lineHeight: 46,
  },
  Title_14: {
    fontSize: 28,
    lineHeight: 36,
  },
  Title_15: {
    fontSize: 30,
    lineHeight: 40,
  },
  Title_16: {
    fontSize: 32,
    lineHeight: 42,
  },
  Title_18: {
    fontSize: 36,
    lineHeight: 48,
  },
  SubHead_20: {
    fontSize: 40,
    lineHeight: 52,
  },
  Head_24: {
    fontSize: 48,
    lineHeight: 60,
  },
  Head_28: {
    fontSize: 56,
    lineHeight: 68,
  },
  Head_32: {
    fontSize: 64,
    lineHeight: 76,
  },
  Title_22: {
    fontSize: 22,
    lineHeight: 34,
  },
  Title_40: {
    fontSize: 40,
    lineHeight: 48,
  },
};

const getFont = (
  fontSize: string,
  fontWeight: 'Bold' | 'Medium' | 'Regular' = 'Medium',
) => {
  const style = getFontStyle(fontSize, fontWeight) as any;
  return style;
};

const getFontFamilyFromWeight = (
  fontWeight: string | number,
  fontFamily: string = fontCnIos,
): object => {
  if (Platform.OS === 'ios') {
    return {
      fontFamily: iosFontFamily[fontWeight] || fontCnIos,
    };
  }
  return {
    fontFamily,
    fontWeight: androidFontWeight[fontWeight] || fontWeight,
  };
};

const getFontStyle = (fontSizeKey, fontWeight) => {
    const { fontSize, lineHeight } = ctripFontMap[fontSizeKey] || {
        fontSize: fontSizeKey,
        lineHeight: +fontSizeKey + 10,
      };
      return {
        ...getFontFamilyFromWeight(fontWeight),
        fontSize: BbkUtils.getPixel(fontSize),
        lineHeight: BbkUtils.getPixel(lineHeight),
      };
};

// /**
//  * head
//  */
export const head1Style = getFont('Head_32');
// export const head1BoldStyle = getFont('Head_32', 'Bold');
// export const head1LigthStyle = getFont('Head_32', 'Regular');

// export const head2Style = getFont('Head_28');
// export const head2BoldStyle = getFont('Head_28', 'Bold');
// export const head2LigthStyle = getFont('Head_28', 'Regular');

// export const head3Style = getFont('Head_24');
// export const head3BoldStyle = getFont('Head_24', 'Bold');
// export const head3LigthStyle = getFont('Head_24', 'Regular');

// export const subHeadStyle = getFont('SubHead_20');
export const subHeadBoldStyle = getFont('SubHead_20', 'Bold');
// export const subHeadLigthStyle = getFont('SubHead_20', 'Regular');

// /**
//  * title
//  */
// export const title1Style = getFont('Title_18');
// export const title1BoldStyle = getFont('Title_18', 'Bold');
// export const title1LightStyle = getFont('Title_18', 'Regular');

// export const title2Style = getFont('Title_16');
// export const title2BoldStyle = getFont('Title_16', 'Bold');
export const title2LightStyle = getFont('Title_16', 'Regular');

// export const title3Style = getFont('Title_15');
// export const title3BoldStyle = getFont('Title_15', 'Bold');
// export const title3LightStyle = getFont('Title_15', 'Regular');
// export const subTitle1Style = title3Style;
// export const subTitle1BoldStyle = title3BoldStyle;
// export const subTitle1LightStyle = title3LightStyle;

// export const title4Style = getFont('Title_14');
// export const title4BoldStyle = getFont('Title_14', 'Bold');
// export const title4LightStyle = getFont('Title_14', 'Regular');
// export const subTitle2Style = title4Style;
// export const subTitle2BoldStyle = title4BoldStyle;
// export const subTitle2LightStyle = title4LightStyle;

// /**
//  * body
//  */
// export const body1Style = getFont('Body_15');
// export const body1BoldStyle = getFont('Body_15', 'Bold');
// export const body1LightStyle = getFont('Body_15', 'Regular');

// export const body2Style = getFont('Body_14');
// export const body2BoldStyle = getFont('Body_14', 'Bold');
// export const body2LightStyle = getFont('Body_14', 'Regular');

export const body3Style = getFont('Body_13');
// export const body3BoldStyle = getFont('Body_13', 'Bold');
export const body3LightStyle = getFont('Body_13', 'Regular');

// /**
//  * caption
//  */
// export const captionStyle = getFont('Caption_12');
// export const captionBoldStyle = getFont('Caption_12', 'Bold');
// export const captionLightStyle = getFont('Caption_12', 'Regular');

// /**
//  * tag
//  */
// export const tagStyle = getFont('Tag_11');
// export const tagBoldStyle = getFont('Tag_11', 'Bold');
export const tagLightStyle = getFont('Tag_11', 'Regular');

// /**
//  * label or tag
//  */
// const $labelLStyle = {
//   lineHeight: BbkUtils.getPixel(36),
// };
// const $labelSStyle = {
//   lineHeight: BbkUtils.getPixel(32),
// };
// export const labelLStyle = {
//   ...body3LightStyle,
//   ...$labelLStyle,
// };
// export const labelLBoldStyle = {
//   ...body3BoldStyle,
//   ...$labelLStyle,
// };
// export const labelLLightStyle = {
//   ...body3LightStyle,
//   ...$labelLStyle,
// };
// export const labelSStyle = {
//   ...getFont('Tag_11', 'Regular'),
//   ...$labelSStyle,
// };
// export const labelSBoldStyle = {
//   ...getFont('Tag_11', 'Bold'),
//   ...$labelSStyle,
// };
// export const labelSLightStyle = {
//   ...getFont('Tag_11', 'Regular'),
//   ...$labelSStyle,
// };
// /**
//  *  Guide
//  */
// export const horizontalStyle = title2LightStyle;
// export const horizontalSelectedStyle = title2Style;
// /**
//  * icon
//  */
// export const iconXXXL = getFont('132');
// /**
//  * dialog
//  */
// export const dialogTitle = title1Style;
// export const dialogSubTitle = title3LightStyle;
