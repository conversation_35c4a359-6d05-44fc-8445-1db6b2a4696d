import { ViewStyle, TextStyle } from 'react-native';
import { BbkUtils } from '../../Utils';
import font from './font';

const { getPixel } = BbkUtils;

const { title2MediumStyle, body1Style, body3Style } = font;

const buttonBaseStyle: ViewStyle = {
  justifyContent: 'center',
  alignItems: 'center',
};

export const buttonLStyle: ViewStyle = {
  ...buttonBaseStyle,
  borderRadius: getPixel(12),
  paddingHorizontal: getPixel(40),
  paddingVertical: getPixel(24),
  minHeight: getPixel(88),
  minWidth: getPixel(180),
};

export const buttonLTextStyle: TextStyle = {
  ...title2MediumStyle,
  lineHeight: getPixel(42),
};

export const buttonMStyle: ViewStyle = {
  ...buttonBaseStyle,
  borderRadius: getPixel(8),
  paddingHorizontal: getPixel(32),
  paddingVertical: getPixel(28),
  minHeight: getPixel(72),
  minWidth: getPixel(144),
};

export const buttonMTextStyle: TextStyle = {
  ...body1Style,
  lineHeight: getPixel(40),
};

export const buttonSStyle: ViewStyle = {
  ...buttonBaseStyle,
  borderRadius: getPixel(8),
  paddingHorizontal: getPixel(24),
  height: getPixel(60),
  minWidth: getPixel(120),
};

export const buttonSTextStyle: TextStyle = {
  ...body3Style,
  lineHeight: getPixel(36),
};

export const buttonSprayStyle = {
  borderTopLeftRadius: getPixel(12),
  borderTopRightRadius: getPixel(12),
  borderBottomLeftRadius: getPixel(12),
  borderBottomRightRadius: getPixel(36),
};
