import { BbkUtils } from '../../Utils';
import { spaceXS } from './space';
// import { radiusS, radiusM } from './radius';

const { getPixel } = BbkUtils;

const baseStyle = {
  justifyContent: 'center',
  alignItems: 'center',
  flexDirection: 'row',
};

export const baseLabelLHeight = getPixel(32);
export const baseLabelSHeight = getPixel(28);

// export const baseLabelLStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceS,
//   minHeight: baseLabelLHeight,
// };

export const baseLabelSStyle = {
  ...baseStyle,
  paddingHorizontal: spaceXS,
  minHeight: baseLabelSHeight,
};

// export const radiusLabelLStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceL,
//   minHeight: getPixel(55),
//   borderBottomRightRadius: radiusM,
// };

// export const radiusLabelSStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceS,
//   minHeight: getPixel(32),
//   borderBottomRightRadius: radiusM,
// };

// export const bubbleLabelLStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceS,
//   minHeight: getPixel(28),
//   borderRadius: radiusS,
// };

// export const numBubbleLabelStyle = bubbleLabelLStyle;

// export const bubbleLabelSStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceS,
//   minHeight: getPixel(24),
//   borderRadius: radiusS,
// };

// export const infoBubbleLabelStyle = {
//   ...baseStyle,
//   paddingHorizontal: spaceS,
//   minHeight: getPixel(24),
// };
