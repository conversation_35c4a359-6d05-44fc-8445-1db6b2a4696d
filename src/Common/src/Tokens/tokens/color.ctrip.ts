// /**
//  * RGBA颜色
//  */
// export const R_0_0_0_0_0 = 'rgba(0, 0, 0, 0)';
export const R_0_0_0_0_7 = 'rgba(0, 0, 0, 0.7)';
// export const R_0_0_0_0_4 = 'rgba(0, 0, 0, 0.4)';
// export const R_0_0_0_0_3 = 'rgba(0, 0, 0, 0.3)';
// export const R_0_160_233_0_5 = 'rgba(0, 160, 233, 0.5)';
// export const R_0_0_0_0_14 = 'rgba(0, 0, 0, 0.14)';
// export const R_218_240_255 = 'rgb(218, 240, 255)';
// export const R_239_250_255 = 'rgb(239, 250, 255)';
// export const R_11_166_110_0_08 = 'rgba(11, 166, 110, 0.08)';
// export const R_11_166_110_0_04 = 'rgba(11, 166, 110, 0.04)';
// export const R_234_212_155_0_8 = 'rgba(234, 212, 155, 0.80)';
// export const R_159_117_49_0_3 = 'rgba(159, 117, 49, 0.30)';
// export const R_46_182_170_0_1 = 'rgba(46, 182, 170, 0.1)';
// export const R_0_0_0_0_65 = 'rgba(0, 0, 0, 0.65)';
// export const R_34_34_34_0_7 = 'rgba(34, 34, 34, 0.7)';
// export const R_3_133_246_0_1 = 'rgba(3, 133, 246, 0.1)';
// export const R_51_51_51_0_06 = 'rgba(51, 51, 51, 0.06)';
// export const R_46_49_65 = 'rgb(46, 49, 65)';
// export const R_174_191_212 = 'rgb(174, 191, 212)';
// export const R_15_41_77_0_08 = 'rgba(15, 41, 77, 0.08)';
// export const R_225_33_255_0_4 = 'rgba(225, 33, 255, 0.40)';
// export const R_154_204_249 = 'rgb(154, 204, 249)';
// export const R_0_0_0_0_6 = 'rgba(0, 0, 0, 0.60)';
// export const R_0_0_0_0_5 = 'rgba(0, 0, 0, 0.50)';
export const R_255_216_213_0_7 = 'rgba(255, 216, 213, 0.70)';
// export const R_255_255_255_0_4 = 'rgba(255, 255, 255, 0.40)';
// export const R_255_255_255_0 = 'rgba(255, 255, 255, 0)';
// export const R_255_255_255_0_5 = 'rgba(255, 255, 255, 0.50)';
// export const R_242_112_0_0_2 = 'rgba(242, 112, 0, 0.20)';
// export const R_0_0_0_0_05 = 'rgba(0, 0, 0, 0.05)';
export const R_254_189_147_1 = 'rgba(254, 189, 147, 1)';
// export const R_0_174_115_0_1 = 'rgba(0, 174, 115, 0.1)';
// export const R_255_255_255_0_13 = 'rgba(255, 255, 255, 0.13)';
// export const R_255_255_255_0_6 = 'rgba(255, 255, 255, 0.6)';
// export const R_255_199_194_0_7 = 'rgba(255, 199, 194, 0.7)';
// export const R_255_160_160 = 'rgb(255, 160, 160)';
// export const R_255_255_255_0_21 = 'rgba(255, 255, 255, 0.21)';
// export const R_0_32_59_0_02 = 'rgba(0, 32, 59, 0.02)';
// export const R_233_241_255_0 = 'rgba(233, 241, 255, 0)';
// export const R_244_250_255_0 = 'rgba(244, 250, 255, 0)';
// export const R_0_134_246_0_08 = 'rgba(0, 134, 246, 0.08)';
// export const R_0_141_155_0_5 = 'rgba(0, 141, 155, 0.5)';
export const R_201_224_254_0_55 = 'rgba(201,224,254,0.55)';
// export const R_239_244_248_0 = 'rgba(239, 244, 248, 0)';
export const R_255_119_0 = 'rgb(255, 119, 0)';
export const R_255_157_10 = 'rgb(255, 157, 10)';
export const R_255_198_198 = 'rgb(255, 198, 198)';
// export const R_161_221_255_0_22 = 'rgba(161,221,255,0.22)';
// export const R_104_185_255 = 'rgb(104,185,255)';
// export const R_17_223_119 = 'rgb(17, 223, 119)';
// export const R_0_222_174 = 'rgb(0, 222, 174)';
// export const R_255_150_48 = 'rgb(255, 150, 48)';
// export const R_255_32_75 = 'rgb(255, 32, 75)';

export const C_transparent = 'transparent';

// /**
//  * http://cds.ued.ctripcorp.com/?cat=144
//  */
// export const C_007fe8 = '#007fe8';
// export const C_0072d1 = '#0072d1';
export const C_0086f6 = '#0086f6';
export const C_2598f8 = '#2598f8';
// export const C_4caaf8 = '#4caaf8';
// export const C_72bbf9 = '#72bbf9';
// export const C_99CEFB = '#99CEFB';
// export const C_bfe0fc = '#bfe0fc';
export const C_E6F3FE = '#E6F3FE';
export const C_f3f8fe = '#f3f8fe';
// export const C_0186F6 = '#0186F6';
export const C_2698f7 = '#2698f7';
export const C_00A0E9 = '#00A0E9';
export const C_5678A8 = '#5678A8';
// export const C_4eaaf8 = '#4eaaf8';
// export const C_397FF6 = '#397FF6';

export const C_0385F6 = '#0385F6';
// export const C_2F69E2 = '#2F69E2';
// export const C_113DA3 = '#113DA3';
// export const C_007AF5 = '#007AF5';
export const C_5578A8 = '#5578A8';
// export const C_1677ff = '#1677ff';
// export const C_0083FF = '#0083FF';
// export const C_6FB8F6 = '#6FB8F6';
// export const C_fafcff = '#fafcff';
// export const C_f2f9ff = '#f2f9ff';
// export const C_4c97ff = '#4c97ff';
// export const C_f4faff = '#f4faff';
// export const C_e9f1ff = '#e9f1ff';
// export const C_4673be = '#4673be';
// export const C_006edc = '#006edc';
// export const C_0176e6 = '#0176e6';
// export const C_9DADC7 = '#9DADC7';
// export const C_B4CBED = '#B4CBED';
// export const C_8dc8ff = '#8dc8ff';
// export const C_edf7ff = '#edf7ff';
// export const C_0f4999 = '#0f4999';
// export const C_CCECFF = '#CCECFF';
// export const C_EBF6FF = '#EBF6FF';
// export const C_e3f2ff = '#e3f2ff';
// export const C_C9E0FE = '#C9E0FE';
// export const C_add9ff = '#add9ff';
export const C_006ff6 = '#006ff6';
// export const C_F5F9FF = '#F5F9FF';
// export const C_009c66 = '#009c66';
// export const C_00ae73 = '#00ae73';
// export const C_00b87a = '#00b87a';
export const C_24c28d = '#24c28d';
// export const C_4bcea2 = '#4bcea2';
// export const C_74d8b6 = '#74d8b6';
// export const C_99e1c8 = '#99e1c8';
// export const C_bfeddd = '#bfeddd';
// export const C_e6f8f1 = '#e6f8f1';
// export const C_f2fbf7 = '#f2fbf7';
// export const C_45A83A = '#45A83A';
// export const C_19A094 = '#19A094'; // Dark Green
// export const C_00AE73 = '#00AE73';
// export const C_F2FBF8 = '#F2FBF8';
// export const C_01AE73 = '#01AE73';
// export const C_26C28D = '#26C28D';

// export const C_d96500 = '#d96500';
// export const C_f27000 = '#f27000';
export const C_ff7700 = '#ff7700';
export const C_ff8b26 = '#ff8b26';
// export const C_ffa04c = '#ffa04c';
// export const C_ffb473 = '#ffb473';
// export const C_fdc899 = '#fdc899';
// export const C_ffdcbf = '#ffdcbf';
// export const C_fff1e5 = '#fff1e5';
export const C_fff8f2 = '#fff8f2';
export const C_ff6600 = '#ff6600';
// export const C_462003 = '#462003'; // Reviews Satisfaction Text
// export const C_FFF3CA = '#FFF3CA'; // Reviews Satisfaction bg
// export const C_FF5722 = '#FF5722';
// export const C_FFFAF3 = '#FFFAF3';
// export const C_FF6F00 = '#FF6F00';
// export const C_FF9500 = '#FF9500';
// export const C_FFA04D = '#FFA04D';
// export const C_FF9913 = '#FF9913';
export const C_FEF1E6 = '#FEF1E6';
export const C_FF6501 = '#FF6501';
// export const C_FE6600 = '#FE6600';
// export const C_93582f = '#93582f';
// export const C_d06117 = '#d06117';
// export const C_cc8d61 = '#cc8d61';
// export const C_FFF6F0 = '#FFF6F0';
// export const C_FFF3F0 = '#FFF3F0';
// export const C_FFEAE5 = '#FFEAE5';
export const C_ff3500 = '#ff3500';
// export const C_FEB64E = '#FEB64E';
// export const C_FF5A06 = '#FF5A06';

// export const C_d01307 = '#d01307';
// export const C_e9170a = '#e9170a';
// export const C_f63a2d = '#f63a2d';
// export const C_f85d53 = '#f85d53';
// export const C_f97f78 = '#f97f78';
// export const C_fba49d = '#fba49d';
export const C_fcc5c2 = '#fcc5c2';
// export const C_fee8e5 = '#fee8e5';
export const C_fef2f1 = '#fef2f1';
export const C_F85E53 = '#F85E53';
export const C_F5190A = '#F5190A';
// export const C_E96457 = '#E96457';
// export const C_EE3B28 = '#EE3B28';
export const C_FEF3F2 = '#FEF3F2';
// export const C_FF2F1E = '#FF2F1E';
// export const C_FFEEE7 = '#FFEEE7';
// export const C_FF481D = '#FF481D';
// export const C_FEE8E6 = '#FEE8E6';
// export const C_F5180B = '#F5180B';
// export const C_F51A0B = '#F51A0B';
// export const C_F8665B = '#F8665B';
// export const C_F03831 = '#F03831';

// export const C_E1C285 = '#E1C285'; // Easylife bg
// export const C_C9963E = '#C9963E'; // Easylife Text
// export const C_654C0A = '#654C0A'; // Easylife Text dark
// export const C_9F7531 = '#9F7531'; // Easylife Tag
// export const C_FFFDF6 = '#FFFDF6'; // Easylife Tag Bg
// export const C_EAD6A1 = '#EAD6A1'; // Easylife Border Color
// export const C_FFFBE0 = '#FFFBE0';
// export const C_DE5B10 = '#DE5B10';
export const C_FEECCF = '#FEECCF';
export const C_AE8032 = '#AE8032';
export const C_FFF3DF = '#FFF3DF';
export const C_F8E4C3 = '#F8E4C3';

export const C_333333 = '#333333';
export const C_666 = '#666';
export const C_999 = '#999';
export const C_aaa = '#aaa';
export const C_bbb = '#bbb';
export const C_ccc = '#ccc';
export const C_ddd = '#ddd';
export const C_eee = '#eee';
export const C_f4f4f4 = '#f4f4f4';
export const C_f8f8f8 = '#f8f8f8';
// export const C_455873 = '#455873';
// export const C_0F294D = '#0F294D';
export const C_030303 = '#030303';
// export const C_8592A6 = '#8592A6';
export const C_DADFE6 = '#DADFE6';
// export const C_e5f2fe = '#e5f2fe';
export const C_222 = '#222';
// export const C_F5F5F5 = '#F5F5F5';
// export const C_E0E0E0 = '#E0E0E0';
// export const C_EFECEA = '#EFECEA';
// export const C_EFEADA = '#EFEADA';
export const C_BBC9DC = '#BBC9DC';
// export const C_E5EAF2 = '#E5EAF2';
export const C_E3E3E3 = '#E3E3E3';
// export const C_D4D4D4 = '#D4D4D4';
// export const C_EFF1F6 = '#EFF1F6';
// export const C_FAFAFA = '#FAFAFA';
// export const C_D8D8D8 = '#D8D8D8';
// export const C_EBEBEB = '#EBEBEB';
// export const C_AABBD3 = '#AABBD3';
// export const C_5C7080 = '#5C7080';
// export const C_E6E6E6 = '#E6E6E6';
// export const C_F7F8F9 = '#F7F8F9';
// export const C_F6F6F6 = '#F6F6F6';
// export const C_eaeaea = '#eaeaea';
export const C_F1F5F8 = '#F1F5F8';
// export const C_FEFEFF = '#FEFEFF';
export const C_111111 = '#111111';
// export const C_222630 = '#222630';
export const C_ebeff5 = '#ebeff5';
// export const C_888888 = '#888888';
// export const C_C5C5C5 = '#C5C5C5';
// export const C_ced2d9 = '#ced2d9';
// export const C_F2F6F9 = '#F2F6F9';
// export const C_3A4A99 = '#3A4A99';
// export const C_eff2f5 = '#eff2f5';
// export const C_e7e7e7 = '#e7e7e7';

// export const C_5578a7 = '#5578a7';
export const C_89a0c2 = '#89a0c2';
// export const C_99aecb = '#99aecb';
// export const C_abbbd4 = '#abbbd4';
// export const C_bac9db = '#bac9db';
export const C_cdd6e5 = '#cdd6e5';
// export const C_dee4ee = '#dee4ee';
export const C_eef1f6 = '#eef1f6';
export const C_f6f8fa = '#f6f8fa';
// export const C_f9fafe = '#f9fafe';
export const C_DCE0E5 = '#DCE0E5';
// export const C_f8fafd = '#f8fafd';
export const C_F2F8FE = '#F2F8FE';
// export const C_99aeca = '#99aeca';
export const C_f5f6fa = '#f5f6fa';
// export const C_E9F2FF = '#E9F2FF';

export const C_EEEEEE = '#EEEEEE';
export const C_FFF = '#fff';
// export const C_F9DF02 = '#F9DF02';
export const C_000 = '#000';
// export const C_007FE9 = '#007FE9';
// export const C_C4C4C4 = '#C4C4C4';
export const C_00a7fa = '#00a7fa';
export const C_0076f5 = '#0076f5';
export const C_ffa50a = '#ffa50a';
// export const C_ff9d0a = '#ff9d0a';
// export const C_19A0F0 = '#19A0F0';
// export const C_0B67D1 = '#0B67D1';
// export const C_EDFAFF = '#EDFAFF';
// export const C_E7F3FF = '#E7F3FF';
// export const C_DFF3FF = '#DFF3FF';
// export const C_007FEA = '#007FEA';
// export const C_4DAEFF = '#4DAEFF';
export const C_218CFF = '#218CFF';
export const C_FF8739 = '#FF8739';
export const C_FF664B = '#FF664B';
// export const C_94D1FF = '#94D1FF';
// export const C_D1D7DC = '#D1D7DC';
export const C_E2F1FF = '#E2F1FF';
export const C_F1F8FF = '#F1F8FF';
export const C_F3F9FF = '#F3F9FF';
// export const C_EAF5FF = '#EAF5FF';
// export const C_F9FCFF = '#F9FCFF';
// export const C_71BDFF = '#71BDFF';
// export const C_8ACAFF = '#8ACAFF';
export const C_fb5857 = '#fb5857';
export const C_ff9365 = '#ff9365';
// export const C_8E5E11 = '#8E5E11';
// export const C_FFF8E9 = '#FFF8E9';
// export const C_FFFCF8 = '#FFFCF8';
export const C_DDE4ED = '#DDE4ED';
// export const C_F5F5F9 = '#F5F5F9';
// export const C_09BB07 = '#09BB07';
// export const C_F76260 = '#F76260';
// export const C_2eb6aa = '#2eb6aa';
// export const C_BABABA = '#BABABA';
export const C_FF6B61 = '#FF6B61';
export const C_F64D41 = '#F64D41';
// export const C_FFF1E6 = '#FFF1E6';
// export const C_FFFAF2 = '#FFFAF2';
// export const C_F2C100 = '#F2C100';
// export const C_35BC74 = '#35BC74';
// export const C_E5F8F1 = '#E5F8F1';
// export const C_F0F2F5 = '#F0F2F5';
// export const C_366AB3 = '#366AB3';
// export const C_FFEAC1 = '#FFEAC1';
// export const C_FFEBC9 = '#FFEBC9';
export const C_F63B2E = '#F63B2E';
// export const C_BFCDE0 = '#BFCDE0';
// export const C_02A971 = '#02A971';
// export const C_b7e4ff = '#b7e4ff';
// export const C_ffffdd = '#ffffdd';
// export const C_c16ff8 = '#c16ff8';
// export const C_F0FAFF = '#F0FAFF';
// export const C_1CC677 = '#1CC677';
// export const C_B809D3 = '#B809D3';
// export const C_FCDEFF = '#FCDEFF';
// export const C_01B87A = '#01B87A';
// export const C_5378A6 = '#5378A6';
export const C_474747 = '#474747';
// export const C_E5470F = '#E5470F';
// export const C_fd8f3a = '#fd8f3a';
// export const C_34C6B6 = '#34C6B6';
// export const C_3E89FA = '#3E89FA';
// export const C_5F84FE = '#5F84FE';
// export const C_787FFE = '#787FFE';
// export const C_eb7449 = '#eb7449';
// export const C_D01508 = '#D01508';
// export const C_7B3E29 = '#7B3E29';
// export const C_FFF7F1 = '#FFF7F1';
// export const C_FFEFE5 = '#FFEFE5';
// export const C_FBD9C3 = '#FBD9C3';
// export const C_F1C09F = '#F1C09F';
// export const C_D2D2D2 = '#D2D2D2';
// export const C_D0D0D0 = '#D0D0D0';
export const C_F9F9F9 = '#F9F9F9';
export const C_F98078 = '#F98078';
export const C_FF7B5B = '#FF7B5B';
export const C_F94E3A = '#F94E3A';
export const C_FF8700 = '#FF8700';
export const C_FF9908 = '#FF9908';
export const C_FF714E = '#FF714E';
export const C_F53B2E = '#F53B2E';
// export const C_FFAFAF = '#FFAFAF';
// export const C_FF6D4C = '#FF6D4C';
export const C_FF4C1D = '#FF4C1D';
export const C_FFFAF9 = '#FFFAF9';
// export const C_FEC899 = '#FEC899';
export const C_AB5647 = '#AB5647';
export const C_3167B8 = '#3167B8';
export const C_C58A7F = '#C58A7F';
// export const C_0080FF = '#0080FF';
// export const C_93300D = '#93300D';
// export const C_FCF2F1 = '#FCF2F1';
// export const C_20324B = '#20324B';
// export const C_3B5272 = '#3B5272';
// export const C_7292C0 = '#7292C0';
// export const C_4A6C9B = '#4A6C9B';
// export const C_61B7FF = '#61B7FF';
export const C_73BCFA = '#73BCFA';
export const C_00203B = '#00203B';
// export const C_034db5 = '#034db5';
// export const C_E7F1FF = '#E7F1FF';
// export const C_DFE4EA = '#DFE4EA';
export const C_EDF2F8 = '#EDF2F8';
// export const C_F46518 = '#F46518';
// export const C_FF7529 = '#FF7529';
// export const C_72BCFA = '#72BCFA';
// export const C_0172D0 = '#0172D0';
export const C_FFDDDD = '#FFDDDD';
// export const C_F5F8FA = '#F5F8FA';
// export const C_0086F8 = '#0086F8';
// export const C_B4E6FF = '#B4E6FF';
// export const C_FFF2BF = '#FFF2BF';
export const C_CCD6E5 = '#CCD6E5';
// export const C_F7FAFD = '#F7FAFD';
// export const C_05B87A = '#05B87A';
// export const C_EFFAFF = '#EFFAFF';
export const C_CEE8FF = '#CEE8FF';
export const C_979797 = '#979797';
export const C_FDEBCE = '#FDEBCE';
// export const C_784323 = '#784323';
// export const C_8D472E = '#8D472E';
// export const C_4E251C = '#4E251C';
export const C_fae2b9 = '#fae2b9';
export const C_ffeed5 = '#ffeed5';
// export const C_FFF8EC = '#FFF8EC';
// export const C_CCA76B = '#CCA76B';
export const C_555555 = '#555555';
export const C_FFF4E2 = '#FFF4E2';
export const C_FFF9EF = '#FFF9EF';
export const C_B47B43 = '#B47B43';
export const C_3E3E3E = '#3E3E3E';
export const C_86898A = '#86898A';
export const C_494949 = '#494949';
export const C_787878 = '#787878';
export const C_384A8C = '#384A8C';
export const C_192039 = '#192039';
export const C_AFB7D1 = '#AFB7D1';
export const C_4B5F9D = '#4B5F9D';
export const C_27396F = '#27396F';
export const C_6376B7 = '#6376B7';
export const C_3A4C8B = '#3A4C8B';
export const C_6275b6 = '#6275b6';
export const C_47559F = '#47559F';
export const C_383C5F = '#383C5F';
export const C_B5BBD9 = '#B5BBD9';
export const C_626B9D = '#626B9D';
export const C_465290 = '#465290';
export const C_7C8ADC = '#7C8ADC';
export const C_49579E = '#49579E';
export const C_7b89db = '#7b89db';
export const C_595cb3 = '#595cb3';
export const C_e3edfe = '#e3edfe';
export const C_7EA8B5 = '#7EA8B5';
export const C_36647F = '#36647F';
export const C_D3F5FF = '#D3F5FF';
export const C_E8F9FE = '#E8F9FE';
export const C_5E9BAE = '#5E9BAE';
export const C_CBDCE1 = '#CBDCE1';
export const C_6FABBF = '#6FABBF';
export const C_6298AA = '#6298AA';
export const C_8ABCCC = '#8ABCCC';
export const C_55859A = '#55859A';
export const C_6293a7 = '#6293a7';
export const C_218db1 = '#218db1';
export const C_e3f6fc = '#e3f6fc';
export const C_DDB57E = '#DDB57E';
export const C_9D763F = '#9D763F';
export const C_F1E1CB = '#F1E1CB';
export const C_DBB981 = '#DBB981';
export const C_D0A357 = '#D0A357';
export const C_E6C489 = '#E6C489';
export const C_C8A16A = '#C8A16A';
export const C_debb81 = '#debb81';
export const C_7AA2DD = '#7AA2DD';
export const C_416FB2 = '#416FB2';
export const C_CEE2FF = '#CEE2FF';
export const C_F0F6FF = '#F0F6FF';
export const C_CADAF1 = '#CADAF1';
export const C_6090D4 = '#6090D4';
export const C_91B7EE = '#91B7EE';
export const C_4C7EC6 = '#4C7EC6';
export const C_78a0db = '#78a0db';
export const C_4d86d8 = '#4d86d8';
export const C_D7F1FF = '#D7F1FF';
export const C_EFF9FF = '#EFF9FF';
export const C_99DCFD = '#99DCFD';
export const C_46B3FF = '#46B3FF';
export const C_0092F8 = '#0092F8';
export const C_0FBFFF = '#0FBFFF';
export const C_008AF5 = '#008AF5';
export const C_2c9af7 = '#2c9af7';
// export const C_F20C00 = '#F20C00';
// export const C_dce4ed = '#dce4ed';
// export const C_55B7FF = '#55B7FF';
// export const C_49A0FF = '#49A0FF';
// export const C_FF8678 = '#FF8678';
// export const C_FFEFEF = '#FFEFEF';
// export const C_F8FBFF = '#F8FBFF';
// export const C_0286f6 = '#0286f6';
// export const C_5B5B5B = '#5B5B5B';
// export const C_FF502C = '#FF502C';
// export const C_F6F9FD = '#F6F9FD';
// export const C_FF480E = '#FF480E';
// export const C_912F00 = '#912F00';
// export const C_d7231d = '#d7231d';
// export const C_FF2323 = '#FF2323';
// export const C_284B7D = '#284B7D';
// export const C_d6d6d6 = '#d6d6d6';
// export const C_F51909 = '#F51909';
// export const C_5c5c5c = '#5c5c5c';
// export const C_ca741c = '#ca741c';
// export const C_FFF0DB = '#FFF0DB';
// export const C_0ba66e = '#0ba66e';
// export const C_FFEBDB = '#FFEBDB';
export const C_F2200C = '#F2200C';
// export const C_fff6f5 = '#fff6f5';
// export const C_3568FF = '#3568FF';
// export const C_3B98FE = '#3B98FE';
// export const C_FFC25F = '#FFC25F';
// export const C_E98811 = '#E98811';
// export const C_AE6214 = '#AE6214';
export const C_D5E8FF = '#D5E8FF';
export const C_F1F7FF = '#F1F7FF';
export const C_FF5500 = '#FF5500';
export const C_888 = '#888';
// export const C_E6FAF3 = '#E6FAF3';
// export const C_24B381 = '#24B381';
// export const C_F0F0F0 = '#F0F0F0';
// export const C_9f5c5c = '#9f5c5c';
// export const C_ebf3ff = '#ebf3ff';
// export const C_F5F7FA = '#F5F7FA';
// export const C_f97b20 = '#f97b20';
// export const C_FF9900 = '#FF9900';
// export const C_F8FAFB = '#F8FAFB';
export const C_fff8ed = '#fff8ed';
export const C_dbdcde = '#dbdcde';
// export const C_1856AE = '#1856AE';
// export const C_E0F0FD = '#E0F0FD';
// export const C_FFF4E1 = '#FFF4E1';
// export const C_00D19F = '#00D19F';
// export const C_E3F9EF = '#E3F9EF';
// export const C_008FFF = '#008FFF';
// export const C_FCFDFF = '#FCFDFF';
// export const C_D9EEFE = '#D9EEFE';
// export const C_E5F7FE = '#E5F7FE';
// export const C_E1F2FF = '#E1F2FF';
// export const C_FF8800 = '#FF8800';
// export const C_CBCBCB = '#CBCBCB';
// export const C_5D6F96 = '#5D6F96';
// export const C_D5DAE1 = '#D5DAE1';
// export const C_657c96 = '#657c96';
// export const C_318BF7 = '#318BF7';
// export const C_2aa1ff = '#2aa1ff';
// export const C_F5F8FB = '#F5F8FB';

export const white = C_FFF;
export const textWhite = C_FFF;
export const black = C_000;
export const transparent = C_transparent;
export const transparentBase = C_transparent;
export const blackTransparent = R_0_0_0_0_7;
export const modalShadow = R_0_0_0_0_7;
// export const cardShadow = R_0_0_0_0_14;

// 品牌色、标准色
export const blueBase = C_0086f6;
// export const greenBase = C_00b87a;
export const orangeBase = C_ff7700;
export const redBase = C_F5190A;
export const grayBase = C_999;
export const blueGrayBase = C_111111;
export const blueDeepBase = C_006ff6;
// 单色图标
export const blueIcon = C_2598f8;
export const greenIcon = C_24c28d;
// export const redIcon = C_f63a2d;
// 边框色
export const redBorder = C_fcc5c2;
export const grayBorder = C_eee;
export const grayDescLine = C_bbb;
export const darkGrayBorder = C_ccc;
// // 浅色衬底
export const blueBg = C_f3f8fe;
export const blueBgSecondary = C_E6F3FE;
export const orangeBg = C_fff8f2;
export const orangeBgLight = C_fff8f2;
export const redBg = C_fef2f1;
export const grayBg = C_f4f4f4;
export const grayBgSecondary = C_f8f8f8;
export const blueGrayBg = C_eef1f6;
export const tableBg = C_F2F8FE;
export const grayPlaceholder = C_EEEEEE;
export const tableGrayBg = C_f5f6fa;
export const blackBase = C_111111;

// // 点击色

// 价格
export const orangePrice = C_ff6600;
// export const priceColor = C_ff6600;

// // 字体
export const fontPrimary = C_111111;
export const fontSecondary = C_666;
export const fontSubDark = C_999;
export const fontSubLight = C_ddd;
export const fontGrayBlue = C_aaa;
export const fontGrayDark = C_222;

// // 渐变
// export const linearGradientBlueLight = C_00a7fa;
// export const linearGradientBlueDark = C_0076f5;
export const linearGradientOrangeLight = C_ffa50a;
export const linearGradientOrangeDark = C_ff7700;
export const linearGradientOrderCardBlueLight = C_00a7fa;
export const linearGradientOrderCardBlueDark = C_218CFF;
export const linearGradientOrderCardOrangeLight = C_FF8739;
export const linearGradientOrderCardOrangeDark = C_FF664B;
// export const linearGradientPlateDark = C_94D1FF;
// export const linearGradientPlateOtherDark = C_D1D7DC;
export const linearGradientItineraryCardStop1 = C_E2F1FF;
export const linearGradientItineraryCardStop2 = C_F1F8FF;
export const linearGradientItineraryCardStop3 = C_F3F9FF;

export const linearGradientRushGrabDark = C_fb5857;
export const linearGradientRushGrabLight = C_ff9365;

export const newLinearGradientRushGrabDark = R_255_119_0;
export const newLinearGradientRushGrabLight = R_255_157_10;

// // 保险绿

export const radioBorderColor = C_DCE0E5;
export const radioDisableBackGroundColor = C_cdd6e5;

// // horizontal selected of blue color
// export const horizontalSelectedColor = C_0186F6;
export const horizontalColor = C_666;

// // pickdrop of guide for map component
export const mapTabSelectdColor = C_0086f6;

// 统一将所有按钮的颜色替换为006ff6
export const btnBorder = C_006ff6;
export const guideStepSequenceColor = C_ccc;
export const btnDarkBlueBorder = C_0385F6;

// // 表格

// // 无忧租

// 芝麻免押 blue14
export const sesamePrimary = C_00A0E9;

// 猜你喜欢
export const likeGradient1 = C_FF6B61;
export const likeGradient2 = C_F64D41;

// export const labelGreenBg = C_e6f8f1;
// export const labelGreenText = C_00b87a;
// export const labelOrangeText = C_ff7700;
export const labelBlueBg = C_E6F3FE;
// export const bookbarTipsOrangeBg = C_FFFAF2;
// // 点评

// // 订单详情页
// export const blueNps = C_397FF6;
// export const blueVehicleHdBg = C_E9F2FF;
export const selfHelpBg = C_f6f8fa;
export const dayText = C_DADFE6;
// export const verifyTitle = C_ff6600;
// export const insLableBg = C_eee;
// export const bbkNpsBgColor = white;
// export const bbkNpsTitleColor = fontPrimary;
// export const bbkNpsWillStatusColor = blueGrayBase;
// export const bbkNpsPointNumColor = mapTabSelectdColor;
// export const bbkNpsPointNumBgColor = blueBgSecondary;
// export const bbkNpsSelectColor = blueBase;
// export const bbkNpsInputBg = C_f6f8fa;
export const diffBg = C_FEF3F2;

// 优惠
export const discountRed = C_F85E53;

// // 支付方式label
// export const fontBlackDark = C_455873;
// export const fontBlackLight = C_0F294D;
// export const orangePayLabel = C_FF6F00;

// dialog
export const dialogTitle = C_030303;

// // driver
// export const dirverRadioVaildBorder = C_eee;

// // 沪牌
// // 新版沪牌

// // 芝麻label border

// 城市区域
export const defaultLabel = C_5678A8;
export const secLabel = C_89a0c2;

// 平铺列表
export const lineColor = C_EEEEEE;

// 分割线
export const splitGray = C_ccc;

// // Bookbar
// export const bookbarBgColor = C_FFF;
// filterbutton
export const tipsRed = C_F63B2E;

// // AddedService
// export const addedServiceOrangeTag = C_ff6600;

// export const shadowColor = R_15_41_77_0_08;
// switch
export const switchBgInactive = C_ccc;

// // warning
// export const warning = C_FF9913;

// // 国庆节颜色
// export const nationalDayColor = white;
// // 列表页总价说明
// export const priceDescIconColor = C_01B87A;

// // 一嗨全国连锁标签

// // 新版首页底部slogan的title颜色
export const homeSloganTitColor = C_474747;

// 优惠券
export const couponItemBg = C_F9F9F9;
export const couponRed = C_F98078;
export const couponLinearStart = C_FF7B5B;
export const couponLinearEnd = C_F94E3A;
export const cornerLinearStart = C_FF8700;
export const cornerLinearEnd = C_FF9908;
export const listCouponLinearStart = C_FF714E;
export const listCouponLinearEnd = C_F53B2E;
export const homeNewCouponLinearStart = R_255_216_213_0_7;
export const homeHasPurchaseCouponLinearEnd = R_255_198_198;
export const newCoupon = C_FF4C1D;
export const listCouponEntryBg = C_FFFAF9;

// // 取消订单到店无车颜色

// // 特权弹窗
export const privilegeBtnColor = C_AB5647;
export const privilegeBgColor = C_3167B8;
export const privilegeDescColor = C_C58A7F;
// // 修改订单补款确认页-保险加购状态颜色
// // 标签样式
export const labelPostfixBg = C_FEF1E6;
export const labelMarketBorder = R_254_189_147_1;
// // 全国连锁
// // 租车中心
// // 退款进度
export const refundTotalAmount = C_FF6501;
// export const refundSuccess = C_01AE73;
// export const refundFaile = C_F5180B;
// // 首页行程卡
export const ItineraryCardTitle = C_5578A8;
export const ItineraryCardSelectedBorder = C_73BCFA;

export const cardShadowColor = C_00203B;

export const homeCouponBgShadow = C_FFDDDD;
export const newBookingTableLine = C_CCD6E5;
// // 订详押金

// 行程卡 radio 边框颜色
export const itineraryCardRadioBoxBorder = C_BBC9DC;
export const itineraryCardRadioBoxTick = C_DDE4ED;
export const itineraryCardRadioShadow = C_CEE8FF;
export const itineraryCardGapLine = C_979797;

// 权益页面渐变色
export const memberText = C_FDEBCE;
export const memberDetailTxt = C_fae2b9;
export const memberGoRent = C_ffeed5;
// // 黑钻
export const linearGradientBlackDiamondStart = C_555555;
export const linearGradientBlackDiamondEnd = black;
export const blackDiamondTitleBg = C_FFF4E2;
export const blackDiamondTipBg = C_FFF9EF;
export const blackDiamondTipText = C_B47B43;
export const blackDiamondLine = grayDescLine;
export const blackDimamndText = C_FEECCF;
export const linearGradientButtonBlackDiamondStart = C_3E3E3E;
export const linearGradientButtonBlackDiamondEnd = black;
export const linearGradientMenuBlackDiamondStart = C_86898A;
export const linearGradientMenuBlackDiamondEnd = C_494949;
export const blackDiamondNumber = C_787878;
export const blackDiamondBookingTxt = C_AE8032;
export const blackDiamondCouponBg = C_FFF3DF;
export const blackDiamondBookingTip = C_F8E4C3;
// 金钻
export const linearGradientGoldDiamondStart = C_384A8C;
export const linearGradientGoldDiamondEnd = C_192039;
export const goldDiamondTitleBg = C_FFF4E2;
export const goldDiamondTipBg = C_FFF9EF;
export const goldDiamondTipText = C_B47B43;
export const goldDiamondLine = C_AFB7D1;
export const goldDiamondText = C_FEECCF;
export const linearGradientButtonGoldDiamondStart = C_4B5F9D;
export const linearGradientButtonGoldDiamondEnd = C_27396F;
export const linearGradientMenuGoldDiamondStart = C_6376B7;
export const linearGradientMenuGoldDiamondEnd = C_3A4C8B;
export const goldDiamondNumber = C_6275b6;
export const goldDiamondBookingTxt = C_AE8032;
export const goldDiamondCouponBg = C_FFF3DF;
export const goldDiamondBookingTip = C_F8E4C3;
// 钻石
export const linearGradientDiamondStart = C_47559F;
export const linearGradientDiamondEnd = C_383C5F;
export const diamondTitleBg = C_FFF4E2;
export const diamondTipBg = C_FFF9EF;
export const diamondTipText = C_B47B43;
export const diamondLine = C_B5BBD9;
export const diamondText = C_FEECCF;
export const linearGradientButtonDiamondStart = C_626B9D;
export const linearGradientButtonDiamondEnd = C_465290;
export const linearGradientMenuDiamondStart = C_7C8ADC;
export const linearGradientMenuDiamondEnd = C_49579E;
export const diamondNumber = C_7b89db;
export const diamondBookingTxt = C_595cb3;
export const diamondCouponBg = C_e3edfe;
export const diamondBookingTip = diamondBookingTxt;
// 铂金
export const linearGradientPlatinumStart = C_7EA8B5;
export const linearGradientPlatinumEnd = C_36647F;
export const platinumTitleBg = C_D3F5FF;
export const platinumTipBg = C_E8F9FE;
export const platinumTipText = C_5E9BAE;
export const platinumLine = C_CBDCE1;
export const platinumText = white;
export const linearGradientButtonPlatinumStart = C_6FABBF;
export const linearGradientButtonPlatinumEnd = C_6298AA;
export const linearGradientMenuPlatinumStart = C_8ABCCC;
export const linearGradientMenuPlatinumEnd = C_55859A;
export const platinumNumber = C_6293a7;
export const platinumBookingTxt = C_218db1;
export const platinumCouponBg = C_e3f6fc;
export const platinumBookingTip = platinumBookingTxt;
// 黄金
export const linearGradientGoldStart = C_DDB57E;
export const linearGradientGoldEnd = C_9D763F;
export const goldTitleBg = C_FFF4E2;
export const goldTipBg = C_FFF9EF;
export const goldTipText = C_B47B43;
export const goldLine = C_F1E1CB;
export const goldText = white;
export const linearGradientButtonGoldStart = C_DBB981;
export const linearGradientButtonGoldEnd = C_D0A357;
export const linearGradientMenuGoldStart = C_E6C489;
export const linearGradientMenuGoldEnd = C_C8A16A;
export const goldNumber = C_debb81;
export const goldBookingTxt = C_AE8032;
export const goldCouponBg = C_FFF3DF;
export const goldBookingTip = goldBookingTxt;
// 白银
export const linearGradientSilverStart = C_7AA2DD;
export const linearGradientSilverEnd = C_416FB2;
export const silverTitleBg = C_CEE2FF;
export const silverTipBg = C_F0F6FF;
export const silverTipText = C_7AA2DD;
export const silverLine = C_CADAF1;
export const silverText = white;
export const linearGradientButtonSilverStart = linearGradientSilverStart;
export const linearGradientButtonSilverEnd = C_6090D4;
export const linearGradientMenuSilverStart = C_91B7EE;
export const linearGradientMenuSilverEnd = C_4C7EC6;
export const silverNumber = C_78a0db;
export const silverHomeBlockTitleColor = C_4d86d8;
// 普通
export const linearGradientNormalStart = linearGradientOrderCardBlueLight;
export const linearGradientNormalEnd = C_0076f5;
export const normalTitleBg = C_D7F1FF;
export const normalTipBg = C_EFF9FF;
export const normalTipText = C_00a7fa;
export const normalLine = C_99DCFD;
export const normalText = white;
export const linearGradientButtonNormalStart = C_46B3FF;
export const linearGradientButtonNormalEnd = C_0092F8;
export const linearGradientMenuNormalStart = C_0FBFFF;
export const linearGradientMenuNormalEnd = C_008AF5;
export const normalNumber = linearGradientButtonNormalStart;
export const normalHomeBlockTitleColor = C_2c9af7;
// // 订详续租背景色
// export const getCouponToRent = C_d7231d;
// // 标签升级
export const marketCouponPriceColor = C_ff3500;

// // 优选强化
// export const optimizationStrengthenDot = C_5c5c5c;
// // 新版限行提示

// // 车型推荐标签
// export const recommendLabel = C_0ba66e;

// // 能源说明弹窗

// 修改订单修改说明提醒
export const modifyOrderExplain = C_F2200C;

// 无少结果推荐
export const noResultBg = C_F1F5F8;
export const recommendProposeBg = C_111111;
export const virutualNumberBgGradient1 = C_D5E8FF;
export const virutualNumberBgGradient2 = C_F1F7FF;

export const recommendBg = C_FF5500;

export const virtualNumberSplitLine = C_888;
export const virtualNumberTitleBg = R_201_224_254_0_55;

// 填写页优化
export const bookingOptimizationBlue = C_006ff6;
// export const addMoreDriver = C_888888;
// 填写页优惠押金合并
export const couponSplit = C_ebeff5;
// export const disableCoupon = C_C5C5C5;

// 订单卡片
export const orderCardUpgradeBg = C_fff8ed;

// 首页新版搜索框
export const searchTip = C_ff8b26;
export const searchGapSplit = C_dbdcde;

// 年龄修改提示
export const ageModifyBlue = C_006ff6;
// // 首页灰色背景
// export const homeBottomBg = C_F2F6F9;

export const osdItineraryCardBg = '#F5F8FC';

export const osdItineraryCardBoder = '#EEEFF3';

export const osdItineraryGapLine = '#D5D5D5';

export const osdlinearGradientItinerary1 = '#E6EFFB';

// export const osdlinearGradientItinerary2 = '#E6ECF4';

// export const C_F2F7FE = '#F2F7FE';
// export const C_FFD7CB = '#FFD7CB';
// export const C_FFEACC = '#FFEACC';
// export const C_36190A = '#36190A';
// export const C_646464 = '#646464';
// export const C_CBDFFC = '#CBDFFC';
// export const C_CACACA = '#CACACA';
// export const C_00A66F = '#00A66F';
// export const C_F54336 = '#F54336';
// export const C_FFE4CC = '#FFE4CC';
// export const C_4D6F94 = '#4D6F94';
// export const C_14C800 = '#14C800';
// export const C_D0D8E6 = '#D0D8E6';
// export const C_00B988 = '#00B988';
// export const C_006FF6 = '#006ff6';
// export const C_DBE7F0 = '#DBE7F0';
// export const C_FE5500 = '#FE5500';
// export const C_001A65 = '#001A65';
// export const C_000A43 = '#000A43';
export const C_0066F6 = '#0066F6';
export const C_009FFB = '#009FFB';
export const C_B8BDC8 = '#B8BDC8';
export const C_8B91A0 = '#8B91A0';
export const C_0073F5 = '#0073F5';

// export const productRecommendTip = '#385B8C';
// export const productRecommendTipBg = '#C8E1FF';
// export const C_DFE9F4 = '#DFE9F4';
// export const C_E3EAF1 = '#E3EAF1';
// export const C_F1F5FC = '#F1F5FC';
// export const C_FFF8F8 = '#FFF8F8';
// export const C_F4A29D = '#F4A29D';
// export const C_69C6A5 = '#69C6A5';
// export const C_EFF8F3 = '#EFF8F3';
// export const C_EDF5FF = '#EDF5FF';
// export const C_0088F6 = '#0088F6';
// export const C_F1F3FA = '#F1F3FA';
// export const C_E3E3E4 = '#E3E3E4';
// export const C_E8ECF1 = '#E8ECF1';
// export const C_E8E8E8 = '#E8E8E8';
// export const C_08A66F = '#08A66F';
// export const C_D9E0EE = '#D9E0EE';
// export const C_DEDEDE = '#DEDEDE';
// export const C_FA6400 = '#FA6400';
// export const C_EFEFEF = '#EFEFEF';
// export const C_777777 = '#777777';
// export const C_E8F5FF = '#E8F5FF';
// export const C_71ACFF = '#71ACFF';
export const C_FFEDE8 = '#FFEDE8';
// export const C_DEE9FF = '#DEE9FF';
// export const C_DBF3FF = '#DBF3FF';
// export const C_D4EEFF = '#D4EEFF';
export const C_F9A278 = '#F9A278';
export const C_619EFF = '#619EFF';
// export const C_83C0FF = '#83C0FF';
export const C_DBDBDB = '#DBDBDB';
// export const C_4673BE = '#4673BE';
// export const C_4673B2 = '#4673B2';
// export const C_2375FF = '#2375FF';
// export const C_00CB86 = '#00CB86';
// export const C_41A5F9 = '#41A5F9';
export const deepBlueBase = C_006ff6;
// export const C_E4F9F2 = '#E4F9F2';
// export const C_EEF4FF = '#EEF4FF';
// export const C_DCE1E8 = '#DCE1E8';
// export const C_A8A8A8 = '#A8A8A8';
// export const C_C4DDFF = '#C4DDFF';
export const C_EBF4FF = '#EBF4FF'
