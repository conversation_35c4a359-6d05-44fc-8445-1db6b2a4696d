/*
 * Backpack - Skyscanner's Design System
 *
 * Copyright 2016-2019 Skyscanner Ltd
 *
 * Licensed under the Apache License, Version 2.0 (the 'License');
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an 'AS IS' BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import color from '../tokens/color';

const colorToRgb = (colorToken, opacity) => {
  if (!colorToken) return '';
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  let color = colorToken.toLowerCase();
  if (reg.test(color)) {
    if (color.length === 4) {
      let colorNew = '#';
      for (let i = 1; i < 4; i += 1) {
        colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1));
      }
      color = colorNew;
    }
    var colorChange = [];
    for (var i = 1; i < 7; i += 2) {
      colorChange.push(parseInt('0x' + color.slice(i, i + 2)));
    }
    return `rgba(${colorChange.join(',')}, ${opacity})`;
  } else {
    return color;
  }
};
const setOpacity = (colorToken, opacity) => {
  if (colorToken === color.transparentBase) {
    return colorToken;
  }
  // throw new Error 引发白屏，兼容处理
  let safeOpacityValue = opacity;
  if (isNaN(parseFloat(opacity))) {
    safeOpacityValue = 1;
  } else if (opacity > 1) {
    safeOpacityValue = 1;
  } else if (opacity < 0) {
    safeOpacityValue = 0;
  }
  return colorToRgb(colorToken, safeOpacityValue);
};

export default setOpacity;
