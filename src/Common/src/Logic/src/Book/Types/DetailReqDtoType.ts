export interface AllianceInfoType {
  AllianceId?: number;
  Ouid?: string;
  Sid?: number;
  DistributorOrderId?: string;
  DistributorUID?: string;
  DistributorChannelId?: string;
}

export interface MobileInfoType {
  CustomerGPSLat?: number;
  CustomerGPSLng?: number;
  MobileModel?: string;
  MobileSN?: string;
  CustomerIP?: string;
  WirelessVersion?: string;
}

export interface ExtraTagsType {
  key?: string;
}

export interface BaseRequestType {
  sourceFrom?: string;
  requestId?: string;
  parentRequstId?: string;
  channelId?: number;
  AllianceInfo?: AllianceInfoType;
  locale?: string;
  currencyCode?: string;
  MobileInfo?: MobileInfoType;
  sourceCountryId?: number;
  site?: string;
  language?: string;
  sessionId?: string;
  invokeFrom?: number;
  uid?: string;
  patternType?: number;
  clientId?: string;
  vid?: string;
  extraTags?: ExtraTagsType;
}

export interface ExtensionType {
  name?: string;
  value?: string;
}

export interface HeadType {
  syscode?: string;
  lang?: string;
  auth?: string;
  cid?: string;
  ctok?: string;
  cver?: string;
  sid?: string;
  extension?: Array<ExtensionType>;
  sauth?: string;
}

export interface VcExtendRequestType {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}

export interface ReferenceType {
  bizVendorCode?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
  vehicleCode?: string;
  packageId?: number;
  packageType?: number;
  vcExtendRequest?: VcExtendRequestType;
  decoratorVendorType?: number;
  easyLifeUpgradePackageId?: number;
  isEasyLife?: boolean;
  withPrice?: boolean;
  packageId4CutPrice?: number;
  payMode?: number;
  bomCode?: string;
  productCode?: string;
  rateCode?: string;
  subType?: number;
  comPriceCode?: string;
  priceVersion?: string;
  priceVersionOfLowestPrice?: string;
  pCityId?: number;
  rCityId?: number;
  vendorVehicleCode?: string;
  age?: number;
  alipay?: boolean;
  aType?: number;
  hotType?: number;
  priceType?: number;
  vehicleDegree?: string;
  idType?: number;
  fType?: number;
}

export interface PoiType {
  longitude?: number;
  latitude?: number;
  radius?: number;
}

export interface DoorToDoorType {
  need?: boolean;
  level?: number;
  type?: number;
}

export interface PickupPointInfoType {
  locationType?: number;
  cityId?: number;
  locationCode?: string;
  locationName?: string;
  poi?: PoiType;
  date?: Date;
  storeCode?: string;
  address?: string;
  doorToDoor?: DoorToDoorType;
  pickUpLevel?: number;
  pickOffLevel?: number;
  pickupOnDoor?: boolean;
  dropOffOnDoor?: boolean;
}

export interface DetailReqType {
  baseRequest?: BaseRequestType;
  head?: HeadType;
  reference?: ReferenceType;
  pickupPointInfo?: PickupPointInfoType;
  returnPointInfo?: PickupPointInfoType;
}
