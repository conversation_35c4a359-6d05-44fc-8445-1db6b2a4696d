export enum ServiceProgressStatus {
    UnAllot = '1',
    UnDeal = '2',
    Dealing = '3',
    Reprieve = '4',
    Complete = '5',
    MoreUnComplete = '99'
}

export interface ProgressItem{
    title?: string;
    description?: string;
    code?: string;
    type?: Number;
    typeDesc?: string;
    sortNum?: Number;
    extCode?: Number;
}

export interface ServiceProgressDTO{
    serviceId?: string;
    createTime?: string;
    status?: ServiceProgressStatus;
    progressList?: Array<ProgressItem>;
}

