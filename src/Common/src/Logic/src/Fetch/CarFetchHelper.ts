import { Fetch, Device, Application } from '@ctrip/crn';
import _ from 'lodash';
import { BbkUtils } from '../../../Utils';

export const FETCH_CACHE = {
  TIME: 60, // seconds
  LOCATION: 'MEM',
};

export interface cachePolicyType {
  enableCache: boolean;
  cacheExpireTime: number;
  cacheKey: string;
  cacheLocation: string;
}

export interface appRequestMapType {
  cachePolicy: cachePolicyType;
}

export interface appResponseMapType {
  isCacheValid: boolean;
}

export interface RequestType {
  sequenceId?: string;
  appRequestMap?: appRequestMapType;
}

export interface parameterBuilderType {
  param: any;
  useCache?: boolean;
  userId?: string;
  cacheExpireTime?: number; // seconds
  verifyResponseIsValid?: Function;
}

/**
 * get fetch cache key
 */
export interface getCacheKeyType {
  url: string;
  param: any;
  uid: string;
  omit?: Array<string>;
}

export interface withRetryType {
  maxCount?: number;
  retryCount?: number;
}

export interface fetchCostTimeType {
  isSuccess: boolean;
  networkCost?: number;
  useCache?: boolean;
  isFromCache?: boolean;
  isCacheValid?: boolean;
  url: string;
  requestId: string;
  parentRequestId?: string;
  pageId?: string;
  error?: any;
}

export interface baseRequestType {
  sourceFrom: string;
  channelType?: number;
  requestId?: string;
  parentRequestId?: string;
  invokeFrom?: string;
  site?: string;
  language?: string;
  locale?: string;
  currencyCode?: string;
  sourceCountryId?: string;
  channelId?: number;
  clientVersion?: string;
  vid?: string;
  patternType?: string;
  productCategoryID?: number;
  allianceId?: string;
  sId?: string;
  extraTags?: any;
  extMap?: any;
  extraMaps?: any;
}

export const parameterBuilder = ({
  param,
  useCache,
  userId,
  cacheExpireTime,
  verifyResponseIsValid,
}: parameterBuilderType) => {
  const parameter = BbkUtils.cloneDeep(param);
  parameter.appRequestMap = {};

  const addCacheSetter = () => {
    parameter.appRequestMap.cachePolicy = {
      enableCache: true,
      cacheExpireTime: cacheExpireTime || FETCH_CACHE.TIME,
    };
  };

  if (useCache) {
    addCacheSetter();
  }

  if (userId) {
    parameter.appRequestMap.userId = userId;
  }

  if (verifyResponseIsValid) {
    parameter.appRequestMap.verifyResponseIsValid = verifyResponseIsValid;
  }

  return parameter;
};

export const withRetry =
  (func: Function) =>
  async ({ maxCount = 0, retryCount = 0 }: withRetryType, ...args: any) => {
    try {
      const res = await func(...args);
      let currentRetryCount = retryCount;
      let isRetry = false;
      // 无效缓存，直接重试一次连接
      if (
        res &&
        res.appResponseMap &&
        res.appResponseMap.isFromCache &&
        !res.appResponseMap.isCacheValid &&
        currentRetryCount === 0
      ) {
        isRetry = true;
      }

      const verifyResponse = () => {
        const { verifyResponseIsValid = null } = args.appRequestMap || {};
        if (verifyResponseIsValid) {
          return verifyResponseIsValid(res);
        }
        return !!res;
      };

      // 判断响应是否有效
      if (!isRetry && !verifyResponse() && currentRetryCount < maxCount) {
        isRetry = true;
      }

      if (isRetry) {
        currentRetryCount += 1;
        return withRetry(func)(
          { maxCount, retryCount: currentRetryCount },
          ...args,
        );
      }
      return res;
    } catch (err) {
      throw err;
    }
  };

export const getCacheKey = ({
  url,
  uid,
  param = {},
  omit = ['now', 'requestId', 'baseRequest', 'uniqRequestKey'],
}: getCacheKeyType) => {
  const parameter = _.omit(param, omit);
  return encodeURIComponent(`${url}_${uid}_${JSON.stringify(parameter)}`);
};

// validate fetch cache
export const removeCache = (cacheKey: string) =>
  Fetch.removeCache({ cacheKey });

export const getBaseRequest = ({
  sourceFrom,
  channelType = 7,
  requestId,
  parentRequestId = '',
  invokeFrom = '',
  site = 'cn',
  language = 'cn',
  locale = 'zh_cn',
  currencyCode = 'CNY',
  sourceCountryId,
  channelId,
  clientVersion,
  vid,
  patternType = '34',
  productCategoryID = 34,
  allianceId,
  sId,
  extraTags,
  extMap,
  extraMaps,
}: baseRequestType) => {
  const { latitude, longitude }: any = Device.deviceInfo || {};

  return {
    sourceFrom,
    channelType,
    requestId,
    parentRequestId,
    invokeFrom,
    site,
    language,
    locale,
    currencyCode,
    sourceCountryId,
    channelId,
    clientVersion,
    clientid: _.get(Device, 'deviceInfo.clientID') || '',
    vid,
    patternType,
    productCategoryID,
    mobileInfo: {
      customerGPSLat: Number(latitude) || 0,
      customerGPSLng: Number(longitude) || 0,
      mobileModel: `${Device.deviceType}`,
      wirelessVersion: `${Application.version}`,
    },
    allianceInfo: {
      allianceId,
      ouid: '1',
      sId,
      distributorUID: '1',
    },
    extraTags,
    extMap,
    extraMaps,
  };
};
