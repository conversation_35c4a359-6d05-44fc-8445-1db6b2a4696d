export interface AllianceInfoType {
  AllianceId?: number;
  Ouid?: string;
  Sid?: number;
  DistributorOrderId?: string;
  DistributorUID?: string;
  DistributorChannelId?: string;
}

export interface MobileInfoType {
  CustomerGPSLat?: number;
  CustomerGPSLng?: number;
  MobileModel?: string;
  MobileSN?: string;
  CustomerIP?: string;
  WirelessVersion?: string;
}

export interface ExtraTagsType {
  key?: string;
}

export interface BaseRequestType {
  sourceFrom?: string;
  requestId?: string;
  parentRequstId?: string;
  channelId?: number;
  AllianceInfo?: AllianceInfoType;
  locale?: string;
  currencyCode?: string;
  MobileInfo?: MobileInfoType;
  sourceCountryId?: number;
  site?: string;
  language?: string;
  sessionId?: string;
  invokeFrom?: number;
  uid?: string;
  patternType?: number;
  clientId?: string;
  vid?: string;
  extraTags?: ExtraTagsType;
}

export interface ExtensionType {
  name?: string;
  value?: string;
}

export interface HeadType {
  syscode?: string;
  lang?: string;
  auth?: string;
  cid?: string;
  ctok?: string;
  cver?: string;
  sid?: string;
  extension?: Array<ExtensionType>;
  sauth?: string;
}

export interface CancelAuthRequestType {
  baseRequest?: BaseRequestType;
  head?: HeadType;
  type?: number;
  orderId?: number;
  billNo?: string;
  status?: number;
}
