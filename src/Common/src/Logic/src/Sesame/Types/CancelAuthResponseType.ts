export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface ExtensionType {
  Id?: string;
  Version?: string;
  ContentType?: string;
  Value?: string;
}

export interface ResponseStatusType {
  Timestamp?: Date;
  Ack?: string;
  Errors?: Array<ErrorsType>;
  Build?: string;
  Version?: string;
  Extension?: Array<ExtensionType>;
}

export interface CancelAuthResponseType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
}
