# @ctrip/bbk-logic/List

> Bbk logic repositories.

## Snapshot

## Provider

`getProductGroups`  
`getProductGroupsAndCount`  
`FilterCalculater`

## Usage

```js
import {
  getProductGroups,
  getProductGroupsAndCount,
  FilterCalculater,
} from '@ctrip/bbk-logic';

const productGroups = getProductGroups();

const { productGroups, vehiclesCount, pricesCount } =
  getProductGroupsAndCount();

// faster calculate
const { vehiclesCount, pricesCount } = FilterCalculater();
```

## Props

| Property      | PropType              | Required | Default Value |
| ------------- | --------------------- | -------- | ------------- |
| productGroups | ProductGroupsType[]   | true     | -             |
| group         | ProductGroupsType     | false    | -             |
| filterItems   | FilterMenuItemsType[] | false    | -             |
| activeFilters | FilterType            | false    | -             |
