import { fetch, Fetch, Util } from '@ctrip/crn';
import _ from 'lodash';
import fetchLatest from '../../LatestFetch';
import {
  CarFetchType,
  HooksType,
  HooksName,
  HooksParamsType,
  TimmerType,
} from './FetchTypes';
import { CACHE_FROM, REQUEST_PARAMS_MONITOR_TYPES } from './Constants';
import {
  fetchByCache,
  saveResponseToCache,
  isNativeCache,
  wrapperResponse,
  getEnvType,
  getRequestUrl,
  getCacheParams,
  wrapperRequest,
  fetchByCacheSync,
} from './FetchCoreOption';
import FetchUtil from './FetchUtil';

const fetchPromise = async (url: string, params: any): Promise<any> =>
  new Promise((resolve, reject) => {
    fetch(url, params)
      .then((result: any) => resolve(result))
      .catch((error: any) => reject(error));
  });

class FetchCore {
  static envType = null;

  static testid = '';

  static mock = '';

  // 请求队列
  static fetchArray = {};

  timeLoggerMetric =
    (timmer: TimmerType) => (names: Array<string> | string) => {
      if (!names || names.length === 0) return;

      const eventNames = Array.isArray(names) ? names : [names];

      eventNames.forEach(name => {
        if (timmer[name] === 0 || !timmer[name]) {
          timmer[name] = new Date().getTime();
        }

        if (name === HooksName.faild) {
          timmer.afterFetch = new Date().getTime();
        }
      });
    };

  hooksEvent =
    (hooks: HooksType) =>
    (names: Array<string> | string, params: HooksParamsType) => {
      if (!names || names.length === 0) return;

      const eventNames = Array.isArray(names) ? names : [names];

      eventNames.forEach(name => {
        if (hooks && hooks[name] && typeof hooks[name] === 'function') {
          hooks[name](params);
        }
      });
    };

  fetchCoreAsync = async (request: CarFetchType, options: any = {}) => {
    const { url, hooks } = request;
    const timmer: TimmerType = {
      beforeEnvironment: 0,
      afterEnvironment: 0,
      beforeCacheFetch: 0,
      afterCacheFetch: 0,
      beforeFetch: 0,
      afterFetch: 0,
      beforeSetCache: 0,
      afterSetCache: 0,
    };
    const hooksCallback = this.hooksEvent(hooks);
    const timeMetric = this.timeLoggerMetric(timmer);

    // 构建请求参数开始
    timeMetric(HooksName.beforeEnvironment);

    FetchCore.envType = FetchCore.envType || (await FetchUtil.getEnvType());

    const requestUrl = getRequestUrl(
      url,
      FetchCore.envType,
      undefined,
      FetchCore.testid,
      FetchCore.mock,
    );

    const cachePolicy = getCacheParams(request);

    const params = wrapperRequest(request, cachePolicy);

    // 2022-10-11 添加请求参数过滤无效值处理
    params.body = this.filterInvalidParameters(
      request,
      params.body,
      hooksCallback,
    );

    // 2022-11-17 添加关键请求参数缺失监控
    this.verifyRequestParameters(request, hooksCallback);

    // 构建请求参数结束
    timeMetric(HooksName.afterEnvironment);

    if (cachePolicy.enableCache) {
      // 缓存请求开始
      timeMetric(HooksName.beforeCacheFetch);

      let cacheRes = null;
      if (Util.isInChromeDebug) {
        cacheRes = await fetchByCache(
          request.verifyResponseIsValid,
          cachePolicy,
        );
      } else {
        cacheRes = fetchByCacheSync(request.verifyResponseIsValid, cachePolicy);
      }

      // 缓存请求结束
      // 缓存命中 || 缓存未命中
      timeMetric(HooksName.afterCacheFetch);

      if (cacheRes && cacheRes.isCacheValid) {
        const response = wrapperResponse({
          response: cacheRes.response,
          timmer,
          isFromCache: true,
          isCacheValid: true,
          cacheFrom: CACHE_FROM.MEMOREY,
          cacheKey: cachePolicy.cacheKey,
          groupId: cachePolicy.groupId,
          uniqRequestKey: _.get(params, 'body.uniqRequestKey'),
        });
        hooksCallback(HooksName.done, { request, response });
        return response;
      }
    }

    try {
      // 网络请求开始
      timeMetric(HooksName.beforeFetch);

      // 当前请求的sequenceId
      FetchCore.fetchArray[requestUrl] = params.sequenceId;

      let fetchRes;
      if (options?.latest) {
        fetchRes = await fetchLatest(requestUrl, params, options);
      } else {
        fetchRes = await fetchPromise(requestUrl, params);
      }

      if (
        FetchCore.fetchArray[requestUrl] &&
        params.sequenceId &&
        FetchCore.fetchArray[requestUrl] !== params.sequenceId
      ) {
        return {
          canceled: true,
        };
      }

      // 网络请求结束
      timeMetric(HooksName.afterFetch);

      if (cachePolicy.enableCache) {
        // 设置缓存开始
        timeMetric(HooksName.beforeSetCache);

        saveResponseToCache(
          request.verifyResponseIsValid,
          cachePolicy,
          fetchRes,
        );

        // 设置缓存结束
        timeMetric(HooksName.afterSetCache);
      }

      const { isFromNativeCache, isCacheValid } = isNativeCache(
        fetchRes,
        request.verifyResponseIsValid,
      );

      if (
        cachePolicy.enableCache &&
        !isCacheValid &&
        Fetch &&
        Fetch.removeCache
      ) {
        Fetch.removeCache({ cacheKey: cachePolicy.cacheKey });
      }

      const response = wrapperResponse({
        response: fetchRes,
        timmer,
        isFromCache: isFromNativeCache,
        isCacheValid,
        cacheFrom: isFromNativeCache ? CACHE_FROM.NATIVE : '',
        cacheKey: cachePolicy.cacheKey,
        groupId: cachePolicy.groupId,
        uniqRequestKey: _.get(params, 'body.uniqRequestKey'),
      });

      // 2023-02-17 添加请求参数和前端参数不一致监控
      this.verifyParametersCoincident(request, response, hooksCallback);

      hooksCallback(HooksName.done, { request, response, options });
      return response;
    } catch (error) {
      timeMetric(HooksName.faild);
      hooksCallback(HooksName.faild, { request, error, options });
      throw error;
    }
  };

  // 验证调用接口参数是否正确（eg: 下单接口缺少用户信息)
  verifyRequestParameters = (
    request: CarFetchType,
    hooksCallback: Function,
  ) => {
    const { verifyRequestParameters } = request;
    if (verifyRequestParameters && !verifyRequestParameters()) {
      hooksCallback(HooksName.paramValidateFail, {
        request,
        type: REQUEST_PARAMS_MONITOR_TYPES.KEY_PARAMETERS_MISSING,
      });
    }
  };

  // 验证请求参数和前端参数是否一致
  verifyParametersCoincident = (
    request: CarFetchType,
    response,
    hooksCallback: Function,
  ) => {
    const { verifyParametersCoincident } = request;
    if (
      verifyParametersCoincident &&
      !verifyParametersCoincident(response?.requestInfo)
    ) {
      hooksCallback(HooksName.paramValidateFail, {
        request,
        type: REQUEST_PARAMS_MONITOR_TYPES.REQUEST_PARAMETERS_INCONSISTENT,
      });
    }
  };

  // 请求参数过滤无效值处理
  filterInvalidParameters = (
    request: CarFetchType,
    body: any,
    hooksCallback: Function,
  ): Object => {
    if (Object.values(body).findIndex(FetchUtil.valIsInvalid) > -1) {
      hooksCallback(HooksName.paramValidateFail, {
        request,
        type: REQUEST_PARAMS_MONITOR_TYPES.INCLUDE_INVALID_VALUE,
      });
      return FetchUtil.filterParam(body);
    }
    return body;
  };
}

export default FetchCore;
