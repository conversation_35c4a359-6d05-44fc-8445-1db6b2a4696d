import { Storage, Util } from '@ctrip/crn';
import {
  DOMAIN_ID,
  CACHE_TIME,
  CACHE_MAX_LENGTH,
  CACHE_FROM,
} from './Constants';

const MAX_LENGTH = 2;
const STORAGE_MEMO = new Map<string, Array<any>>();

export interface MemorizeCacheSetType {
  key: string;
  groupId: string;
  expireTime?: number;
  isSaveStorage?: boolean;
  data: any;
  cacheTime?: Date;
}

export interface MemorizeCacheGetType {
  key: string;
  groupId: string;
  isFromStorage?: boolean;
  isSyncMemory?: boolean;
  isLoadStorageThenRemove?: boolean;
}

class MemorizeCache {
  static getCacheSync(param: MemorizeCacheGetType) {
    const { key, groupId } = param;
    let cacheData = null;
    const cache = STORAGE_MEMO.get(groupId);
    if (cache) {
      const temp = cache.find(f => f.key === key);
      if (temp) {
        const { cacheTime, expireTime, data } = temp;
        if (!MemorizeCache.isExpire(cacheTime, expireTime)) {
          cacheData = data;
        }
      }
    }
    return cacheData;
  }

  static async getCache(param: MemorizeCacheGetType): Promise<any> {
    let cacheData = MemorizeCache.getCacheSync(param);

    const {
      key,
      groupId,
      isFromStorage,
      isSyncMemory = true,
      isLoadStorageThenRemove,
    } = param;

    // 判断是否从Storage中获取
    if (!cacheData && isFromStorage) {
      let value = '';
      if (Util.isInChromeDebug) {
        // @ts-ignore
        value = await Storage.loadPromise({ key, domain: DOMAIN_ID });
      } else {
        value = Storage.loadSync({ key, domain: DOMAIN_ID });
      }
      if (isLoadStorageThenRemove) {
        Storage.remove({ key, domain: DOMAIN_ID });
      }

      if (value) {
        try {
          cacheData = JSON.parse(value);

          // 同步至内存缓存
          if (isSyncMemory) {
            MemorizeCache.setCache({
              key,
              groupId,
              expireTime: CACHE_TIME,
              data: cacheData,
            });
          }
        } catch (error) {}
      }
    }
    return new Promise(resolve => {
      resolve(cacheData);
    });
  }

  static getCacheDataSync(param) {
    let cacheData = MemorizeCache.getCacheSync(param);
    const {
      key,
      groupId,
      isFromStorage,
      isSyncMemory = true,
      isLoadStorageThenRemove,
    } = param;
    // 判断是否从Storage中获取
    if (!cacheData && isFromStorage) {
      let value = Storage.loadSync({ key, domain: DOMAIN_ID });
      if (isLoadStorageThenRemove) {
        Storage.remove({ key, domain: DOMAIN_ID });
      }
      if (value) {
        try {
          cacheData = JSON.parse(value);
          // 同步至内存缓存
          if (isSyncMemory) {
            MemorizeCache.setCache({
              key,
              groupId,
              expireTime: CACHE_TIME,
              data: cacheData,
            });
          }
        } catch (error) {}
      }
    }

    return cacheData;
  }

  static setCache(cacheData: MemorizeCacheSetType) {
    const { key, groupId, expireTime, isSaveStorage, data } = cacheData;

    const expires = expireTime || CACHE_TIME;

    const item = {
      key,
      data,
      isSaveStorage,
      cacheTime: new Date(),
      expireTime: expires,
    };

    const cache = STORAGE_MEMO.get(groupId);
    if (!cache) {
      STORAGE_MEMO.set(groupId, [item]);
    } else {
      const idx = cache.findIndex(f => f.key === key);
      if (idx > -1) {
        cache.splice(idx, 1);
      }
      if (cache.length >= MAX_LENGTH) {
        cache.splice(0, 1);
      }
      cache.push(item);
      STORAGE_MEMO.set(groupId, cache);
    }

    if (isSaveStorage) {
      const value = typeof data === 'string' ? data : JSON.stringify(data);
      Storage.save({ key, value, expires, domain: DOMAIN_ID });
    }

    MemorizeCache.isCacheAvailable();
  }

  static removeCache(
    groupId: string,
    key: string,
    isSaveStorage: boolean = false,
  ) {
    if (groupId) {
      const data = STORAGE_MEMO.get(groupId);
      if (data && data.length > 0) {
        data.forEach((item, index) => {
          if (key === item.key) {
            data.splice(index, 1);
          }
        });
        if (data.length > 0) {
          STORAGE_MEMO.set(groupId, data);
        } else {
          STORAGE_MEMO.delete(groupId);
        }
      } else {
        STORAGE_MEMO.delete(groupId);
      }
      if (isSaveStorage) {
        Storage.remove({ key, domain: DOMAIN_ID });
      }
    }
  }

  // private
  static isExpire(cacheTime: Date, expireTime: number, now?: Date) {
    const current = now || new Date();
    if (isNaN(expireTime)) return true;

    if (current.getTime() > cacheTime.getTime() + expireTime * 1000) {
      return true;
    }

    return false;
  }

  // private
  static isCacheAvailable() {
    STORAGE_MEMO.forEach((item, groupId) => {
      item.forEach((data, index) => {
        const { key, cacheTime, expireTime, isSaveStorage } = data;
        if (MemorizeCache.isExpire(cacheTime, expireTime)) {
          MemorizeCache.removeCache(groupId, key, isSaveStorage);
        }
      });
    });

    if (STORAGE_MEMO.size > CACHE_MAX_LENGTH) {
      let zeroId = null;
      STORAGE_MEMO.forEach((value, groupId) => {
        if (!zeroId) zeroId = groupId;
      });
      STORAGE_MEMO.delete(zeroId);
    }
  }
}

export { MemorizeCache };
export default MemorizeCache;
