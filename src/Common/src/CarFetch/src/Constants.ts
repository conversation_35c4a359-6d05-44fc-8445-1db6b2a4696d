export const DOMAIN_ID = 'rn_car_app';
export const CACHE_TIME = 60 * 5; // second
export const CACHE_LOCATION = 'MEMO';
export const CACHE_MAX_LENGTH = 15;
export const FETCH_TIMEOUT = 30; // second
export const FETCH_METHOD = 'post';
export const REST_SOA = 'restapi/soa2';

export const CACHE_FROM = {
  MEMOREY: 'memorey',
  NATIVE: 'native',
};

export const ENV_TYPE = {
  FAT: 'fat',
  UAT: 'uat',
  BATTLE: 'battle',
  PROD: 'prd',
  SMOCK: 'smock', // 远程mock服务
};

export const DOMAIN_URL = {
  [ENV_TYPE.FAT]: 'gateway.m.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.UAT]: 'gateway.m.uat.qa.nt.ctripcorp.com',
  [ENV_TYPE.BATTLE]: 'gateway.m.fws.qa.nt.ctripcorp.com',
  [ENV_TYPE.PROD]: 'm.ctrip.com',
  [ENV_TYPE.SMOCK]: 'offline.fx.fws.qa.nt.ctripcorp.com', // 云函数mock服务测试地址
};

// 请求参数监控类型
export const REQUEST_PARAMS_MONITOR_TYPES = {
  INCLUDE_INVALID_VALUE: 'INCLUDE_INVALID_VALUE', // 包含无效值
  KEY_PARAMETERS_MISSING: 'KEY_PARAMETERS_MISSING', // 关键参数缺失
  REQUEST_PARAMETERS_INCONSISTENT: 'REQUEST_PARAMETERS_INCONSISTENT', // 请求参数不一致
  IS_DOMESTIC_APP_TYPE_INCONSISTENT: 'IS_DOMESTIC_APP_TYPE_INCONSISTENT', // isDomestic和appType不一致
};
