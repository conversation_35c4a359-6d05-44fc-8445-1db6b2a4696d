import MemorizeCache from './MemorizeCache';
import { CachePolicyType } from './FetchTypes';
import { TimmerType, ResponseMapType, CarFetchType } from './FetchTypes';
import {
  REST_SOA,
  CACHE_FROM,
  CACHE_LOCATION,
  FETCH_METHOD,
  FETCH_TIMEOUT,
  ENV_TYPE,
} from './Constants';
import FetchUtil from './FetchUtil';
import getCachePolicy from './CachePolicy';

export const isResponseValid = (
  verifyResponseIsValid: Function,
  response: any,
) => {
  if (verifyResponseIsValid) {
    return verifyResponseIsValid(response);
  }
  return true;
};

export const fetchByCache = async (
  verifyResponseIsValid: Function,
  cache: CachePolicyType,
) => {
  let isCacheValid = false;
  let response = null;

  if (cache.enableCache) {
    response = await MemorizeCache.getCache({
      key: cache.cacheKey,
      groupId: cache.groupId,
      isFromStorage: cache.isFromStorage,
      isLoadStorageThenRemove: cache.isLoadStorageThenRemove,
    });

    if (verifyResponseIsValid && response) {
      isCacheValid = isResponseValid(verifyResponseIsValid, response);
    }

    // 移除无效缓存
    if (response && !isCacheValid) {
      MemorizeCache.removeCache(cache.groupId, cache.cacheKey);
    }
  }

  return { isCacheValid, response };
};

export const fetchByCacheSync = (verifyResponseIsValid, cache) => {
  let isCacheValid = false;
  let response = null;
  if (cache.enableCache) {
    response = MemorizeCache.getCacheDataSync({
      key: cache.cacheKey,
      groupId: cache.groupId,
      isFromStorage: cache.isFromStorage,
      isLoadStorageThenRemove: cache.isLoadStorageThenRemove,
    });
    if (verifyResponseIsValid && response) {
      isCacheValid = isResponseValid(verifyResponseIsValid, response);
    }
    // 移除无效缓存
    if (response && !isCacheValid) {
      MemorizeCache.removeCache(cache.groupId, cache.cacheKey);
    }
  }
  return { isCacheValid, response };
};

export const saveResponseToCache = async (
  verifyResponseIsValid: Function,
  cachePolicy: CachePolicyType,
  response: any,
) => {
  if (cachePolicy.enableCache && response) {
    let isCacheValid = true;

    if (verifyResponseIsValid) {
      isCacheValid = isResponseValid(verifyResponseIsValid, response);
    }

    if (isCacheValid) {
      MemorizeCache.setCache({
        key: cachePolicy.cacheKey,
        groupId: cachePolicy.groupId,
        data: response,
        expireTime: cachePolicy.cacheExpireTime,
        isSaveStorage: cachePolicy.isSaveStorage,
      });
    }
  }
};

export const wrapperResponse = ({
  response,
  timmer,
  isFromCache,
  isCacheValid,
  cacheFrom,
  cacheKey,
  groupId,
  uniqRequestKey,
}: {
  response: any;
  timmer: TimmerType;
  isFromCache: boolean;
  isCacheValid: boolean;
  cacheFrom?: string;
  cacheKey: string;
  groupId: string;
  uniqRequestKey: string;
}) => {
  if (!response) return response;

  const {
    beforeEnvironment,
    afterEnvironment,
    beforeCacheFetch,
    afterCacheFetch,
    beforeFetch,
    afterFetch,
    beforeSetCache,
    afterSetCache,
  } = timmer;

  const environmentCost = afterEnvironment - beforeEnvironment;
  const cacheFetchCost = afterCacheFetch - beforeCacheFetch;
  const fetchCost = afterFetch - beforeFetch;
  const setCacheCost = afterSetCache - beforeSetCache;
  const networkCost =
    isFromCache && cacheFrom === CACHE_FROM.MEMOREY
      ? cacheFetchCost
      : fetchCost;
  const appResponseMap: ResponseMapType = {
    isFromCache,
    isCacheValid,
    cacheKey,
    groupId,
    networkCost,
    environmentCost,
    cacheFetchCost,
    fetchCost,
    setCacheCost,
    cacheFrom,
    uniqRequestKey,
    beforeFetch,
    afterFetch,
  };
  // 解决response已有appResponseMap造成的error
  response = {
    ...response,
    appResponseMap: {
      ...response.appResponseMap,
      ...appResponseMap,
    },
  };
  return response;
};

export const isNativeCache = (
  response: any,
  verifyResponseIsValid: Function,
) => {
  let isFromNativeCache = false;
  /* eslint-disable dot-notation */
  if (response && response['__isFromCache']) {
    isFromNativeCache = true;
  }
  let isCacheValid = true;
  if (response) {
    isCacheValid = isResponseValid(verifyResponseIsValid, response);
  }
  return { isFromNativeCache, isCacheValid };
};

export const wrapperRequest = (
  request: CarFetchType,
  cachePolicy: CachePolicyType,
) => {
  const { params } = request;
  if (cachePolicy && cachePolicy.enableCache) {
    params.cachePolicy = {
      enableCache: true,
      cacheKey: cachePolicy.cacheKey,
      cacheExpireTime: cachePolicy.cacheExpireTime,
      cacheLocation: cachePolicy.cacheLocation || CACHE_LOCATION,
    };
  }
  if (!params.body) {
    params.body = FETCH_METHOD;
  }
  if (!params.timeout) {
    params.timeout = FETCH_TIMEOUT;
  }
  return params;
};

export const getEnvType = async (envType: string) => {
  if (envType) return envType;
  return await FetchUtil.getEnvType();
};

export const getDomainURL = (env: string) => FetchUtil.getDomainURL(env);

export const getRequestUrl = (
  url: string,
  env: string,
  protocol: string = 'https',
  testid: string,
  mock: string,
) => {
  if (url.startsWith('http')) return url;
  const domain = getDomainURL(env);
  if (env === ENV_TYPE.SMOCK) {
    // 去除已有url上的参数
    const paramStart = url?.indexOf('?');
    const soaUrl = paramStart > -1 ? url?.substring(0, paramStart) : url;
    return `http://${domain}/soa2/26037/mock?path=${soaUrl}${
      testid ? `&testid=${testid}` : ''
    }${mock ? `&mock=${mock}` : ''}`;
  }
  return `${protocol}://${domain}/${REST_SOA}/${url}`;
};

export const getCacheParams = (request: CarFetchType) => {
  const { url, uid, cachePolicy, params } = request;
  return getCachePolicy({
    cachePolicy,
    url,
    uid,
    paramBody: params.body,
  });
};
