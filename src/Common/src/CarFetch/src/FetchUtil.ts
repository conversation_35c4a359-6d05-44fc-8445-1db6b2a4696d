import { Env, Util, Storage, User } from '@ctrip/crn';
import _ from 'lodash';
import { BbkPlatform } from '../../Utils';
import { ENV_TYPE, DOMAIN_URL, DOMAIN_ID } from './Constants';
class Utils {
  static getEnvType = BbkPlatform.getEnvType;

  static getDomainURL(env: string): string {
    return DOMAIN_URL[env] || DOMAIN_URL[ENV_TYPE.PROD];
  }

  static valIsInvalid = value =>
    ['undefined', 'null', 'unknown', 'UNKNOW'].includes(value);

  static filterParam = (param: Object) => _.omitBy(param, Utils.valIsInvalid);
}

export default Utils;
