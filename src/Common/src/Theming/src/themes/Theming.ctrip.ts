import { color, tokenType } from '../../../Tokens';

export default {
  backgroundColor: '#FFFFFF',
  bbkTextColor: '#333333',
  bbkGroupNameColor: '#287DFA',
  bbkGroupNameBackgroundColor: '#E9F2FF',
  bbkGroupNameHotLabelColor: '#FD554A',
  horizontalNavSelectedTextColor: color.blueBase,
  horizontalNavSelectedIndicatorColor: '#0086F6',
  bbkSearchPanelBorderColor: '#DADFE6',
  bbkInsuranceSliderBorderColor: '#287DFA',
  bbkInsuranceSliderShadowColor: 'rgba(40, 125, 250, 0.08)',
  bbkInsuranceSliderItemHightColor: '#287DFA',
  /**
   * List
   */
  vendorDescLowColor: color.fontSubDark,
  vendorDescColor: color.blueBase,
  scrollBackgroundColor: color.grayBg,
  blueBase: color.blueBase,
  black: color.black,
  grayBorder: color.grayBorder,
  blueBg: color.blueBg,
  orangePrice: color.orangePrice,
  fontSubLight: color.fontSubLight,
  redBorder: color.redBorder,
  /**
   * Vehicle
   */
  bbkPriceDescPricePrimaryColor: color.blueBase,
  bbkVehicleNameBorderBottomColor: color.black,
  bbkVehicleDescSplitLineColor: null,
  /**
   * Label & Button
   */
  [tokenType.ColorType.Red]: {
    labelColor: color.white,
    labelBgColor: color.redBg,
  },
  [tokenType.ColorType.Orange]: {
    labelColor: color.white,
    labelBgColor: color.orangePrice,
  },
  /**
   * OrderDetailNps
   */
  bbkNpsBgColor: color.white,
  bbkNpsTitleColor: color.fontPrimary,
  bbkNpsWillStatusColor: color.blueGrayBase,
  bbkNpsPointNumColor: color.mapTabSelectdColor,
  bbkNpsPointNumBgColor: color.blueBgSecondary,
  bbkNpsSelectColor: color.blueBase,
  bbkNpsInputBg: '#F5F7FA',
};
