import _ from 'lodash';
import { color } from '../../Tokens';

const {
  grayBase: colorSkyGrayTint07,
  grayBase: colorSkyGrayTint06,
  grayBase: colorSkyGrayTint04,
  grayBase: colorSkyGrayTint02,
  grayBase: colorSkyGrayTint01,
  grayBase: colorSkyGray,
} = color;

const grays = {
  colorSkyGrayTint07,
  colorSkyGrayTint06,
  colorSkyGrayTint04,
  colorSkyGrayTint02,
  colorSkyGrayTint01,
  colorSkyGray,
};

export interface Props {
  theme: any;
}

export const isValidTheme = (
  requiredAttributes: Array<string>,
  theme: Object,
): boolean =>
  requiredAttributes.reduce(
    (valid, attribute) =>
      _.has(theme, attribute) &&
      (_.isBoolean(theme[attribute]) ||
        _.isNumber(theme[attribute]) ||
        !_.isEmpty(theme[attribute])) &&
      valid,
    true,
  );

export const makeThemePropType =
  (requiredAttributes: Array<string>) =>
  (props: Props, propName: string, componentName: string): Error | boolean => {
    const { theme } = props;
    if (!theme) {
      return false;
    }

    const validTheme = isValidTheme(requiredAttributes, theme);

    if (!validTheme) {
      return new Error(
        `Invalid prop \`${propName}\` supplied to \`${componentName}\`. When supplying \`theme\` all the required theming attributes(\`${requiredAttributes.join(
          ', ',
        )}\`) must be supplied.`,
      ); // eslint-disable-line max-len
    }
    return false;
  };

export const getThemeAttributes = (
  requiredAttributes: Array<string>,
  theme: Object,
  optionalAttributes?: Array<string>,
): Object => {
  if (!theme) {
    return null;
  }

  if (theme && !isValidTheme(requiredAttributes, theme)) {
    return null;
  }

  const filteredRequiredAttributes = requiredAttributes
    ? requiredAttributes.reduce((result, attribute) => {
        if (theme) {
          result[attribute] = theme[attribute]; // eslint-disable-line no-param-reassign
        }
        return result;
      }, {})
    : null;

  const filteredOptionalAttributes = optionalAttributes
    ? optionalAttributes.reduce((result, attribute) => {
        if (theme) {
          result[attribute] = theme[attribute]; // eslint-disable-line no-param-reassign
        }
        return result;
      }, {})
    : null;

  return { ...filteredRequiredAttributes, ...filteredOptionalAttributes };
};
