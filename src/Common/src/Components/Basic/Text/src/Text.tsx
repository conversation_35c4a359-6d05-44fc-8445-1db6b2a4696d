import React from 'react';
import { TextStyle } from 'react-native'; // @ts-ignore

import { CRNText } from '@ctrip/crn';
import { color, icon, font } from '../../../../Tokens';
import { BbkUtils } from '../../../../Utils';
import { withTheme } from '../../../../Theming';
enum Type {
  ICON = 'icon',
}
type IText = {
  type: Type;
  theme: any;
  style: TextStyle;
  iconVersion?: 'v2';
  children: React.ReactElement;
  smallFontStyle?: any;
};
const BbkComponentText: React.FC<IText> = props => {
  const { type, children, style } = props;
  const textStyle = [
    {
      color: color.fontPrimary,
    },
    style,
    {
      fontFamily: icon.getIconFamily(),
    },
  ];
  if (type && type === Type.ICON && typeof children === 'string') {
    let code = children.replace(/&#x([0-9a-fA-F]+);/g, function (all, hex) {
      return String.fromCodePoint(parseInt(hex, 0x10));
    });
    return (
      <CRNText {...props} style={textStyle}>
        {BbkUtils.htmlDecode(code)}
      </CRNText>
    );
  }
  return (
    <CRNText
      {...props}
      style={[font.body3LightStyle, { color: color.fontPrimary }, style]}
    />
  );
};

export default withTheme(BbkComponentText);
