import React, { useEffect, useState, ReactElement, Fragment } from 'react';
import BbkComponentText from '../../Text';
import Sign, { SignName, SignData } from './Sign';
import { TextStyle } from 'react-native';

type SignBack = {
  signed: string;
  sign: string;
  localeData: string;
};
const useSign: (type: SignName, data: string[]) => SignBack = (type, data) => {
  data = data.filter(v => v);
  const [sign, setSign] = useState('');
  const localeData = null;
  useEffect(() => {
    const sign = new Sign((localeData && localeData.locale) || 'zh-cn');
    setSign(sign[type]);
  }, [type, localeData]);
  if (!data || !data.length || !sign) return null;
  return { signed: data.join(sign), sign, localeData };
};

type ISignText = {
  type: SignName;
  data?: Array<string | ReactElement>;
  style?: any;
};
const SignText: React.FC<ISignText> = ({ type, data = [], style }) => {
  data = data.filter(v => v);
  const localeData = null;
  const [sign, setSign] = useState('');
  useEffect(() => {
    const sign = new Sign((localeData && localeData.locale) || 'zh-cn');
    setSign(sign[type]);
  }, [type, localeData]);
  if (!data.length) {
    return <BbkComponentText style={style}>{sign}</BbkComponentText>;
  }
  return (
    <Fragment>
      {data.reduce((memo, item, index) => {
        if (typeof item === 'string') {
          return (
            <BbkComponentText>
              {memo}
              <BbkComponentText>
                {item}
                {sign}
              </BbkComponentText>
            </BbkComponentText>
          );
        }
        return (
          <BbkComponentText>
            {memo}
            {item}
            {index !== data.length - 1 && (
              <BbkComponentText>{sign}</BbkComponentText>
            )}
          </BbkComponentText>
        );
      }, null)}
    </Fragment>
  );
};

export { useSign, SignName, SignData, SignText };
export default Sign;
