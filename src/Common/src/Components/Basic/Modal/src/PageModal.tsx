import React, { Component } from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  Animated,
  ViewStyle,
  Modal,
} from 'react-native';
import { CRNModal } from '@ctrip/crn';
import { color, druation } from '../../../../Tokens';
import { BbkUtils } from '../../../../Utils';
import { isHarmony } from '../../../../Utils/src/Utils';

interface IProps {
  visible: boolean;
  animateDuration?: number;
  location?: string;
  style?: ViewStyle;
  animateType?: string;
  useCRNModal?: boolean;
  onMaskPress?: () => void;
  modalTouchLayerStyle?: ViewStyle;
  closeModalBtnTestID?: string;
  testID?: string;
}

interface IState {
  modalVisible: boolean;
  slideupAnim: any;
  fadeAnim: any;
}

export default class PageModal extends Component<IProps, IState> {
  constructor(props) {
    super(props);
    this.state = {
      modalVisible: !!props.visible,
      slideupAnim: new Animated.Value(0),
      fadeAnim: new Animated.Value(0),
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (!!this.state.modalVisible === !!nextProps.visible) return;
    if (!nextProps.visible) {
      this.hideModal();
    } else {
      this.showModal();
    }
  }

  shouldComponentUpdate(nextProps, nextState) {
    return !!nextProps.visible === !!nextState.modalVisible;
  }

  getLocationStyle = (location?: string) => {
    let str;
    switch (location) {
      case 'top':
        str = 'flex-start';
        break;
      case 'center':
        str = 'center';
        break;
      case 'bottom':
        str = 'flex-end';
        break;
      default:
        str = 'flex-end';
    }
    return {
      justifyContent: str,
    };
  };

  getAnimateStyle = (animateType?: string) => {
    let animateStyle;
    switch (animateType) {
      case 'slideUp':
        animateStyle = this.slideUpAnimateStyle();
        break;
      case 'slideDown':
        animateStyle = this.slideDownAnimateStyle();
        break;
      case 'fade':
        animateStyle = this.fadeAnimateStyle();
        break;
      default:
        animateStyle = this.slideUpAnimateStyle();
    }
    return animateStyle;
  };

  fadeAnimateStyle = () => {
    return [
      {
        opacity: this.state.fadeAnim,
      },
    ];
  };

  slideUpAnimateStyle = () => {
    return [
      {
        opacity: this.state.fadeAnim,
        transform: [
          {
            translateY: this.state.slideupAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [BbkUtils.vh(100), 0],
            }),
          },
        ],
      },
    ];
  };

  slideDownAnimateStyle = () => {
    return [
      {
        opacity: this.state.fadeAnim,
        transform: [
          {
            translateY: this.state.slideupAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [-BbkUtils.vh(100), 0],
            }),
          },
        ],
      },
    ];
  };

  animate = (visible, callback?) => {
    const { animateDuration = druation.animationDurationSm } = this.props;
    const start = visible ? 0 : 1;
    const end = visible ? 1 : 0;
    this.state.fadeAnim.setValue(start);
    this.state.slideupAnim.setValue(start);
    Animated.parallel([
      Animated.timing(this.state.slideupAnim, {
        toValue: end,
        duration: animateDuration,
        useNativeDriver: true,
      }),
      Animated.timing(this.state.fadeAnim, {
        toValue: end,
        duration: animateDuration,
        useNativeDriver: true,
      }),
    ]).start(callback);
  };

  showModal = () => {
    if (!this.state.modalVisible) {
      this.setState({
        modalVisible: true,
      });
      this.animate(true);
    }
  };
  hideModal = () => {
    if (this.state.modalVisible) {
      this.animate(false, () => {
        this.setState({
          modalVisible: false,
        });
      });
    }
  };

  handleMaskPress = () => {
    const { onMaskPress } = this.props;
    if (typeof onMaskPress === 'function') {
      BbkUtils.ensureFunctionCall(onMaskPress);
    } else {
      this.hideModal();
    }
  };

  render() {
    const { modalVisible } = this.state;
    const {
      style,
      location,
      animateType,
      useCRNModal = false,
      modalTouchLayerStyle,
      closeModalBtnTestID,
      testID,
    } = this.props;
    const animationStyle = this.getAnimateStyle(animateType);
    const locStyle = this.getLocationStyle(location);
    if (!modalVisible) return null;

    const children = (
      <>
        <TouchableWithoutFeedback
          onPress={this.handleMaskPress}
          testID={closeModalBtnTestID}
        >
          <Animated.View
            style={[
              Style.modalTouchLayer,
              modalTouchLayerStyle,
              { opacity: this.state.fadeAnim },
            ]}
          />
        </TouchableWithoutFeedback>
        <Animated.View style={[Style.modalContent, animationStyle]}>
          {this.props.children}
        </Animated.View>
      </>
    );
    // http://pages.release.ctripcorp.com/crn/CRNDoc/chapter2/components/CRNModal.html
    // 强制modal全屏，并穿透状态栏
    const otherProps = {
      coverStatusBar: true,
    };
    const NewModal = isHarmony ? Modal : CRNModal;
    return useCRNModal ? (
      <NewModal visible={!!modalVisible} transparent {...otherProps}>
        <View testID={testID} style={[Style.crnModalWrap, locStyle]}>
          {children}
        </View>
      </NewModal>
    ) : (
      <View testID={testID} style={[Style.modalLayer, style, locStyle]}>
        {children}
      </View>
    );
  }
}

const Style = StyleSheet.create({
  modalLayer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    zIndex: 10,
    elevation: 10,
    overflow: 'hidden',
  },
  modalTouchLayer: {
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: color.blackTransparent,
  },
  modalContent: {
    backgroundColor: color.transparent,
  },
  crnModalWrap: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-end',
    overflow: 'hidden',
    backgroundColor: color.transparent,
  },
});
