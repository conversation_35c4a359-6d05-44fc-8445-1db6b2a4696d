import React, { useEffect, useState, useCallback } from 'react';
import {
  View,
  FlexStyle,
  Dimensions,
  Keyboard,
  NativeModules,
  LayoutAnimation,
  Platform,
} from 'react-native';

const clientHeight = Dimensions.get('window').height;
const { UIManager } = NativeModules;
UIManager?.setLayoutAnimationEnabledExperimental &&
  UIManager?.setLayoutAnimationEnabledExperimental(true);

type IKeyboardAvoidView = {
  style?: any;
};
const KeyboardAvoidView: React.FC<IKeyboardAvoidView> = ({
  style,
  children,
}) => {
  const [height, setHeight] = useState(null);
  useEffect(() => {
    let keyboardDidShowListener;
    let keyboardDidHideListener;
    const keyboardDidShow = frame => {
      LayoutAnimation.configureNext({
        duration: 300,
        update: {
          type: LayoutAnimation.Types.spring,
          springDamping: 0.8,
        },
      });
      if (Platform.OS === 'ios')
        setHeight(clientHeight - frame.endCoordinates.height);
    };
    keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      keyboardDidShow,
    );
    keyboardDidHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        setHeight(null);
      },
    );
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  let defaultStyle: FlexStyle = { flex: 1 };
  if (height) {
    defaultStyle = { height };
  }
  return <View style={[defaultStyle, style]}>{children}</View>;
};

export default KeyboardAvoidView;
