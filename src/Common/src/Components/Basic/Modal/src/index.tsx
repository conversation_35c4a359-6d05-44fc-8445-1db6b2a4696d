import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
  View,
  StyleSheet,
  TouchableWithoutFeedback,
  ModalProps,
  ViewStyle,
  Modal,
} from 'react-native'; // @ts-ignore no-export CRNModal

import { CRNModal } from '@ctrip/crn';
import * as Animatable from 'react-native-animatable';
import { BbkUtils } from '../../../../Utils';
import { color } from '../../../../Tokens';
import { withTheme, getThemeAttributes } from '../../../../Theming';
import KeyboardAvoidView from './KeyboardAvoidView';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import {
  AnimationPresets,
  getLocationStyle,
  LocationType,
} from './animationPreset';
import { isHarmony } from '../../../../Utils/src/Utils';

const { selector, vh } = BbkUtils;

export interface IBbkComponentModal extends ModalProps {
  animationInType: string;
  animationInDuration: number;
  animationOutType: string;
  animationOutDuration: number;
  isMask: boolean;
  modalVisible: boolean;
  animation: boolean;
  maskHide: boolean;
  location: LocationType;
  style?: ViewStyle;
  styleInner?: ViewStyle;
  theme?: any;
}
const BbkComponentModal: React.FC<IBbkComponentModal> = ({
  modalVisible,
  isMask = true,
  maskHide = true,
  onRequestClose = () => {},
  location = LocationType.bottom,
  animation = true,
  animationInType = 'fadeInUp',
  animationInDuration = 400,
  animationOutType = 'fadeOutDown',
  animationOutDuration = 200,
  children,
  theme,
  style,
  styleInner,
  ...passthroughProps
}) => {
  const themes = getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any;
  const backgroundColor =
    (themes && themes.bbkModalMaskColor) || color.blackTransparent;

  const [visible, setVisible] = useState(false);
  const animatableViewRef = useRef(null);
  const type = selector(modalVisible, animationInType, animationOutType);

  const ModalWrapper = selector(isHarmony, Modal, CRNModal);

  const duration = selector(
    modalVisible,
    animationInDuration,
    animationOutDuration,
  );
  const animate = useCallback(() => {
    if (
      !animation ||
      !animatableViewRef ||
      !animatableViewRef.current ||
      !animatableViewRef.current[type]
    )
      return;
    animatableViewRef.current[type](duration);
  }, [modalVisible, animation]);
  useEffect(() => {
    if (modalVisible) {
      setVisible(true);
    } else {
      animate();
      setTimeout(() => {
        setVisible(false);
      }, duration);
    }
  }, [modalVisible]);
  useEffect(() => {
    if (visible) {
      animate();
    }
    // [FIX] #国内# #境外# #首页# 新首页的时间选择控件会点击失效
    // http://iwork.ctripcorp.com/sync/opencard/2567/4904/15002/609044
    if (modalVisible && !visible) {
      setVisible(true);
    }
  }, [visible]);
  const onPressMask = useCallback(() => {
    if (maskHide) {
      // @ts-ignore upgrade 072
      onRequestClose();
    }
  }, [maskHide]);
  return (
    <ModalWrapper
      transparent
      animationType="fade"
      onRequestClose={onRequestClose}
      {...passthroughProps}
      visible={visible}
      style={style}
      statusBarTranslucent={true}
    >
      <View
        style={[
          StyleSheet.absoluteFillObject,
          selector(isMask, { backgroundColor }),
        ]}
      >
        <View style={[styles.modalWrapper]}>
          <KeyboardAvoidView
            style={[styles.modalContainer, getLocationStyle(location)]}
          >
            <TouchableWithoutFeedback onPress={onPressMask}>
              <View style={StyleSheet.absoluteFillObject} />
            </TouchableWithoutFeedback>
            <Animatable.View
              style={[
                {
                  transform: [
                    {
                      translateY: vh(
                        location === LocationType.top ? -120 : 120,
                      ),
                    },
                  ],
                },
                styleInner,
              ]}
              ref={animatableViewRef}
            >
              {children}
            </Animatable.View>
          </KeyboardAvoidView>
        </View>
      </View>
    </ModalWrapper>
  );
};
export const BbkComponentModalAnimationPreset = (type = 'center') => {
  if (AnimationPresets[type]) return AnimationPresets[type];
  return {
    location: 'center',
    animation: false,
  };
};
const styles = StyleSheet.create({
  modalContainer: {
    alignItems: 'center',
  },
  modalWrapper: {
    flex: 1,
    justifyContent: 'flex-start',
  },
});
export default withTheme(BbkComponentModal);
