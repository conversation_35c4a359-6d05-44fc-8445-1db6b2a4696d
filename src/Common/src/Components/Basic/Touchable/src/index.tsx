import React, { ReactNode, useCallback } from 'react';
import {
  TouchableHighlight,
  TouchableHighlightProps,
  TouchableOpacity,
  TouchableNativeFeedbackProps,
  TouchableOpacityProps,
} from 'react-native';
import _ from 'lodash';
export interface Props
  extends TouchableNativeFeedbackProps,
    TouchableOpacityProps,
    TouchableHighlightProps {
  children?: ReactNode | ReactNode[];
  highLight?: boolean;
  debounce?: boolean;
  debounceTime?: number;
  testID?: string;
}

const Touchable = ({
  highLight = false,
  underlayColor,
  style,
  activeOpacity,
  background,
  children,
  debounce,
  debounceTime,
  onPress,
  testID,
  ...passThroughProps
}: Props) => {
  const debounceOnPress =
    onPress &&
    useCallback(
      _.debounce(onPress, debounceTime || 1000, {
        trailing: false,
        leading: true,
      }),
      [onPress],
    );
  const fixOnPress = debounce ? debounceOnPress : onPress;

  if (highLight) {
    return (
      <TouchableHighlight
        underlayColor={underlayColor || 'rgba(0,0,0,.2)'}
        style={style}
        disabled={!onPress}
        onPress={fixOnPress}
        testID={testID}
        {...passThroughProps}
      >
        {children}
      </TouchableHighlight>
    );
  }

  return (
    <TouchableOpacity
      activeOpacity={activeOpacity || 0.7}
      style={style}
      disabled={!onPress}
      onPress={fixOnPress}
      testID={testID}
      {...passThroughProps}
    >
      {children}
    </TouchableOpacity>
  );
};

export default Touchable;
