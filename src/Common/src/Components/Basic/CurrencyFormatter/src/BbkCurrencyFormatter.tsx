import React from 'react';
import { TextStyle, View } from 'react-native'; // @ts-ignore upgrade 072
import PropTypes from 'prop-types';
import BbkComponentText from '../../Text';
import { BbkUtils } from '../../../../Utils';
import { toRMB } from '../../../../Shark/src/Index';
import NumberText from '../../NumberText';

const { getPixel } = BbkUtils;

interface IBUL10nNumberOption {
  usesGroupingSeparator?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}

export interface IBUFormatCurrencyTextProps {
  price: number;
  currency: string;
  currencyStyle?: TextStyle | TextStyle[];
  priceStyle?: TextStyle | TextStyle[];
  wrapperStyle?: TextStyle | TextStyle[];
  options?: IBUL10nNumberOption;
  isNew?: boolean;
  testID?: string;
  useNumberText?: boolean;
}

export default class BbkCurrencyFormatter extends React.Component<IBUFormatCurrencyTextProps> {
  static propTypes = {
    currency: PropTypes.string.isRequired,
    price: PropTypes.number.isRequired,
    currencyStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    priceStyle: PropTypes.oneOfType([PropTypes.object, PropTypes.array]),
    isNew: PropTypes.bool,
  };

  constructor(props) {
    super(props);
  }

  currencyNewText = ({
    currency,
    currencyStyle,
    priceStyle,
    priceStr,
    wrapperStyle,
    testID,
    useNumberText,
  }) => {
    const TextWrapper = useNumberText ? NumberText : BbkComponentText;
    return (
      <View
        testID={testID}
        key={currency}
        style={[wrapperStyle, { flexDirection: 'row' }]}
      >
        <BbkComponentText style={[currencyStyle, { marginRight: getPixel(2) }]}>
          {toRMB(currency)}
        </BbkComponentText>
        <TextWrapper style={[priceStyle]}>{priceStr}</TextWrapper>
      </View>
    );
  };

  render() {
    const {
      currency,
      currencyStyle,
      priceStyle,
      wrapperStyle,
      testID,
      useNumberText,
    } = this.props;
    const { price } = this.props;

    return this.currencyNewText({
      currency,
      currencyStyle,
      priceStyle,
      priceStr: price,
      wrapperStyle,
      testID,
      useNumberText,
    });
  }
}
