import React, { Component } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableHighlight,
  TouchableWithoutFeedback,
  AppRegistry,
} from 'react-native';
import { color } from '../../../../Tokens';
// @ts-ignore
const utils = AppRegistry.utils;

class AlertView extends Component {
  static defaultProps = {
    title: '',
    content: '',
    buttons: [{ text: 'ok' }],
    style: {},
  };

  render() {
    let props: any = this.props;
    let { content, buttons = [], gid } = props;
    if (typeof content === 'string' || typeof content === 'number') {
      content = (
        <Text style={[styles.contentText]} testID="dialog message">
          {content}
        </Text>
      );
    } else {
      content = React.cloneElement(content, { testID: 'dialog message' });
    }
    let buttonsLength = buttons.length;
    //adjust the padding
    // @ts-ignore
    styles.content.paddingTop = props.title ? 10 : 25;

    return (
      <TouchableWithoutFeedback
        key={gid}
        onPress={() => {
          props.onClose();
        }}
      >
        <View style={[styles.dialogLayout]}>
          <View style={[styles.dialog]}>
            <View style={[styles.content]}>
              {props.title !== '' ? (
                <View style={[styles.title]}>
                  <Text style={[styles.titleText]} testID="dialog title">
                    {props.title}
                  </Text>
                </View>
              ) : null}
              {content}
            </View>

            <View
              style={[
                buttonsLength > 2
                  ? { flexDirection: 'column' }
                  : { flexDirection: 'row' },
              ]}
            >
              {buttons.map(function (item, index) {
                let itemStyle =
                  styles[
                    index || buttonsLength !== 2 ? 'buttonRight' : 'buttonLeft'
                  ];
                return (
                  <TouchableHighlight
                    testID={
                      item.testID ||
                      (index == 0
                        ? 'dialog negative button'
                        : 'dialog positive button')
                    }
                    key={index}
                    underlayColor="#f9f9f9"
                    style={[styles.button, itemStyle]}
                    onPress={() => {
                      props.onClose();
                      item.onPress && item.onPress();
                    }}
                  >
                    <Text style={[styles.buttonText]}>{item.text}</Text>
                  </TouchableHighlight>
                );
              })}
            </View>
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }
}

const styles = StyleSheet.create({
  dialogLayout: {
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  dialog: {
    backgroundColor: color.white,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: color.splitGray,
    flexDirection: 'column',
    borderRadius: 15,
    alignSelf: 'center',
    width: 280,
    overflow: 'hidden',
    textAlign: 'center',
  },
  title: {
    backgroundColor: 'transparent',
    // height: 44,//title不应有高度限制
    overflow: 'hidden',
    position: 'relative',
    justifyContent: 'center',
  },
  titleText: {
    fontSize: 16,
    marginVertical: 0,
    marginHorizontal: 20,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  content: {
    flex: 1,
    paddingBottom: 20,
    overflow: 'visible',
  },

  contentText: {
    fontSize: 15,
    paddingLeft: 10,
    paddingRight: 10,
  },

  button: {
    backgroundColor: 'transparent',
    height: 44,
    flex: 1,
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: color.splitGray,
  },
  // buttonActive: {
  //     backgroundColor: '#f9f9f9'
  // },
  buttonText: {
    textAlign: 'center',
    color: color.blueBase,
    fontSize: 16,
  },
});

let Alert: any = {};

Alert.alert = function (title, content, buttons) {
  let gid = 'alert-' + utils.getGidNum(),
    props = {
      gid,
      title: String(!title ? '' : title),
      content: String(!content ? '' : content),
      buttons,
    };
  let ComponentToRender = (
    <AlertView
      {...props}
      // @ts-ignore
      onClose={() => {
        utils.hideContainer(gid);
      }}
      key={gid}
    />
  );

  utils.render(ComponentToRender);
};
/**
 * ref https://facebook.github.io/react-native/docs/alertios#prompt
 */
Alert.prompt = function (
  title,
  message,
  callbackOrButtons,
  ButtonsArray = [],
  type,
  defaultValue,
  keyboardType,
) {
  if (typeof callbackOrButtons !== 'function') {
    ButtonsArray = callbackOrButtons || [];
    type = defaultValue;
    defaultValue = keyboardType;
  }
  let gid = 'alert-' + utils.getGidNum(),
    props = {
      title: String(!title ? '' : title),
      content: String(!message ? '' : message),
      buttons: ButtonsArray,
    };
  let ComponentToRender = (
    <AlertView
      {...props}
      // @ts-ignore
      onClose={() => {
        utils.hideContainer(gid);
      }}
      key={gid}
    />
  );

  utils.render(ComponentToRender);
};
export default Alert;
