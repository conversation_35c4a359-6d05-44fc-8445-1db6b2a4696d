import React, { Component, Fragment } from 'react';
import PropTypes from 'prop-types';
import {
  Text,
  Platform,
  TextInput,
  TextInputProps,
  TextStyle,
} from 'react-native'; // @ts-ignore

import { Dialog as IBUDialog } from '@ctrip/crn';
import { BbkUtils } from '../../../../Utils';
import styles from './style';
import Dialog, { props as DialogProps } from './Dialog';

const { ensureFunctionCall } = BbkUtils;

const useAndroidDialog =
  // @ts-ignore
  IBUDialog && (Platform.OS === 'android' || Platform.OS === 'harmony');

interface props extends DialogProps {
  validator?: () => string;
  titleStyle?: TextStyle;
  defaultValue?: string;
  inputTextProps: any;
}

interface state {
  message: string;
  value: string;
}

export default class DialogWithEdit extends Component<props, state> {
  constructor(props) {
    super(props);
    const { inputTextProps = {}, defaultValue } = props;
    this.state = {
      message: '',
      value: defaultValue || inputTextProps.value || '',
    };
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    if (useAndroidDialog && nextProps.visible !== this.props.visible) {
      if (nextProps.visible) {
        const {
          inputTextProps = {},
          confirmBtnText,
          cancelBtnText,
          validator,
          defaultValue,
          onConfirm,
          onCancel,
        } = nextProps;
        IBUDialog.showDialogWithEdit(
          {
            // canceledOnTouchOutside: true, //可不填，默认为空
            cancelable: false, // 可不填，默认为空
            title: nextProps.title,
            message: defaultValue || inputTextProps.value || '',
            textPositive: confirmBtnText,
            textNegative: cancelBtnText,
          },
          // @ts-ignore
          value => {
            const errormsg = ensureFunctionCall(validator, this, value);
            if (errormsg) {
              IBUDialog.sendErrorMsg(errormsg);
            } else {
              IBUDialog.sendCorrectMsg();
              ensureFunctionCall(onConfirm, this, value);
            }
          },
          onCancel,
        );
      }
    }
  }

  handleTextChange = value => {
    const { inputTextProps, validator } = this.props;
    // const message = ensureFunctionCall(validator, this, value);
    // this.setState({
    //     message: message || "",
    // });
    this.setState({
      value,
    });
    ensureFunctionCall(inputTextProps.onChangeText, this, value);
  };

  handleConfirm = () => {
    const { inputTextProps, validator, onConfirm } = this.props;
    const { value } = this.state;
    const message = ensureFunctionCall(validator, this, this.state.value);
    this.setState({
      message: message || '',
    });
    if (!message) {
      ensureFunctionCall(onConfirm, this, value);
    }
  };

  render() {
    const {
      title,
      titleStyle,
      inputTextProps = {},
      ...passThroughProps
    } = this.props;
    const { style, ...inputTextPassThroughProps } = inputTextProps;
    if (useAndroidDialog) {
      return null;
    }
    const { message, value } = this.state;
    return (
      <Dialog
        type="confirm"
        {...passThroughProps}
        onConfirm={this.handleConfirm}
      >
        <Text style={[styles.alertTitle, titleStyle]}>{title}</Text>
        <TextInput
          style={[styles.input, style]}
          autoFocus
          autoCapitalize="none"
          clearButtonMode="while-editing"
          {...inputTextPassThroughProps}
          value={value}
          onChangeText={this.handleTextChange}
        />
        <Text style={styles.inputFeedback}>{message}</Text>
      </Dialog>
    );
  }
}
