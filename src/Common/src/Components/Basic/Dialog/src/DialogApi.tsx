//@ts-ignore
import { Dialog } from '@ctrip/crn';
import { Alert } from 'react-native';

export default class DialogApi {
  static alert(title, message, buttons = [], options) {
    Alert.alert(title, message, buttons, options);
    return;
  }
  static prompt(title, message, callbackOrButtons, options) {
    Alert.prompt(title, message, callbackOrButtons, options);
    return;
  }
  static sendErrorMsg(errorMsg) {
    Dialog.sendErrorMsg(errorMsg);
  }
  static sendCorrectMsg() {
    Dialog.sendCorrectMsg();
  }
  static showDialogWithMutilChoice(...props) {
    // @ts-ignore
    Dialog.showDialogWithMutilChoice(...props);
  }
  static showDialogWithSingleChoice(...props) {
    // @ts-ignore
    Dialog.showDialogWithMutilChoice(...props);
  }
}
