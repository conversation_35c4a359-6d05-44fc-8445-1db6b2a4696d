import {
  Platform,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Image,
  I18nManager,
  View,
} from 'react-native';
import React, { memo } from 'react';
import { color, space, border, font } from '../../../../Tokens';
import { BbkUtils, BbkStyleUtil } from '../../../../Utils';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import BbkComponentTouchable from '../../Touchable/src';
import BbkText from '../../Text';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
const { getPixel } = BbkUtils;

const { blueGrayBase, blueBase, grayBase, fontPrimary, fontSubDark } = color;
const { spaceXS, spaceXL, spaceS } = space;
const { borderSizeSm, borderSizeLg } = border;
const { body3LightStyle, body3BoldStyle, labelSLightStyle } = font;

const styles = StyleSheet.create({
  view: {
    height: spaceXL + spaceXS - borderSizeSm - borderSizeLg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  viewSmall: {
    height: spaceXL - borderSizeSm - borderSizeLg,
  },
  text: {
    color: fontPrimary,
    paddingHorizontal: spaceS,
    ...body3LightStyle,
  },
  textDisabled: {
    color: grayBase,
  },
  textSelected: {
    color: blueBase,
    ...body3BoldStyle,
  },
  subTitleText: {
    color: fontSubDark,
    ...labelSLightStyle,
    marginTop: getPixel(-6),
  },
  subTitleTextSelected: {
    color: blueBase,
  },
  easyLifeTagStyle: {
    position: 'absolute',
    top: 0,
    right: getPixel(-2),
    ...BbkStyleUtil.getWH(80, 26),
  },
  newEasyLifeTagStyle: {
    right: 0,
    ...BbkStyleUtil.getWH(92, 33),
  },
  indicator: {
    position: 'absolute',
    bottom: 0,
    flexDirection: I18nManager && I18nManager.isRTL ? 'row-reverse' : 'row',
  },
});

export type Props = {
  id: string;
  onPress: () => {};
  title: string;
  accessibilityLabel?: string;
  disabled: boolean;
  selected: boolean;
  style: ViewStyle;
  small: boolean;
  theme?: Object;
  textStyle?: TextStyle;
  textDisabledStyle?: TextStyle;
  textSelectedStyle?: TextStyle;
  subTitleSelectedStyle?: TextStyle;
  subTitle?: string;
  isEasyLife?: boolean;
  animateIndicator?: boolean;
  iWidth?: number;
  iHeight?: number;
  iBottom?: number;
  useNewEasyLifeStyle?: boolean;
  selectedDisable?: boolean;
  indicatorColor?: string;
};

const horizontalNavSelectedTextColor = 'horizontalNavSelectedTextColor';

const propsAreEqual = (prevProps, nextProps) =>
  prevProps.selected === nextProps.selected &&
  prevProps.disabled === nextProps.disabled &&
  prevProps.title === nextProps.title;

const BbkHorizontalNavItem = (props: Props) => {
  const {
    accessibilityLabel,
    disabled,
    selected,
    style,
    theme,
    small,
    title,
    textStyle,
    textSelectedStyle,
    textDisabledStyle,
    subTitle,
    subTitleSelectedStyle,
    isEasyLife,
    animateIndicator,
    iWidth,
    iHeight,
    iBottom = 0,
    useNewEasyLifeStyle,
    selectedDisable = true,
    indicatorColor,
    ...rest
  } = props;
  const accessibilityStates = [];
  const textSize = small ? 'sm' : 'base';
  const viewStyles = [];
  const textStyles = [];
  const subTitleStyles = [];
  viewStyles.push(styles.view);
  textStyles.push(styles.text);
  subTitleStyles.push(styles.subTitleText);
  const themeAttributes: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  const horizontalNavSelectedIndicatorColor = indicatorColor || blueBase;

  textStyles.push(textStyle);

  if (disabled) {
    accessibilityStates.push('disabled');
    textStyles.push(styles.textDisabled);
    textStyles.push(textDisabledStyle);
    subTitleStyles.push(styles.textDisabled);
    subTitleStyles.push(textDisabledStyle);
  } else if (selected) {
    textStyles.push(styles.textSelected);
    if (themeAttributes) {
      textStyles.push({
        color: themeAttributes[horizontalNavSelectedTextColor],
      });
      subTitleStyles.push({
        color: themeAttributes[horizontalNavSelectedTextColor],
      });
    }
    textStyles.push(textSelectedStyle);
    subTitleStyles.push(styles.subTitleTextSelected);
    subTitleStyles.push(subTitleSelectedStyle);
  }

  if (small) {
    viewStyles.push(styles.viewSmall);
  }

  if (style) {
    viewStyles.push(style);
  }
  // @ts-ignore
  const isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';
  const platformProps = isAndroid ? { borderlessBackground: false } : {};
  const easyLifeImgUrl = `https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/easyLifeTag${
    useNewEasyLifeStyle ? 2 : ''
  }.png`;
  return (
    <BbkComponentTouchable
      accessibilityRole="button"
      accessibilityLabel={accessibilityLabel || title} // @ts-ignore upgrade 072
      accessibilityStates={accessibilityStates}
      disabled={disabled || (selectedDisable && selected)}
      {...platformProps}
      {...rest}
      style={viewStyles}
    >
      <BbkText style={textStyles} textStyle={textSize}>
        {title}
      </BbkText>
      {!!subTitle && <BbkText style={subTitleStyles}>{subTitle}</BbkText>}
      {isEasyLife && (
        <Image
          style={[
            styles.easyLifeTagStyle,
            useNewEasyLifeStyle && styles.newEasyLifeTagStyle,
          ]}
          resizeMode="contain"
          source={{ uri: easyLifeImgUrl }}
        />
      )}
      {!animateIndicator && selected && (
        <View
          style={[
            styles.indicator,
            {
              width: iWidth,
              height: iHeight,
              bottom: iBottom,
              backgroundColor: horizontalNavSelectedIndicatorColor,
            },
          ]}
        ></View>
      )}
    </BbkComponentTouchable>
  );
};

// @ts-ignore
export default memo(withTheme(BbkHorizontalNavItem), propsAreEqual);
