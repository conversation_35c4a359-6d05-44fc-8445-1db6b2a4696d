import { Animated } from 'react-native';
import React from 'react';
import { druation } from '../../../../Tokens';

const { animationDurationSm } = druation;

interface IProps {
  WrappedComponent: any;
  animatedProps: Array<string>;
}

function withAnimatedProps(WrappedComponent, animatedProps) {
  return class WithAnimatedProps extends React.Component<any> {
    constructor(props: IProps) {
      super(props);

      this.state = {};

      animatedProps.forEach(prop => {
        this.state[prop] = new Animated.Value(props[prop]);
      });
    }

    UNSAFE_componentWillReceiveProps(nextProps: IProps) {
      const animations = animatedProps.map(prop => {
        const tempState = this.state;
        return Animated.timing(tempState[prop], {
          toValue: nextProps[prop],
          duration: nextProps['animateDruation'] || animationDurationSm,
          useNativeDriver: false,
        });
      });

      Animated.parallel(animations).start();
    }

    render() {
      const newProps = {};

      animatedProps.forEach(prop => {
        const tempState = this.state;
        newProps[prop] = tempState[prop];
      });

      return <WrappedComponent {...this.props} {...newProps} />;
    }
  };
}

export default withAnimatedProps;
