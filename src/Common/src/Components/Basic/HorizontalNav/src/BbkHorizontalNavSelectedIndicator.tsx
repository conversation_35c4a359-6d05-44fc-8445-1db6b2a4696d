import React from 'react';
import { Animated, StyleSheet } from 'react-native';
import PropTypes from 'prop-types';
import { getThemeAttributes, withTheme, Theme } from '../../../../Theming';
import { color } from '../../../../Tokens';
import { REQUIRED_THEME_ATTRIBUTES, themePropType } from './Theming';

const { blueBase } = color;

const styles = StyleSheet.create({
  selectedIndicator: {
    backgroundColor: blueBase,
  },
});

export type Props = {
  xOffset?: number | Object;
  width?: number | Object;
  height?: number | Object;
  bottom?: number;
  indicatorColor?: any;
  theme?: Theme;
};

const BbkHorizontalNavSelectedIndicator = (props: Props) => {
  const { xOffset, width, height, theme, bottom, indicatorColor } = props;
  const style = [];
  style.push(styles.selectedIndicator);
  if (typeof bottom === 'number') {
    style.push({
      bottom,
    });
  }

  const themeAttributes: any =
    getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) || {};
  if (indicatorColor) {
    style.push({
      backgroundColor: indicatorColor,
    });
  } else {
    const horizontalNavSelectedIndicatorColor = blueBase;
    if (themeAttributes) {
      style.push({
        backgroundColor: horizontalNavSelectedIndicatorColor,
      });
    }
  }

  const animationStyles = {
    transform: [
      {
        translateX: xOffset,
      },
    ],
    width,
    height,
  };
  // @ts-ignore upgrade 072
  return <Animated.View style={[style, animationStyles]} />;
};

BbkHorizontalNavSelectedIndicator.propTypes = {
  xOffset: PropTypes.oneOfType([
    PropTypes.number,
    PropTypes.instanceOf(Object),
  ]),
  bottom: null,
  width: PropTypes.oneOfType([PropTypes.number, PropTypes.instanceOf(Object)]),
  theme: themePropType,
};

BbkHorizontalNavSelectedIndicator.defaultProps = {
  xOffset: null,
  width: null,
  bottom: null,
  indicatorColor: null,
  theme: null,
};

// @ts-ignore
export default withTheme(BbkHorizontalNavSelectedIndicator);
