import React, { useEffect, useState } from 'react';
import PropsTypes from 'prop-types';
import {
  View,
  Dimensions,
  StyleSheet,
  NativeModules,
  LayoutAnimation,
  Animated,
  Easing,
} from 'react-native';
import { BbkUtils } from '../../../../Utils';
import { color } from '../../../../Tokens';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';

const { ensureFunctionCall, isAndroid, getPixel } = BbkUtils;
const { width } = Dimensions.get('window');

const { UIManager } = NativeModules;
UIManager?.setLayoutAnimationEnabledExperimental &&
  UIManager?.setLayoutAnimationEnabledExperimental(true);

export interface themeType {
  progressBarBackgroundColor: string;
}

export interface ProgressBarType {
  progress: number;
  style?: any;
  innerStyle?: any;
  progressWidth?: number;
  progressHeight?: number;
  defaultPercent?: number;
  theme?: themeType;
  onProgressDismiss?: Function;
}

const getStyle = (theme: themeType) => {
  const themeAttributes: any = {
    ...getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme),
  };

  const progressInnerStyle: any = [styles.progressInner];
  if (themeAttributes.progressBarBackgroundColor) {
    progressInnerStyle.push({
      backgroundColor: themeAttributes.progressBarBackgroundColor,
    });
  }
  return {
    progressInnerStyle,
  };
};

const ProgressBar = (props: ProgressBarType) => {
  let {
    progress,
    style,
    innerStyle,
    progressWidth,
    progressHeight = 5,
    defaultPercent,
    theme,
  } = props;
  useEffect(() => {
    LayoutAnimation.configureNext({
      duration: 500,
      update: {
        type: LayoutAnimation.Types.spring,
        springDamping: 0.8,
      },
    });
  }, [progress]);

  const { progressInnerStyle } = getStyle(theme);

  if (progress >= 1) progress = 1;
  const height = progress === 1 ? 0 : getPixel(progressHeight);
  const widths =
    progressWidth * ((1 - defaultPercent) * progress + defaultPercent);
  return (
    <View style={[styles.progressWrapper, style, { width: progressWidth }]}>
      <View
        style={[progressInnerStyle, innerStyle, { width: widths, height }]}
      />
    </View>
  );
};

ProgressBar.propTypes = {
  progress: PropsTypes.number,
  style: PropsTypes.any,
  innerStyle: PropsTypes.any,
  progressWidth: PropsTypes.number,
  defaultPercent: PropsTypes.number,
};

ProgressBar.defaultProps = {
  progress: 0,
  defaultPercent: 0.5,
  progressWidth: width,
};

const ProgressBarAndroid = (props: ProgressBarType) => {
  const [visible, setVisible] = useState(true);
  const [progress, setProgress] = useState(0);
  const [height, setHeight] = useState(new Animated.Value(getPixel(5)));
  const [width, setWidth] = useState(new Animated.Value(0));

  const resetProgress = () => {
    const { onProgressDismiss } = props;
    // @ts-ignore
    if (!this.isMounted) return;
    setVisible(false);
    width.setValue(0);
    height.setValue(getPixel(5));
    ensureFunctionCall(onProgressDismiss);
  };

  const animate = (progress, cb) => {
    const duration = progress === 1 ? 1000 : 2000; // @ts-ignore upgrade 072
    Animated.timing(width, {
      // easing: Easing.easeIn,
      easing: Easing.ease,
      toValue:
        props.progressWidth *
        ((1 - props.defaultPercent) * progress + props.defaultPercent),
      duration,
    }).start(() => {
      if (progress === 1) {
        // @ts-ignore upgrade 072
        Animated.timing(height, {
          toValue: 0,
          duration: 200,
          // easing: Easing.easeInOut,
          easing: Easing.ease,
        }).start(cb);
      } else if (typeof cb === 'function') {
        cb();
      }
    });
  };

  useEffect(() => {
    let pg = props.progress;
    if (pg >= 1) pg = 1;
    if (pg > 0 && pg <= 1 && progress !== 1) {
      // @ts-ignore
      this.animate(pg, () => {
        if (pg === 1) {
          resetProgress();
        }
      });
    } else if (progress === 1 && pg < 1) {
      setVisible(true);
      animate(progress, null);
    }
    setProgress(progress);
  }, [visible, props, progress, resetProgress, animate]);

  const { innerStyle, style, theme } = props;
  const { progressInnerStyle } = getStyle(theme);

  return visible ? (
    <View style={[styles.progressWrapper, style]}>
      <Animated.View
        style={[progressInnerStyle, innerStyle, { width, height }]}
      />
    </View>
  ) : null;
};

ProgressBarAndroid.defaultProps = {
  progress: 0,
  defaultPercent: 0.5,
  progressWidth: width,
};

export default withTheme(ProgressBar);

const styles = StyleSheet.create({
  progressWrapper: {
    width,
  },
  progressInner: {
    backgroundColor: color.linearGradientOrangeLight,
    height: getPixel(5),
  },
});
