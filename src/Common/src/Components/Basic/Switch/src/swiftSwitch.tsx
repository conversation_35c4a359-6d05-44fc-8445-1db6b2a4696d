import React from 'react';
import { Image as AnimatableImage } from 'react-native-animatable';
import { StyleSheet, View } from 'react-native';
import { LinearGradient } from '@ctrip/crn';
import Touchable from '../../Touchable/src';
import { BbkUtils } from '../../../../Utils';
import { color, layout } from '../../../../Tokens';
import { useMemoizedFn } from '../../../../Utils/src/Utils';

const { getPixel, isIos } = BbkUtils;
const width = getPixel(70, 'ceil');
const height = getPixel(38, 'ceil');
const left0 = isIos ? getPixel(2) : getPixel(1);
const left1 = getPixel(30);
const styles = StyleSheet.create({
  wrapper: {
    width,
    height,
    borderRadius: Math.ceil(height / 2),
    ...layout.flexCenter,
  },
  btn: {
    width: height,
    height,
    position: 'absolute',
  },
  line: {
    width: getPixel(33),
    height: getPixel(4),
    borderRadius: getPixel(4),
  },
});

const toRight = {
  0: { left: left0 },
  1: { left: left1 },
};

const toLeft = {
  0: { left: left1 },
  1: { left: left0 },
};

const animationTime = 100;
const SwiftSwitch = ({ value, onPress }) => {
  const [show, setShow] = React.useState(value);
  const [bgShow, setBgShow] = React.useState(value);
  const animate = React.useRef(null);

  const animating = useMemoizedFn(v => {
    animate.current?.animate(v ? toRight : toLeft);
    setTimeout(() => {
      setBgShow(v);
    }, animationTime / 2);
  });

  React.useEffect(() => {
    if (value === show) return;
    setShow(value);
    animating(value);
  }, [value]);

  const onSwitch = useMemoizedFn(() => {
    const showNow = !show;
    onPress(showNow);
    animating(showNow);
  });

  return (
    <Touchable debounce debounceTime={500} onPress={onSwitch}>
      <LinearGradient
        style={styles.wrapper}
        start={{ x: 0.0, y: 0.0 }}
        end={{ x: 1.0, y: 1.0 }}
        locations={[0, 1]}
        colors={
          bgShow
            ? [color.C_009FFB, color.C_0066F6]
            : [color.C_B8BDC8, color.C_8B91A0]
        }
      >
        <View
          style={[
            styles.line,
            {
              backgroundColor: bgShow ? color.C_0073F5 : color.C_8B91A0,
            },
          ]}
        />
        <AnimatableImage
          source={{
            uri: 'https://dimg04.c-ctrip.com/images/1tg3x12000etldp51B454.png',
          }}
          resizeMode="contain"
          easing="linear"
          duration={animationTime}
          ref={animate}
          style={[styles.btn, { left: show ? left1 : left0 }]}
        />
      </LinearGradient>
    </Touchable>
  );
};
export default SwiftSwitch;
