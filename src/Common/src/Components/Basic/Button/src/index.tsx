import React from 'react';
import {
  StyleSheet,
  ViewStyle,
  TextStyle,
  View,
  Image,
  ImageProps,
  StyleProp,
} from 'react-native'; // @ts-ignore

import {
  LinearGradient, // @ts-ignore upgrade 072
} from '@ctrip/crn';

import { withTheme } from '../../../../Theming'; // @ts-ignore

import { color, font, button, tokenType } from '../../../../Tokens';
import Text from '../../Text'; // @ts-ignore

import BbkTouchable, { Props as TouchableProps } from '../../Touchable/src';
import { Props as LabelProps } from '../../Label';
import { buttonTheme } from './Theme';
import { getPixel } from '../../../../Utils/src/Utils';

const sprayCorner =
  'https://dimg04.c-ctrip.com/images/1tg4k12000cxv1rii421D.png';
const filterCorner =
  'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelected.png';
interface Props
  extends tokenType.ColorProps,
    tokenType.ButtonProps,
    TouchableProps {
  /**
   * button text
   */
  text?: string;
  /**
   * theme from withTheme wrap
   */
  theme?: buttonTheme;
  /**
   * button style
   * these styles will be covered by IBUButton, so these are invalid
   *    {
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          paddingLeft: padding,
          paddingRight: padding,
          backgroundColor: wrapperViewColor,
          borderRadius: 2,
        }
   */
  buttonStyle?: ViewStyle;
  /**
   * button text style
   */
  textStyle?: TextStyle;
  /**
   * button with icon content
   * such as &#xee4f;
   */
  iconCode?: string;
  /**
   * icon style
   */
  iconStyle?: StyleProp<any>;

  onPress?: () => void;

  textLines?: number;

  imageUrl?: string;

  imageProps?: ImageProps;

  labelProps?: LabelProps;

  showRightTopHook?: boolean;

  showRightBottomHook?: boolean;

  rightBottomHookUrl?: string;

  gradientColorArr?: Array<string>;

  iconShowRight?: boolean;

  isLoading?: boolean;
}

const getThemeColor = (theme, colorType) => {
  theme = theme || {};
  return theme[colorType] || theme[tokenType.ColorType.Blue] || {};
};

const getButtonStyle = (
  buttonType,
  { theme, buttonSize, colorType }: Props,
) => {
  const isGradient = buttonType === tokenType.ButtonType.Gradient;
  const isSpray = buttonType === tokenType.ButtonType.Spray;
  const colorTheme = getThemeColor(theme, colorType);
  const { buttonGradientColor = {} as any } = colorTheme;
  const buttonSizeStyle =
    button[`button${buttonSize}Style`] || button.buttonMStyle;
  const buttonTextSizeStyle =
    button[`button${buttonSize}TextStyle`] || button.buttonMTextStyle;
  const buttonColor = color[`${colorType}Base`] || color.blueBase;
  const buttonStyleBgColor = colorTheme.buttonBgColor || buttonColor;
  const sprayBtn = isSpray ? button.buttonSprayStyle : null;
  return {
    $buttonStyle: [
      buttonSizeStyle,
      {
        backgroundColor: isSpray ? color.deepBlueBase : buttonStyleBgColor,
        ...sprayBtn,
      },
    ],
    $buttonTextStyle: [
      buttonTextSizeStyle,
      {
        color:
          (isGradient
            ? buttonGradientColor.buttonGradientTextColor
            : colorTheme.buttonTextColor) || color.white,
      },
    ],
    $gradientColorArr: [
      buttonGradientColor.from || color.linearGradientOrangeLight,
      buttonGradientColor.to || color.linearGradientOrangeDark,
    ],
  };
};

const IbuMap = {
  buttonSize: {
    [tokenType.ButtonSize.S]: 'sm',
    [tokenType.ButtonSize.M]: 'md',
    [tokenType.ButtonSize.L]: 'lg',
  },
  colorStyle: {
    [tokenType.ColorType.Blue]: 'blue',
    [tokenType.ColorType.Orange]: 'orange',
    [tokenType.ColorType.Transparent]: 'transparent',
  },
};

const BbkButton = (props: Props) => {
  const {
    text,
    colorType = tokenType.ColorType.Blue,
    theme,
    buttonStyle,
    textStyle,
    buttonSize,
    buttonType,
    iconCode,
    iconStyle,
    textLines,
    imageUrl,
    imageProps,
    labelProps,
    showRightTopHook = false,
    showRightBottomHook = false,
    rightBottomHookUrl,
    gradientColorArr,
    iconShowRight,
    isLoading,
    ...passThroughProps
  } = props;

  const isGradient = buttonType === tokenType.ButtonType.Gradient;
  const isSpray = buttonType === tokenType.ButtonType.Spray;
  const { $buttonStyle, $buttonTextStyle, $gradientColorArr } = getButtonStyle(
    buttonType,
    props,
  );
  const textDom = (
    <Text style={[$buttonTextStyle, textStyle]} numberOfLines={textLines}>
      {text}
    </Text>
  );

  const iconDom =
    iconCode &&
    (iconCode.indexOf('http') > -1 ? (
      <Image source={{ uri: iconCode }} style={iconStyle} />
    ) : (
      <Text style={[$buttonTextStyle, iconStyle]} type="icon">
        {iconCode}
      </Text>
    ));

  const imageDom = imageUrl && (
    <Image source={{ uri: imageUrl }} {...imageProps} />
  );
  const innerDom = imageUrl ? imageDom : textDom;
  const labelDom = labelProps && (
    <View
      style={[
        styles.$labelStyle,
        labelProps.labelStyle,
        (!labelProps.labelStyle ||
          !labelProps.labelStyle.hasOwnProperty('left')) &&
          styles.$labelStyleRight,
      ]}
    >
      <Text style={[styles.$labelTextStyle, labelProps.textStyle]}>
        {labelProps.text}
      </Text>
    </View>
  );
  const rightTopHook = showRightTopHook && (
    <Image
      source={{
        uri: 'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/filterSelect.png',
      }}
      style={styles.rightTopHookStyle}
    />
  );
  const rightBottomHook =
    showRightBottomHook &&
    (isSpray ? (
      <Image
        source={{
          uri: rightBottomHookUrl || sprayCorner,
        }}
        style={styles.rightBottomHookSprayStyle}
      />
    ) : (
      <Image
        source={{
          uri: rightBottomHookUrl || filterCorner,
        }}
        style={styles.rightBottomHookStyle}
      />
    ));
  const children = !!iconShowRight ? (
    <>
      {innerDom}
      {iconDom}
    </>
  ) : (
    <>
      {iconDom}
      {innerDom}
    </>
  );

  if (isLoading) {
    return (
      <BbkTouchable
        style={!isGradient && [$buttonStyle, buttonStyle]}
        debounce
        {...passThroughProps}
        disabled={true}
      >
        <View style={styles.loadingWrap}>
          <Image
            source={{
              uri: 'https://dimg04.c-ctrip.com/images/1tg0i12000cw6mi0fC09A.gif',
            }}
            style={styles.loading}
          />
        </View>
      </BbkTouchable>
    );
  }

  return (
    <BbkTouchable
      style={!isGradient && [$buttonStyle, buttonStyle]}
      debounce
      {...passThroughProps}
    >
      {rightTopHook}
      {rightBottomHook}
      {isGradient ? (
        <LinearGradient
          style={[$buttonStyle, buttonStyle]}
          start={{ x: 0.0, y: 0.0 }}
          end={{ x: 1.0, y: 1.0 }}
          locations={[0, 1]}
          colors={gradientColorArr || $gradientColorArr}
        >
          {children}
        </LinearGradient>
      ) : (
        <View style={styles.iconView}>{children}</View>
      )}
      {labelDom}
    </BbkTouchable>
  );
};

BbkButton.defaultProps = {
  buttonSize: tokenType.ButtonSize.M,
  buttonType: tokenType.ButtonType.Default,
};

const styles = StyleSheet.create({
  iconView: {
    flexDirection: 'row',
  },
  $labelStyle: {
    position: 'absolute',
    top: getPixel(-14),
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: color.orangeBase,
    borderTopLeftRadius: getPixel(12),
    borderTopRightRadius: getPixel(12),
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: getPixel(12),
    paddingHorizontal: getPixel(10),
    minWidth: getPixel(74),
    height: getPixel(24),
  },
  $labelStyleRight: {
    right: 0,
  },
  $labelTextStyle: {
    ...font.labelSLightStyle,
    color: color.white,
    lineHeight: getPixel(26),
  },
  rightTopHookStyle: {
    width: getPixel(24),
    height: getPixel(24),
    position: 'absolute',
    right: 0,
    top: 0,
  },
  rightBottomHookStyle: {
    width: getPixel(20),
    height: getPixel(20),
    position: 'absolute',
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  rightBottomHookSprayStyle: {
    width: getPixel(96),
    height: getPixel(88),
    position: 'absolute',
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  loadingWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loading: {
    width: getPixel(36),
    height: getPixel(36),
  },
});

export default withTheme(BbkButton);
