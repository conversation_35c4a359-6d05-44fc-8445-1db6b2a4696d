import React from 'react';

import SmallLoading from './SmallLoading';
import CircleLoading from './CircleLoading';

type ILoading = {
  type: string;
  [props: string]: any;
};

const Loading: React.FC<ILoading> = ({ type = 'small', ...otherProps }) => {
  if (type === 'small') {
    return <SmallLoading {...otherProps} />;
  } else if (type === 'circle') {
    return <CircleLoading {...otherProps} />;
  }
  return null;
};

export default Loading;
