import React, { Component } from 'react';
import {
  View,
  StyleSheet,
  Image,
  TextStyle,
  ViewStyle,
  Animated,
  Easing,
} from 'react-native';
import * as Animatable from 'react-native-animatable';
import { BbkUtils } from '../../../../Utils';
import Text from '../../Text';
import { layout, color, font, icon } from '../../../../Tokens';
const { getPixel, selector } = BbkUtils;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: getPixel(10),
    backgroundColor: color.blackTransparent,
    paddingTop: getPixel(28),
    paddingBottom: getPixel(40),
    paddingHorizontal: getPixel(28),
    borderRadius: getPixel(12),
  },
  img: {
    marginLeft: getPixel(-4),
    marginTop: getPixel(4),
  },
  text: {
    marginLeft: getPixel(4),
    marginTop: getPixel(20),
    color: color.white,
    ...font.subTitle1RegularStyle,
  },
});

type ICircleLoading = {
  imageSize?: number;
  text?: string;
  textStyle?: TextStyle;
  style?: ViewStyle;
};

class CircleLoading extends Component<ICircleLoading> {
  spinValue: Animated.Value;

  constructor(props) {
    super(props);
    this.spinValue = new Animated.Value(0);
    this.state = {};
  }
  componentDidMount() {
    this.spin();
  }
  //旋转方法
  spin = () => {
    this.spinValue.setValue(0);

    // @ts-ignore upgrade 072
    Animated.timing(this.spinValue, {
      toValue: 1,
      duration: 1000,
      easing: Easing.linear,
    }).start(() => this.spin());
  };
  render() {
    const { imageSize = 64, text, textStyle, style } = this.props;
    const spin = this.spinValue.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '360deg'],
    });
    return (
      <View style={[styles.wrapper, style]}>
        <Animated.Image
          style={[
            styles.img,
            { width: getPixel(imageSize), height: getPixel(imageSize) },
            { transform: [{ rotate: spin }] },
          ]}
          source={require('../img/circle-loading.png')}
        />
        {selector(text, <Text style={[styles.text, textStyle]}>{text}</Text>)}
      </View>
    );
  }
}

export default CircleLoading;
