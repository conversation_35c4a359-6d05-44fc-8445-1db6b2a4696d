import React from 'react';
import { View, StyleSheet, Image, TextStyle, ViewStyle } from 'react-native';
import * as Animatable from 'react-native-animatable';
import { BbkUtils } from '../../../../Utils';
import Text from '../../Text';

const { getPixel, selector } = BbkUtils;

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getPixel(10),
  },
  img: {
    marginLeft: getPixel(-4),
    marginTop: getPixel(4),
  },
  text: {
    marginLeft: getPixel(4),
  },
});

type ISmallLoading = {
  imageSize?: number;
  text?: string;
  textStyle?: TextStyle;
  style?: ViewStyle;
};

const SmallLoading: React.FC<ISmallLoading> = ({
  imageSize = 50,
  text,
  textStyle,
  style,
}) => (
  <View style={[styles.wrapper, style]}>
    <Image
      source={require('../img/small-loading.gif')}
      style={[
        styles.img,
        { width: getPixel(imageSize), height: getPixel(imageSize) },
      ]}
    />
    {selector(text, <Text style={[styles.text, textStyle]}>{text}</Text>)}
  </View>
);

export default SmallLoading;
