import React, { useContext } from 'react'; //@ts-ignore

import { View, StyleSheet, Platform, ViewStyle, Animated } from 'react-native';
import BbkComponentText from '../../Text';
import { BbkUtils, BbkConstants } from '../../../../Utils';
import { useWindowSizeChanged } from '../../../../Hooks';
import { setOpacity, color, font, icon } from '../../../../Tokens';
import { withTheme, getThemeAttributes } from '../../../../Theming';
import BbkTouchable from '../../Touchable/src';
import { REQUIRED_THEME_ATTRIBUTES } from './Theming';
import {
  FloatHeaderContext,
  getOpacityIconWrapStyle,
  getOpacityIconStyle,
  getOpacityStyle,
} from './Context';
export * from './Context';

const { selector, getPixel, ensureFunctionCall, fixOffsetTop, vw } = BbkUtils;
// @ts-ignore
const isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';
const androidSelector = selector(isAndroid);
const { DEFAULT_HEADER_HEIGHT } = BbkConstants;

export enum TextColorType {
  light = 'Light',
  dark = 'Dark',
}

export type ICHeader = {
  style?: ViewStyle;
  styleInner?: ViewStyle;
  contentStyle?: ViewStyle;
  sideLeftStyle?: ViewStyle;
  leftIcon?: string;
  isLeftIconCross?: boolean;
  leftIconColor?: string;
  leftIconStyle?: ViewStyle;
  title?: string;
  titleColor?: string;
  titleStyle?: ViewStyle;
  subtitle?: string;
  subtitleColor?: string;
  subtitleStyle?: ViewStyle;
  rightIcon?: string;
  rightIconColor?: string;
  rightIconStyle?: ViewStyle;
  isBottomBorder?: boolean;
  renderContent?: () => void;
  renderLeft?: (isHideAnimation?: boolean) => void;
  renderRight?: (isHideAnimation?: boolean) => void;
  onPressLeft?: () => void;
  onPressRight?: () => void;
  renderBottom?: () => void;
  textColorType?: TextColorType;
  backgroundOpacity?: number;
  theme?: any;
  isHideAnimation?: boolean;
  opacityAnimation?: Animated.Value;
  fixOpacityAnimation?: Animated.Value;
  containderStyle?: ViewStyle;
  isShowAnimation?: boolean;
  leftIconTestID?: string;
  titleTestID?: string;
};

const BbkComponentHeader: React.FC<ICHeader> = ({
  onPressLeft,
  onPressRight,
  renderContent,
  renderBottom,
  renderLeft,
  renderRight,
  style,
  styleInner,
  contentStyle,
  sideLeftStyle,
  title,
  titleColor,
  titleStyle,
  subtitle,
  subtitleColor,
  subtitleStyle,
  leftIcon,
  leftIconColor,
  leftIconStyle,
  rightIcon,
  rightIconColor,
  rightIconStyle,
  isBottomBorder = true,
  isLeftIconCross,
  theme,
  textColorType = TextColorType.dark,
  backgroundOpacity = 1,
  isHideAnimation = false,
  opacityAnimation,
  fixOpacityAnimation,
  containderStyle,
  isShowAnimation = false,
  leftIconTestID,
  titleTestID,
}) => {
  const width100Style = useWindowSizeChanged();
  const titleProps: IContent = {
    style: contentStyle,
    title,
    titleColor:
      titleColor ||
      (textColorType === TextColorType.light && theme.backgroundColor),
    titleStyle,
    subtitle,
    subtitleColor,
    subtitleStyle,
    titleTestID,
  };
  const themes = getThemeAttributes(REQUIRED_THEME_ATTRIBUTES, theme) as any;
  const borderColor = (themes && themes.bbkLineColor) || color.grayBorder;

  let backgroundColor;
  if (style && style.backgroundColor) {
    backgroundColor = setOpacity(style.backgroundColor, backgroundOpacity);
  }
  const { opacity } = useContext(FloatHeaderContext);
  const showOpacity = opacity > 0;
  const Container = isHideAnimation ? Animated.View : View;
  const isFuncRenderLeft = typeof renderLeft === 'function';
  const isFuncRenderRight = typeof renderRight === 'function';

  return (
    <View>
      <Container
        style={[
          styles.wrapper,
          width100Style,
          isBottomBorder && showOpacity && styles.bottomLine,
          { borderColor },
          style,
          {
            backgroundColor,
            opacity: isHideAnimation ? fixOpacityAnimation : 1,
          },
          containderStyle,
        ]}
      >
        <View style={[styles.wrapperInner, styleInner]}>
          {selector(
            renderLeft,
            isFuncRenderLeft ? renderLeft() : renderLeft,
            <SideView
              icon={
                leftIcon ||
                selector(isLeftIconCross, icon.cross, icon.arrowLeft)
              }
              iconColor={
                leftIconColor ||
                (textColorType === TextColorType.light && theme.backgroundColor)
              }
              iconStyle={leftIconStyle}
              style={[styles.sideLeft, sideLeftStyle]}
              onPress={onPressLeft}
              testID={leftIconTestID}
            />,
          )}
          {selector(renderContent, renderContent, <Content {...titleProps} />)}
          {selector(
            renderRight,
            isFuncRenderRight ? renderRight() : renderRight,
            <SideView
              icon={rightIcon}
              iconColor={
                rightIconColor ||
                (textColorType === TextColorType.light && theme.backgroundColor)
              }
              iconStyle={rightIconStyle}
              style={styles.sideRight}
              onPress={onPressRight}
            />,
          )}
        </View>
        {(!isHideAnimation || isShowAnimation) && <View>{renderBottom}</View>}
      </Container>

      {isHideAnimation && (
        <Container
          style={[
            styles.wrapper,
            width100Style,
            isBottomBorder && showOpacity && styles.bottomLine,
            { borderColor },
            style,
            {
              backgroundColor,
              opacity: isHideAnimation ? opacityAnimation : 1,
              position: 'absolute',
              top: 0,
              left: 0,
            },
          ]}
        >
          <View style={[styles.wrapperInner, styleInner]}>
            {selector(
              renderLeft,
              isFuncRenderLeft ? renderLeft(isHideAnimation) : renderLeft,
              <SideView
                icon={
                  leftIcon ||
                  selector(isLeftIconCross, icon.cross, icon.arrowLeft)
                }
                iconColor={
                  leftIconColor ||
                  (textColorType === TextColorType.light &&
                    theme.backgroundColor)
                }
                iconStyle={leftIconStyle}
                style={[styles.sideLeft, sideLeftStyle, styles.ctripIconBg]}
                onPress={onPressLeft}
              />,
            )}
            {selector(
              renderRight,
              isFuncRenderRight ? renderRight(isHideAnimation) : renderRight,
              <SideView
                icon={rightIcon}
                iconColor={
                  rightIconColor ||
                  (textColorType === TextColorType.light &&
                    theme.backgroundColor)
                }
                iconStyle={rightIconStyle}
                style={[styles.sideRight, styles.ctripIconBg]}
                onPress={onPressRight}
              />,
            )}
          </View>
        </Container>
      )}
    </View>
  );
};

type ISideView = {
  icon?: string;
  iconColor?: string;
  iconStyle?: ViewStyle | ViewStyle[];
  style?: ViewStyle | ViewStyle[];
  onPress?: () => void;
  testID?: string;
};
const SideView: React.FC<ISideView> = ({
  icon,
  iconColor,
  style,
  iconStyle,
  onPress,
  testID,
}) => {
  if (!icon) return null;
  const Wrapper = selector(onPress, BbkTouchable, View);
  let iconStyles: any = [styles.iconText, iconStyle];
  if (iconColor) {
    iconStyles.push({ color: iconColor });
  }

  const { opacity } = useContext(FloatHeaderContext);
  const opacityWrapStyle = getOpacityIconWrapStyle(opacity);
  const opacityStyle = getOpacityIconStyle(opacity);

  return (
    <Wrapper
      testID={testID}
      style={[styles.sideContainer, style, opacityWrapStyle]}
      onPress={() => {
        ensureFunctionCall(onPress);
      }}
    >
      <BbkComponentText type="icon" style={[iconStyles, opacityStyle]}>
        {icon}
      </BbkComponentText>
    </Wrapper>
  );
};

type IContent = {
  style?: ViewStyle;
  title?: string;
  titleColor?: string;
  titleStyle?: ViewStyle;
  subtitle?: string;
  subtitleColor?: string;
  subtitleStyle?: ViewStyle;
  titleTestID?: string;
};
const Content: React.FC<IContent> = ({
  style,
  title,
  titleColor,
  titleStyle,
  subtitle,
  subtitleColor,
  subtitleStyle,
  titleTestID,
}) => {
  if (!title) return null;
  let defaultTitleStyle = [selector(!subtitle, font.title1BoldStyle)];
  if (titleColor) {
    defaultTitleStyle.push({ color: titleColor });
  }
  const { opacity } = useContext(FloatHeaderContext);
  return (
    <View style={[styles.content, style, getOpacityStyle(opacity)]}>
      <View testID={titleTestID}>
        <BbkComponentText
          numberOfLines={1}
          style={[defaultTitleStyle, titleStyle]}
        >
          {title}
        </BbkComponentText>
      </View>
      {selector(
        subtitle,
        <BbkComponentText style={[{ color: subtitleColor }, subtitleStyle]}>
          {subtitle}
        </BbkComponentText>,
      )}
    </View>
  );
};

const sideTop = Platform.select({
  ios: getPixel(15),
  web: getPixel(15),
  android: getPixel(21),
  // @ts-ignore
  harmony: getPixel(21),
});

const sideWidth = Platform.select({
  ios: getPixel(64),
  web: getPixel(64),
  android: getPixel(64),
  // @ts-ignore
  harmony: getPixel(64),
});

const sideLeft = Platform.select({
  ios: getPixel(32),
  web: getPixel(32),
  android: getPixel(20),
  // @ts-ignore
  harmony: getPixel(20),
});

const styles = StyleSheet.create({
  wrapper: {
    position: 'relative',
    margin: 0,
    paddingTop: fixOffsetTop(),
  },
  ctripIconBg: {
    borderRadius: getPixel(128),
    backgroundColor: setOpacity(color.black, 0.4),
  },
  bottomLine: {
    borderBottomWidth: StyleSheet.hairlineWidth,
  },
  wrapperInner: {
    minHeight: DEFAULT_HEADER_HEIGHT,
    justifyContent: 'space-between',
    flexDirection: 'row',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: getPixel(48 + 32 + 40),
    alignItems: 'center',
  },
  iconText: {
    fontSize: getPixel(48),
  },
  sideContainer: {
    position: 'absolute',
    top: sideTop,
    bottom: sideTop,
    zIndex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sideRight: {
    width: getPixel(60),
    right: getPixel(32),
  },
  sideLeft: {
    width: sideWidth,
    minHeight: sideWidth,
    left: sideLeft,
  },
});
//@ts-ignore
export default withTheme(BbkComponentHeader);
