import React, { useCallback } from 'react';
import { View, StyleSheet, TextStyle, ViewStyle } from 'react-native'; // @ts-ignore upgrade 072
import BbkText from '../../Text';
import BbkTouchable from '../../Touchable/src';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import { BbkUtils } from '../../../../Utils';
import { icon, color } from '../../../../Tokens';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
const { getPixel, selector } = BbkUtils;

type ICheckBox = {
  checked: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  text?: string;
  theme?: any;
  textStyle?: TextStyle;
  iconStyle?: TextStyle;
  unCheckedIconStyle?: TextStyle;
  onCheckedChange?: (checked: boolean) => void;
  useLayout?: boolean;
  squareIcon?: string;
  squareTickFilledIcon?: string;
  testID?: string;
};
const CheckBox: React.FC<ICheckBox> = ({
  checked,
  disabled,
  style,
  iconStyle,
  unCheckedIconStyle,
  text,
  theme,
  textStyle,
  onCheckedChange,
  useLayout = false,
  squareIcon,
  squareTickFilledIcon,
  testID,
}) => {
  let themes = getThemeAttributes(
    REQUIRED_THEME_ATTRIBUTES,
    theme,
  ) as ThemeProps | null;
  if (!themes) themes = {} as ThemeProps;
  const {
    bbkCheckboxColor,
    bbkCheckboxPopColor,
    bbkCheckboxDisableBorderColor,
    bbkCheckboxDisableBackgroundColor,
  } = themes;
  const onCheckBoxChange = useCallback(() => {
    if (!disabled && onCheckedChange) onCheckedChange(!checked);
  }, [disabled, checked]);
  return (
    <BbkTouchable
      style={[selector(text, styles.wrapper), style]}
      onPress={onCheckBoxChange}
      testID={testID}
    >
      {selector(
        disabled,
        <View
          style={[
            styles.checkBoxDisable,
            {
              backgroundColor:
                bbkCheckboxDisableBackgroundColor || color.grayBase,
              borderColor: bbkCheckboxDisableBorderColor || color.grayBorder,
            },
          ]}
        />,
        selector(
          checked,
          <BbkText
            type="icon"
            style={[
              styles.checkBox,
              { color: bbkCheckboxPopColor || color.blueBase },
              iconStyle,
            ]}
          >
            {squareTickFilledIcon || icon.squareTickFilled}
          </BbkText>,
          <BbkText
            type="icon"
            style={[
              styles.checkBox,
              { color: bbkCheckboxColor || color.grayDescLine },
              iconStyle,
              unCheckedIconStyle,
            ]}
          >
            {squareIcon || icon.square}
          </BbkText>,
        ),
      )}
      {selector(text, <BbkText style={[textStyle]}>{text}</BbkText>)}
    </BbkTouchable>
  );
};
const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkBox: {
    paddingRight: getPixel(6),
    fontSize: getPixel(36),
  },
  checkBoxDisable: {
    width: getPixel(17.5),
    height: getPixel(17.5),
    borderStyle: 'solid',
    borderWidth: getPixel(1.3),
    borderRadius: getPixel(2),
    marginRight: getPixel(6),
  },
});
export default withTheme(CheckBox);
