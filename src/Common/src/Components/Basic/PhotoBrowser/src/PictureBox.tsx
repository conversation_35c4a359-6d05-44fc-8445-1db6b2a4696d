import React, { useCallback } from 'react';
import {
  View,
  StyleSheet,
  Image,
  ViewStyle,
  ImageStyle,
  NativeSyntheticEvent,
  ImageLoadEventData,
} from 'react-native';
// PhotoBrowser文档：http://pages.release.ctripcorp.com/BaseBiz-Open/basebizcomponentsbook/CRN/ImageBrorwse/CRNImageBrorwse.html
// @ts-ignore upgrade 072
import { PhotoBrowser } from '@ctrip/crn';
import Touchable from '../../Touchable/src';
import { BbkUtils } from '../../../../Utils';

const { getPixel, fixProtocol } = BbkUtils;

const styles = StyleSheet.create({
  picturesWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingVertical: getPixel(20),
  },
  picturesItem: {
    width: getPixel(160),
    height: getPixel(160),
    marginRight: getPixel(8),
    marginBottom: getPixel(8),
  },
});

interface IPictureBox {
  data: string[];
  onPress: (index: number) => void;
  style?: ViewStyle;
  imgStyle?: ImageStyle;
  thumbnail?: boolean;
  onLoad?: (event: NativeSyntheticEvent<ImageLoadEventData>) => void;
}

const PictureBox: React.FC<IPictureBox> = ({
  data,
  onPress,
  style,
  imgStyle,
  thumbnail,
  ...passTroughProps
}) => {
  if (!data || !data.length) return null;
  const showPicture = useCallback(
    index => {
      const imageList = data.map(val => ({ imageUrl: fixProtocol(val) }));
      PhotoBrowser.showWithScrollCallback(imageList, [], index, {}, () => {});
      onPress && onPress(index);
    },
    [data],
  );
  return (
    <View style={[styles.picturesWrapper, style]}>
      {data.map((v, index) => (
        <Touchable
          key={`content_images_item${index}`}
          onPress={() => showPicture(index)}
        >
          <Image
            style={[styles.picturesItem, imgStyle]}
            // http://conf.ctripcorp.com/pages/viewpage.action?pageId=106239834#id-%E5%9B%BE%E7%89%87%E5%8A%A8%E6%80%81%E5%88%87%E5%9B%BE%E6%8E%A5%E5%85%A5-15.%E5%88%87%E5%9B%BE
            source={{
              uri: fixProtocol(
                `${v}${thumbnail ? '?proc=resize/m_c,w_100,h_200,610D' : ''}`,
              ),
            }}
            resizeMode="cover"
            {...passTroughProps}
          />
        </Touchable>
      ))}
    </View>
  );
};

export default PictureBox;
