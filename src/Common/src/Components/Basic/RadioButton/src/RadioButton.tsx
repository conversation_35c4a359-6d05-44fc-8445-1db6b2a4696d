import React, { useCallback } from 'react';
import { View, StyleSheet, TextStyle, ViewStyle } from 'react-native';
import BbkText from '../../Text';
import BbkTouchable from '../../Touchable/src';
import { getThemeAttributes, withTheme } from '../../../../Theming';
import { BbkUtils } from '../../../../Utils';
import { icon, color, setOpacity } from '../../../../Tokens';
import { REQUIRED_THEME_ATTRIBUTES, ThemeProps } from './Theming';
const { getPixel, selector } = BbkUtils;

type IRadioButton = {
  checked: boolean;
  disabled?: boolean;
  style?: ViewStyle;
  radioButtonStyle?: ViewStyle;
  type?: string;
  text?: string;
  theme?: any;
  textStyle?: TextStyle;
  iconStyle?: TextStyle;
  checkedIconStyle?: TextStyle;
  disabledIconStyle?: TextStyle;
  unCheckTickStyle?: TextStyle;
  isCustomer?: boolean;
  isOnlyChoose?: boolean;
  isEasylife?: boolean;
  unCheckInnerTick?: boolean;
  onPress?: () => void;
  onCheckedChange?: (checked: boolean) => void;
};
const RadioButton: React.FC<IRadioButton> = ({
  checked,
  disabled,
  style,
  radioButtonStyle,
  iconStyle,
  checkedIconStyle,
  unCheckTickStyle,
  disabledIconStyle = {},
  text,
  theme,
  textStyle,
  isOnlyChoose = false,
  unCheckInnerTick = false,
  onCheckedChange,
  onPress,
}) => {
  let themes = getThemeAttributes(
    REQUIRED_THEME_ATTRIBUTES,
    theme,
  ) as ThemeProps | null;
  if (!themes) themes = {} as ThemeProps;
  const {
    bbkRadioButtonColor,
    bbkRadioButtonPopColor,
    bbkRadioButtonDisableBorderColor,
    bbkRadioButtonDisableBackgroundColor,
  } = themes;

  const onRadioButtonChange = useCallback(() => {
    if (!disabled && onCheckedChange && !(isOnlyChoose && !!checked))
      onCheckedChange(!checked);
  }, [disabled, checked]);

  // 纯粹的 press, 避免 useCallback 造成不刷新
  const fixOnPress = () => {
    if (!disabled && onPress) onPress();
  };
  return (
    <BbkTouchable
      style={[style, selector(text, styles.wrapper)]}
      onPress={onPress ? fixOnPress : onRadioButtonChange}
    >
      {selector(
        disabled,
        <View style={[styles.radioButtonContain, radioButtonStyle]}>
          <View
            style={[
              styles.radioButtonDisable,
              {
                backgroundColor:
                  disabledIconStyle.backgroundColor ||
                  bbkRadioButtonDisableBackgroundColor ||
                  color.radioDisableBackGroundColor,
                borderColor:
                  disabledIconStyle.borderColor ||
                  bbkRadioButtonDisableBorderColor ||
                  color.radioBorderColor,
              },
            ]}
          ></View>
        </View>,
        selector(
          checked,
          <View style={[styles.radioButtonContain, radioButtonStyle]}>
            <BbkText
              type="icon"
              style={[
                styles.radioButton,
                { color: bbkRadioButtonPopColor || color.blueBase },
                iconStyle,
                checkedIconStyle,
              ]}
            >
              {icon.circleTickFilled}
            </BbkText>
          </View>,
          <View style={[styles.radioButtonContain, radioButtonStyle]}>
            {unCheckInnerTick && (
              <BbkText
                type="icon"
                style={[styles.unCheckTick, unCheckTickStyle]}
              >
                {icon.tick_area}
              </BbkText>
            )}
            <BbkText
              type="icon"
              style={[
                styles.radioButton,
                { color: bbkRadioButtonColor || color.radioBorderColor },
                iconStyle,
              ]}
            >
              {icon.circleLine}
            </BbkText>
          </View>,
        ),
      )}
      {selector(text, <BbkText style={[textStyle]}>{text}</BbkText>)}
    </BbkTouchable>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  radioButton: {
    fontSize: 21,
  },
  radioButtonContain: {
    width: 21,
    height: 21,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioButtonDisable: {
    width: 18,
    height: 18,
    borderStyle: 'solid',
    borderWidth: getPixel(4),
    borderRadius: 9,
  },
  unCheckTick: {
    fontSize: getPixel(20),
    position: 'absolute',
    color: setOpacity(color.blueBase, 0.2),
  },
});
export default withTheme(RadioButton);
