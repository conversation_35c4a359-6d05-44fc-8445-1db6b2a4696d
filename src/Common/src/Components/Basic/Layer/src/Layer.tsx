import React, { memo } from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { color, font } from '../../../../Tokens';
// import Text from '../../Text';
// import Touchable from '../../Touchable/src';
import BbkHeader from '../../Header/src';
import BbkComponentModal, {
  BbkComponentModalAnimationPreset,
  IBbkComponentModal,
} from '../../Modal';
import { BbkUtils } from '../../../../Utils';
// import { texts } from './Text';

const { selector, getPixel, vh, vw, fixIOSOffsetBottom } = BbkUtils;

const isAndroid = false;

interface IHeader {
  onPressClose: () => void;
  title: string;
}
const Header: React.FC<IHeader> = ({ title, onPressClose }) => (
  <BbkHeader
    renderLeft={selector(isAndroid, <View />, null)}
    isLeftIconCross
    title={title}
    titleStyle={styles.titleStyle}
    onPressLeft={onPressClose}
    style={styles.headerWrapper}
    styleInner={styles.header}
    contentStyle={styles.headerContent}
  />
);

const Content = ({ children, contentStyle }) => (
  <View style={[styles.content, contentStyle]}>{children}</View>
);

// interface IFooter {
//   onPress: () => void;
//   cancelColor?: string;
//   confirmColor?: string;
//   hideFtooterBtn?: boolean;
// }
// const Footer: React.FC<IFooter> = ({
//   onPress,
//   cancelColor = color.fontSubDark,
//   confirmColor = color.blueBase,
//   hideFtooterBtn,
// }) => {
//   if (!isAndroid || !!hideFtooterBtn) return null;
//   return (
//     <View style={styles.footer}>
//       <Touchable onPress={onPress}>
//         <Text
//           style={[font.body1Style, styles.footerItem, { color: confirmColor }]}
//         >
//           {texts.done}
//         </Text>
//       </Touchable>
//       <Touchable onPress={onPress}>
//         <Text
//           style={[font.body1Style, styles.footerItem, { color: cancelColor }]}
//         >
//           {texts.cancel}
//         </Text>
//       </Touchable>
//     </View>
//   );
// };

interface ILayer extends IBbkComponentModal {
  title: string;
  theme: any;
  onRequestClose: () => void;
  isFixbottom?: boolean;
  contentStyle?: ViewStyle;
}
const Layer: React.FC<ILayer> = ({
  title,
  children,
  theme,
  onRequestClose,
  isFixbottom,
  contentStyle,
  ...otherProps
}) => {
  return (
    <BbkComponentModal
      maskHide={true}
      onRequestClose={onRequestClose}
      {...BbkComponentModalAnimationPreset(
        selector(isAndroid, 'center', 'bottom'),
      )}
      {...otherProps}
    >
      <View
        style={[
          styles.wrapper,
          selector(!isAndroid, styles.radius, styles.androidWrapper),
          { backgroundColor: color.white },
        ]}
      >
        <Header title={title} onPressClose={onRequestClose} />
        <Content contentStyle={contentStyle}>{children}</Content>
        {/* <Footer
          onPress={onRequestClose}
          cancelColor={color.fontSubDark}
          confirmColor={color.blueBase}
          {...otherProps}
        /> */}
      </View>
    </BbkComponentModal>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    width: vw(100),
  },
  androidWrapper: {
    width: vw(100) - getPixel(48 * 2),
  },
  radius: {
    minHeight: getPixel(366),
    maxHeight: vh(60),
    borderTopRightRadius: getPixel(24),
    borderTopLeftRadius: getPixel(24),
  },
  headerWrapper: {
    paddingTop: 0,
    elevation: 0,
    width: isAndroid ? -getPixel(48 * 2) : vw(100),
  },
  header: {
    paddingVertical: selector(isAndroid, getPixel(32), getPixel(35)),
    paddingLeft: selector(isAndroid, getPixel(32), 0),
    width: selector(isAndroid, vw(100) - getPixel(32 * 4), vw(100)),
  },
  headerContent: {
    paddingHorizontal: selector(isAndroid, getPixel(0), getPixel(150)),
  },
  titleStyle: {
    textAlign: selector(isAndroid, 'left', 'center'),
  },
  content: {
    padding: getPixel(32),
    paddingTop: selector(isAndroid, 0, getPixel(32)),
    paddingBottom: fixIOSOffsetBottom(32),
  },
  footer: {
    flexDirection: 'row-reverse',
    paddingHorizontal: getPixel(32),
    paddingBottom: getPixel(32),
  },
  footerItem: {
    marginLeft: getPixel(46),
  },
});

export default memo(Layer);
