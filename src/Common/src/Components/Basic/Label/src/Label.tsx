import React from 'react';
import {
  View,
  TextStyle,
  ViewStyle,
  ImageStyle,
  Image,
  StyleSheet,
} from 'react-native';
import {
  label,
  font,
  color,
  border,
  icon as iconToken,
  space,
  tokenType,
  radius,
} from '../../../../Tokens';
import { withTheme } from '../../../../Theming';
import { BbkUtils } from '../../../../Utils';
import BbkText from '../../Text';
import { labelTheme } from './Theme';
const { typeOf, getPixel, autoProtocol } = BbkUtils;

const newSplit = 'https://dimg04.c-ctrip.com/images/1tg0p12000d2ql7hl0058.png';
const newGraySplit =
  'https://dimg04.c-ctrip.com/images/1tg1j12000d2ql1esF809.png';
export interface Props extends tokenType.labelProps, tokenType.ColorProps {
  text: string;
  hasBorder?: boolean;
  isFixSmallFontStyle?: boolean;
  /**
   * 无底色
   */
  noBg?: boolean;
  theme?: labelTheme;
  labelStyle?: ViewStyle;
  textStyle?: TextStyle;
  icon?: iconProps;
  /**
   * 砍价类型标签的前缀
   */
  prefix?: string;
  prefixWrapStyle?: ViewStyle;
  prefixStyle?: TextStyle;
  prefixImage?: string;
  prefixImageStyle?: ImageStyle;
  // 后缀
  postfix?: string;
  postfixWrapStyle?: ViewStyle;
  postfixStyle?: TextStyle;
  isSerration?: boolean; // 是否有锯齿边样式
  isEasyLife?: boolean; // 是否是无忧租图片icon
  serrationStyle?: any;
  borColor?: string;
  isSoldOut?: boolean;
  // 虚线样式
  isNewDash?: boolean;
  testID?: string;
}

interface iconProps {
  iconType: 'primary' | 'secondary';
  iconContent: string;
  iconStyle?: TextStyle;
  iconWrapStyle?: ViewStyle;
  isPrefix?: boolean;
}

const style = StyleSheet.create({
  borderLabelWrap: {
    borderWidth: border.borderSizeXsm,
    borderRadius: radius.radiusXS,
    overflow: 'hidden',
  },
  icon: {
    fontFamily: iconToken.getIconFamily(),
    marginRight: space.spaceS,
  },
  noBg: {
    backgroundColor: color.transparent,
  },
  serrationLeft: {
    width: getPixel(10),
    height: getPixel(12),
    position: 'absolute',
    top: getPixel(8.5),
    left: getPixel(-1),
    zIndex: 10,
  },
  serrationRight: {
    width: getPixel(10),
    height: getPixel(12),
    position: 'absolute',
    top: getPixel(8.5),
    right: getPixel(7),
    zIndex: 10,
  },
  dashed: {
    width: getPixel(2),
    height: getPixel(27),
    position: 'absolute',
    top: getPixel(1),
    left: getPixel(3),
    zIndex: 10,
  },
  dashedNew: {
    width: getPixel(2),
    height: getPixel(34),
    position: 'absolute',
    left: getPixel(3),
    zIndex: 10,
  },
});

const getThemeColor = (theme, colorType) => {
  theme = theme || {};
  if (theme.labelBgColor || theme.labelColor) {
    return theme;
  }
  return theme[colorType] || theme[tokenType.ColorType.Blue] || {};
};

/**
 * style merge rules:
 *
 * 1. colorType              =>  labelBgColor, labelColor
 *    labelSize, labelType   =>  labelStyle, textStyle
 * 2. theme
 * 3. labelStyle, textStyle
 */
const getLabelStyle = ({
  theme,
  hasBorder,
  noBg,
  labelSize,
  labelType,
  colorType = tokenType.ColorType.Blue,
  borColor,
  icon = {} as any,
  isSerration,
}: Props) => {
  const { iconType, iconContent } = icon;
  const isPrimary = iconType === 'primary';

  const colorTheme = getThemeColor(theme, colorType);
  const bgColor = `${colorType || 'blue'}Bg`;
  const baseColor = `${colorType || 'blue'}Base`;
  const borderColor = `${colorType || 'blue'}Border`;

  const {
    labelBgColor = color[hasBorder ? bgColor : baseColor],
    labelColor = color[hasBorder || noBg ? baseColor : bgColor],
    labelBorderColor = color[borderColor] || labelColor,
  } = colorTheme;

  if (iconContent && !labelSize) {
    labelSize = 'L';
  }
  const $labelStyle =
    label[`${labelType}Label${labelSize}Style`] ||
    label[`${labelType}LabelSStyle`] ||
    label[`baseLabel${labelSize}Style`] ||
    label.baseLabelSStyle;

  const labelAddHeight = hasBorder ? style.borderLabelWrap.borderWidth * 2 : 0;

  const $textStyle = font[`label${labelSize}Style`] || font.labelSStyle;

  const themeBgStyle = {
    backgroundColor: labelBgColor,
  };

  return {
    $labelStyle: [
      $labelStyle,
      {
        minHeight: labelAddHeight + $labelStyle.minHeight,
      },
      !isPrimary && themeBgStyle,
      hasBorder && {
        ...style.borderLabelWrap,
        borderColor: labelBorderColor,
      },
      noBg && style.noBg,
    ],
    $textStyle: [
      $textStyle,
      {
        color: labelColor,
        flexWrap: 'wrap',
      },
    ],
    $iconStyle: [
      style.icon,
      {
        width: label.baseLabelLHeight,
        height: label.baseLabelLHeight,
        lineHeight: label.baseLabelLHeight,
        textAlign: 'center',
        borderRadius: radius.radiusXS,
        color: labelColor,
        fontSize: $textStyle.fontSize,
      },
      isPrimary && themeBgStyle,
    ],
    $prefixWrapStyle: [
      $labelStyle,
      {
        backgroundColor: labelColor,
        marginLeft: -$labelStyle.paddingHorizontal,
        marginRight: $labelStyle.paddingHorizontal,
      },
    ],
    $postfixWrapStyle: [
      {
        backgroundColor: isSerration ? color.labelPostfixBg : labelColor,
        marginRight: -$labelStyle.paddingHorizontal,
        marginLeft: getPixel(4),
        paddingLeft: getPixel(4),
        paddingRight: getPixel(8),
      },
    ],
    $postfixStyle: {
      ...$textStyle,
      color: color.white,
    },
    $prefixStyle: {
      ...$textStyle,
      color: color.white,
    },
    $serrationLeft: {
      borderColor: borColor,
    },
    $serrationRight: {
      borderColor: borColor,
    },
  };
};

const tranformArray2Obj = (input: any) => {
  if (typeOf(input) === 'Array') {
    return input.reduce((m, v) => {
      if (typeOf(v) === 'Array') {
        return tranformArray2Obj(v);
      }
      if (v) {
        return { ...m, ...v };
      }
      return m;
    }, {});
  }
  return input;
};

const fixWebLabel = ($textStyle: any, textStyle: any) => {
  const fixStyle = {
    ...tranformArray2Obj($textStyle),
    ...tranformArray2Obj(textStyle),
  };
  return {};
};

const BbkComponentLabel = (props: Props) => {
  const {
    text,
    hasBorder,
    labelStyle,
    textStyle,
    prefix,
    prefixWrapStyle,
    prefixStyle,
    icon = {},
    prefixImage,
    prefixImageStyle,
    postfix,
    postfixWrapStyle,
    postfixStyle,
    isFixSmallFontStyle = true,
    isSerration,
    serrationStyle,
    isEasyLife,
    isSoldOut,
    isNewDash = false,
    testID,
  } = props;
  const {
    iconType,
    iconContent,
    iconStyle,
    isPrefix = true,
    iconWrapStyle,
  } = icon as any;
  const {
    $labelStyle,
    $textStyle,
    $iconStyle,
    $prefixWrapStyle,
    $prefixStyle,
    $postfixWrapStyle,
    $postfixStyle,
    $serrationLeft,
    $serrationRight,
  } = getLabelStyle(props) as any;
  const iconDom = iconContent && (
    <View style={iconWrapStyle}>
      <BbkText type="icon" style={[$iconStyle, iconStyle]}>
        {iconContent}
      </BbkText>
    </View>
  );
  const splitUrl = autoProtocol(
    `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/dashed${
      isSoldOut ? '_gray' : ''
    }.png`,
  );
  const newSplitUrl = isSoldOut ? newGraySplit : newSplit;
  return (
    <View testID={testID}>
      <View style={[$labelStyle, labelStyle]}>
        {!!prefixImage && (
          <Image
            style={prefixImageStyle}
            source={{
              uri: prefixImage,
            }}
            resizeMode={isEasyLife ? 'cover' : 'contain'}
          />
        )}
        {isPrefix && iconDom}
        {!!prefix && (
          <View style={[$prefixWrapStyle, prefixWrapStyle]}>
            <BbkText style={[$prefixStyle, prefixStyle]}>{prefix}</BbkText>
          </View>
        )}
        <BbkText
          style={[$textStyle, textStyle]}
          smallFontStyle={
            isFixSmallFontStyle ? fixWebLabel($textStyle, textStyle) : {}
          }
          numberOfLines={100}
        >
          {text}
        </BbkText>
        {!!postfix && (
          <View>
            <Image
              style={isNewDash ? style.dashedNew : style.dashed}
              source={{
                uri: isNewDash ? newSplitUrl : splitUrl,
              }}
              resizeMode="contain"
            />
            <View style={[$postfixWrapStyle, postfixWrapStyle]}>
              <BbkText style={[$postfixStyle, postfixStyle]}>{postfix}</BbkText>
            </View>
          </View>
        )}
        {!isPrefix && iconDom}
      </View>
      {isSerration && (
        <Image
          style={[style.serrationLeft, $serrationLeft, serrationStyle]}
          source={{
            uri: autoProtocol(
              `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/market_tag_left_half_circle${
                isSoldOut ? '_gray' : ''
              }.png`,
            ),
          }}
          resizeMode="contain"
        />
      )}
      {isSerration && (
        <Image
          style={[style.serrationRight, $serrationRight, serrationStyle]}
          source={{
            uri: autoProtocol(
              `//pic.c-ctrip.com/car/osd/mobile/bbk/resource/market_tag_right_half_circle${
                isSoldOut ? '_gray' : ''
              }.png`,
            ),
          }}
          resizeMode="contain"
        />
      )}
    </View>
  );
};

export default withTheme(BbkComponentLabel);
