// @ts-ignore
import dayjs from '../../Dayjs/src/index';
import CtripL10nDateFormatter from './CtripL10nDateFormatter';

// @ts-ignore
import { typeOf } from './Utils';

const ctripFormatter = date => {
  return new CtripL10nDateFormatter(date);
};

const formatter = date => {
  return ctripFormatter(date);
};


/**
 * 获取取还车时间格式
 *
 *  @format
 *              F1  MM月DD日
 *              F2  MM-DD HH:mm
 * 取还车时间跨年逻辑优化:
 * 1. 取还车都在本年的，显示月日不显示年份
 * 2. 取还车时间都在次年的，显示年月日，如（2019年08月27日取车，2019年09月07日还车）
 * 3. 取车时间在本年，还车时间在次年的，显示年月日，如（2018年12月27日取车，2019年01月07日还车）
 *
 * pickUpTimeFormatCtrip(pTime, rTime, { F1: 'M月D日 HH:mm', F2: 'YYYY-MM-DD HH:mm' })
 * pickUpTimeFormatCtrip(pTime, rTime, { F1: 'YYYY年MM月DD日', F2: 'YYYY年MM月DD日' })
 *
 */
const ctripPickUpTimeFormat = (ptime, rtime, format) => {
  const curYear = dayjs().year();
  const { F1, F2 } = format;
  ptime = dayjs(ptime);
  rtime = dayjs(rtime);
  if (curYear === ptime.year() && curYear === rtime.year()) {
    return {
      pickUpDateStr: ptime.format(F1),
      dropOffDateStr: rtime.format(F1),
    };
  }
  return {
    pickUpDateStr: ptime.format(F2),
    dropOffDateStr: rtime.format(F2),
  };
};

const ctripDateFormat = ({ ptime, rtime, mode = 'header' }) => {
  if (mode === 'header')
    return ctripPickUpTimeFormat(ptime, rtime, {
      F1: 'M月D日 HH:mm',
      F2: 'YYYY-MM-DD HH:mm',
    });
  if (mode === 'short')
    return ctripPickUpTimeFormat(ptime, rtime, {
      F1: 'MM月DD日',
      F2: 'YYYY年MM月DD日',
    });
  if (mode === 'noYearShort')
    return ctripPickUpTimeFormat(ptime, rtime, {
      F1: 'MM月DD日',
      F2: 'MM月DD日',
    });
  if (mode === 'calendar')
    return ctripPickUpTimeFormat(ptime, rtime, {
      F1: 'MM-DD HH:mm',
      F2: 'YYYY-MM-DD HH:mm',
    });
  if (mode === 'bookingHeader')
    return ctripPickUpTimeFormat(ptime, rtime, {
      F1: 'M月D日 HH:mm',
      F2: 'YYYY年M月D日 HH:mm',
    });
};
const ctripYearFormat = ({ ptime, rtime, format = 'YYYY年' }) => {
  const curYear = dayjs().year();
  ptime = dayjs(ptime);
  rtime = dayjs(rtime);
  if (curYear === ptime.year() && curYear === rtime.year()) {
    return { pickUpYearStr: '', dropOffYearStr: '' };
  }
  return {
    pickUpYearStr: ptime.format(format),
    dropOffYearStr: rtime.format(format),
  };
};
const ctripDayStr = time => {
  let dayStr = null;
  const nowDate = dayjs();
  const timeDate = dayjs(time);
  const tomorrowDate = nowDate.clone().add(1, 'days');

  if (timeDate.isSame(nowDate, 'day')) dayStr = '今天';
  if (timeDate.isSame(tomorrowDate, 'day')) dayStr = '明天';

  return dayStr;
};
const ctripWeekDayFormat = ({ ptime, rtime }) => {
  const week = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
  const pdateStr = ctripDayStr(ptime);
  const rdateStr = ctripDayStr(rtime);

  // Property 'isoWeekday' does not exist on type 'Dayjs'.ts(2339)
  // @ts-ignore
  if (!dayjs().isoWeekday) {
    // eslint-disable-next-line global-require
    const isoWeek =
      require('@ctrip/rn_com_car/dist/src/Dayjs/src/plugin/isoWeek').default;
    dayjs.extend(isoWeek);
  }

  // @ts-ignore
  const pickUpWeekStr = pdateStr || week[dayjs(ptime).isoWeekday() - 1];
  // @ts-ignore
  const dropOffWeekStr = rdateStr || week[dayjs(rtime).isoWeekday() - 1];
  return { pickUpWeekStr, dropOffWeekStr };
};

export {
  formatter,
  ctripDateFormat,
  ctripYearFormat,
  ctripWeekDayFormat,
};
