import { getPixel } from './Utils';

export const getWidth = (width: number) => ({ width: getPixel(width) });
export const getHeight = (height: number) => ({ height: getPixel(height) });

/**
 * Get style with width and height
 * @param {number} width
 * @param {number} height
 * @return {{width: number, height: number}}
 */
export const getWH = (width: number, height: number) => ({
  ...getWidth(width),
  ...getHeight(height),
});
