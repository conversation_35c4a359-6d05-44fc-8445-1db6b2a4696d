import _ from 'lodash';
// // @ts-ignore
import dayjs from '../../Dayjs/src/index';
import { useMemo, useRef } from 'react';
import { Dimensions, Platform } from 'react-native';
import { Device } from '@ctrip/crn';
import {
  OFFSET_BOTTOM,
  OFFSET_TOP,
  DEFAULT_IPHONEX_OFFSET_BOTTOM,
  DEFAULT_IPHONEX_OFFSET_TOP,
  timestamps,
} from './Constants';

export const dimensions = Dimensions.get('window');
// @ts-ignore
export const isAndroid = Platform.OS === 'android' || Platform.OS === 'harmony';

export const isIos = Platform.OS === 'ios';
// @ts-ignore
export const isHarmony = Platform.OS === 'harmony';

export const selector = _.curry((bool, trueVal, falseVal = null) =>
  bool ? trueVal : falseVal,
);

// /**
//  * dp数值转换
//  * lineHeight & borderRadius等有些场景需要取整
//  * @param {*} num 待转换的数值
//  * @param {*} round 数据方式(floor 向下取整，ceil向上取整)
//  */
export function getPixel(
  num: number,
  round?: 'ceil' | 'floor',
  designWidth: number = 750,
) {
  const MaxViewportWidth = Math.min(Dimensions.get('window').width, 540);
  let value = (num * MaxViewportWidth) / designWidth;
  if (MaxViewportWidth >= 540) {
    value = (num * 375) / designWidth;
  }
  if (round === 'ceil') value = Math.ceil(value);
  else if (round === 'floor') value = Math.floor(value);
  return value;
}

/**
 * iPhone 12 顶部刘海高度特殊，使用 Device.safeAreaTop，Device.safeAreaBottom 进行计算
 * @param iphoneXValue iPhone X的像素值
 * @param normalValue 非iPhone X的像素值
 * @param useBottom 是否使用 safeAreaBottom 计算，否则使用 safeAreaTop，Device
 */
export function getValueWithIphoneX(
  iphoneXValue: number,
  normalValue: number,
  useBottom: boolean = false,
) {
  if (Device.isiPhoneX) {
    // @ts-ignore
    const fixValue =
      typeof Device.safeAreaTop === 'number'
        ? // @ts-ignore
          (useBottom ? Device.safeAreaBottom : Device.safeAreaTop) -
          (useBottom
            ? DEFAULT_IPHONEX_OFFSET_BOTTOM
            : DEFAULT_IPHONEX_OFFSET_TOP)
        : 0;
    return iphoneXValue + fixValue;
  }
  return normalValue;
}

/**
 * iPhone 12 顶部刘海高度特殊，使用 Device.safeAreaTop 计算
 * @param iphoneXValue iPhone X的像素值
 * @param normalValue 非iPhone X的像素值
 */
export function getValueWithIphoneXTop(
  iphoneXValue: number,
  normalValue: number,
) {
  return getValueWithIphoneX(iphoneXValue, normalValue);
}

/**
 * iPhone 12 顶部刘海高度特殊，使用 Device.safeAreaBottom 计算
 * @param iphoneXValue iPhone X的像素值
 * @param normalValue 非iPhone X的像素值
 */
export function getValueWithIphoneXBottom(
  iphoneXValue: number,
  normalValue: number,
) {
  return getValueWithIphoneX(iphoneXValue, normalValue, true);
}

/**
 * iPhone 12 顶部刘海高度特殊，使用 Device.safeAreaTop 计算 pixel
 * @param iphoneXValue iPhone X的像素值
 * @param normalValue 非iPhone X的像素值
 */
export function getPixelWithIphoneXTop(
  iphoneXValue: number,
  normalValue: number,
) {
  return getValueWithIphoneXTop(getPixel(iphoneXValue), getPixel(normalValue));
}

/**
 * iPhone 12 顶部刘海高度特殊，使用 Device.safeAreaBottom 计算 pixel
 * @param iphoneXValue iPhone X的像素值
 * @param normalValue 非iPhone X的像素值
 */
export function getPixelWithIphoneXBottom(
  iphoneXValue: number,
  normalValue: number,
) {
  return getValueWithIphoneXBottom(
    getPixel(iphoneXValue),
    getPixel(normalValue),
  );
}

export const ensureFunctionCall = (fn, context?: any, ...args) => {
  context = context || this;
  if (typeof fn === 'function') {
    return fn.apply(context, args);
  }
};

/**
 * return the type of Object
 * @param {*} obj
 */
export const typeOf: (obj: any) => string = (obj: object): string =>
  Object.prototype.toString.call(obj).slice(8, -1);

/**
 * produce uuid
 */
export const uuid = (len?: number) => {
  let d = new Date().getTime();
  const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (d + Math.random() * 16) % 16 | 0;
    d = Math.floor(d / 16);
    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);
  });
  if (len) {
    return uuid.slice(0, len);
  }
  return uuid;
};

/**
 * to fix Iphone X and android header(Because android uses an immersive status bar)
 * @param {number} number
 */
export function fixOffsetTop(number = 0) {
  number = isNaN(+number) ? 0 : +number;
  return number + OFFSET_TOP;
}
/**
 * to fix Iphone X footer
 * @param {number} number
 * @param {number} offset
 */
export function fixIOSOffsetBottom(number = 0, offset?: number) {
  number = isNaN(+number) ? 0 : +number;
  if (offset && Device.isiPhoneX) {
    number += offset;
  }
  return number + OFFSET_BOTTOM;
}

export function adaptNoaNomalousBottom() {
  return Platform.select({
    // @ts-ignore
    ios: Device.safeAreaBottom ? 0 : getPixel(16),
    android: getPixel(16),
    // @ts-ignore
    harmony: getPixel(16),
  });
}

/**
 * transform icon string to code
 */
export const htmlDecode = (str = '') => {
  try {
    str = str.replace(/&#(x)?(\w+)(;)?/g, ($, $1, $2) =>
      String.fromCodePoint(parseInt($2, $1 ? 16 : 10)),
    );
    return str;
  } catch (e) {
    return '';
  }
};

// 模拟css的vw单位, 第二个参数是父元素的宽度, 不传则是屏幕宽度
export const vw = (num: number, parent?: number) => {
  const { width } = Dimensions.get('window');
  return (num / 100) * (parent || width);
};

// 模拟css的vh单位, 第二个参数是父元素的高度, 不传则是屏幕高度
export const vh = (num: number, parent?: number) => {
  const { height } = Dimensions.get('screen');
  return (num / 100) * (parent || height);
};

/**
 * Return url with protocol header
 * @param {string} url
 * @return {string}
 */
export const autoProtocol = (url, isHttps = true) => {
  if (!url) {
    return '';
  }
  url = url.trim();
  const protocol = url.includes('ctrip.com') || isHttps ? 'https' : 'http';
  const intelligentURL = url.replace('http:', '').replace('https:', '');
  const withPreUrl = `${url.includes('//') ? '' : '//'}${intelligentURL}`;
  if (url.includes('?')) {
    return `${protocol}:${withPreUrl}`;
  }
  return `${protocol}:${withPreUrl}?timestamps=${timestamps}`;
};

/**
 * return the Difference value of two date
 * @param {Date} btime begain time
 * @param {Date} etime end time
 */
export const getDayGap = (btime, etime) => {
  const startDate = dayjs(btime);
  const endDate = dayjs(etime);
  if (startDate.valueOf() > endDate.valueOf()) {
    return Math.ceil(startDate.diff(endDate, 'days', true));
  }
  return Math.ceil(endDate.diff(startDate, 'days', true));
};

export const cloneDeep = (content: object) => {
  const fixContent = content === undefined ? null : content;
  return JSON.parse(JSON.stringify(fixContent));
};
/**
 * 适配url为标准http url
 */
export const fixProtocol = autoProtocol;
// export const isNotEmpty = (content: string) => !!content;

// 国内租车时长计算
export const isd_dhm = (pickUpDate, dropOffDate) => {
  // 兼容海外osd的时令问题，(用new Date().getTime()会默认转成当地时区，会产生时差)
  const ms = dayjs(dropOffDate).diff(dayjs(pickUpDate));
  if (ms < 0) return;

  const cd = 24 * 60 * 60 * 1000;
  const ch = 60 * 60 * 1000;
  let d = Math.floor(ms / cd);
  let h = Math.floor((ms - d * cd) / ch);
  let m = Math.round((ms - d * cd - h * ch) / 60000);
  // let pad = (n) => n < 10 ? '0' + n : n

  // 向上取整
  if (m > 0) {
    h++;
    m = 0;
  }

  if (h === 24) {
    d++;
    h = 0;
  }

  return (d > 0 ? `${d}天` : '') + (h > 0 ? `${h}小时` : '');
};

// 比较版本号
export function versionCompare(v1, v2) {
  v1 = v1.split('.');
  v2 = v2.split('.');

  let i = 0;
  const len1 = v1.length;
  const len2 = v2.length;
  while (i < len1 && i < len2) {
    const a = v1[i];
    const b = v2[i];
    i += 1;
    if (a === b) {
      continue;
    } else {
      return a - b > 0 ? 1 : -1;
    }
  }

  return len1 > len2 ? 1 : len1 < len2 ? -1 : 0;
}

/**
 * 比较前后对象是否相等，用于重复渲染判断控制
 * @param prev
 * @param next
 * @returns
 */
export const compareProps = (prev, next) => {
  let isEqual = true;
  if (!(prev && next)) {
    return false;
  }
  _.forEach(prev, (value, key) => {
    let objIsEqual = false;
    if (typeof prev[key] === 'object') {
      try {
        objIsEqual = JSON.stringify(prev[key]) === JSON.stringify(next[key]);
      } catch (e) {
        objIsEqual = prev[key] === next[key];
      }
    } else {
      objIsEqual = prev[key] === next[key];
    }
    isEqual = isEqual && objIsEqual;
  });

  return isEqual;
};

type noop = (...args: any[]) => any;

export function useMemoizedFn<T extends noop>(fn: T) {
  if (process.env.NODE_ENV === 'development') {
    if (typeof fn !== 'function') {
      console.error(
        `useMemoizedFn expected parameter is a function, got ${typeof fn}`,
      );
    }
  }

  const fnRef = useRef<T>(fn);

  // why not write `fnRef.current = fn`?
  // https://github.com/alibaba/hooks/issues/728
  fnRef.current = useMemo(() => fn, [fn]);

  const memoizedFn = useRef<T>();
  if (!memoizedFn.current) {
    memoizedFn.current = function (...args) {
      // eslint-disable-next-line @typescript-eslint/no-invalid-this
      return fnRef.current.apply(this, args);
    } as T;
  }

  return memoizedFn.current;
}
