/* eslint-disable */
import dayjs from '../../Dayjs/src/index';

export default class CtripL10nDateFormatter {
  timestamp: any;
  constructor(time) {
    this.timestamp = dayjs(time);
  }

  /**
   * eg: Oct 1, 2017
   */
  ymdShortString() {
    return this.timestamp.format('YYYY年MM月DD日');
  }

  /**
   * eg: 00:00
   */
  hmString() {
    return this.timestamp.format('HH:mm');
  }

  /**
   * eg: 2017
   */
  yString() {
    return this.timestamp.format('YYYY');
  }
}
