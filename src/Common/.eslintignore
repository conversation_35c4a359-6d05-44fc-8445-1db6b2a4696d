#https://cloud.tencent.com/developer/section/1135570
# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
project.xcworkspace

# Android/IJ
#
*.iml
.idea
.gradle
local.properties

# node.js
#
node_modules/
coverage/
npm-debug.log

# BUCK
buck-out/
\.buckd/
android/app/libs
android/keystores/debug.keystore

# tmp
tmp/

# happypack temp folder
.happypack/

# crn run temp folder
.__tmp/

# crn-web temp folder
.cw/

metro.config.js

crnwebconf.js
.jest

*.json.bak

# todo: error  Parsing error: Unexpected token, expected ","
src/Components/App/CPage.tsx
src/Components/App/WithLogCode.tsx
src/State/Product/Enums.ts
src/ComponentBusiness
src/common

# config

scripts/
src/
dist/