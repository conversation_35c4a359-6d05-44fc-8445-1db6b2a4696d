{"compilerOptions": {"target": "es6", "module": "es6", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "declaration": true, "noImplicitAny": false, "allowJs": true, "jsx": "react-native", "outDir": "./dist", "sourceMap": false, "skipLibCheck": true, "noImplicitReturns": false, "noUnusedParameters": false, "mapRoot": "", "lib": ["es6", "dom", "es2017", "es2019"], "composite": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "*.ts", "*.tsx"], "exclude": ["dist", "node_modules", "**/dist/", "**/*.test.ts"], "compileOnSave": false}