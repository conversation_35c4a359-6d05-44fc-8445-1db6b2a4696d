const fs = require('fs');
const babel = require('@babel/core');
const t = require('@babel/types');

const IndexSlot = `
Object.defineProperty(exports, "{moduleName}", {
  enumerable: true,
  set: function(){},
  get: function () {
    return {packageFunction}require("{sourceUrl}"){marketUrl}{rightBracket};
  }
});`;

const defaultExportSlot = `
var {name} = _interopRequireDefault(require("{sourceUrl}"));
var _default = {name}.default;
exports.default = _default;
`;

const indexModuleFileContent = `
"use strict";

function _getRequireWildcardCache() { if (typeof WeakMap !== "function") return null; var cache = new WeakMap(); _getRequireWildcardCache = function () { return cache; }; return cache; }

function _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== "object" && typeof obj !== "function") { return { default: obj }; } var cache = _getRequireWildcardCache(); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

function getRealValue(obj){
  if(obj && obj.default){
    return obj.default;
  }
  return obj;
}

Object.defineProperty(exports, "__esModule", {
  value: true
});
`;

const indexModuleFileContentSlot = `
${indexModuleFileContent}

{replaceContent}
`;

const getInitContent = () => ({
  import: '',
  export: '',
});
let replaceContent = getInitContent();

const config = {
  suffix: '.default',
  exportPrefix: 'exports.',
  getModuleFunction: '_interopRequireWildcard(',
  rightBracket: ')',
  defaultExportStr: 'void 0;',
};

let nameAndSource = {};

// 模板转换工具
function format(template, args) {
  if (!args) return template;
  let str = template;
  for (let key in args) {
    str = str.replace(new RegExp('\\{' + key + '\\}', 'g'), args[key]);
  }
  return str;
}

let omitTans = false;

// 处理import&export
const visitor = {
  ImportDeclaration(path) {
    const specifiers = path.node.specifiers;
    const sourceUrl = path.node.source.value;
    // 非 ./ 开头，认为非入口文件
    if (!sourceUrl.startsWith('.')) {
      omitTans = true;
      return;
    }

    specifiers.map((specifier, i) => {
      let marketUrl = '';
      let packageFunction = '';
      let rightBracket = '';
      const moduleName = specifier.local.name;
      if (t.isImportDefaultSpecifier(specifier)) {
        marketUrl = config.suffix;
      }

      if (t.isImportSpecifier(specifier)) {
        packageFunction = 'getRealValue(';
        marketUrl = `.${specifier.imported.name}`;
        rightBracket = config.rightBracket;
      }

      if (t.isImportNamespaceSpecifier(specifier)) {
        packageFunction = config.getModuleFunction;
        rightBracket = config.rightBracket;
      }

      const definePropertyStr = format(IndexSlot, {
        moduleName,
        packageFunction,
        sourceUrl,
        marketUrl,
        rightBracket,
      });

      nameAndSource[moduleName] = sourceUrl;
      replaceContent.import += `${definePropertyStr}\n`;
    });
  },
  ExportNamedDeclaration(path) {
    const specifiers = path.node.specifiers;
    let exportStr = '';
    specifiers.map(specifier => {
      exportStr += `${config.exportPrefix}${specifier.exported.name} = `;
    });
    exportStr += config.defaultExportStr;
    replaceContent.import += exportStr;
  },
  ExportDefaultDeclaration(path) {
    const name = path.node.declaration.name;
    const sourceUrl = nameAndSource[name];
    const defaultExportStr = format(defaultExportSlot, {
      name: `_${name}`,
      sourceUrl,
    });
    replaceContent.import += defaultExportStr;
  },
  // 适配 Index.Ctrip
  // IfStatement() {
  //   omitTans = true;
  // }
};

const work = ({ indexPath, outPath }) => {
  if (!fs.existsSync(indexPath)) {
    console.log('不存在此文件');
    return;
  }
  // 读文件内容
  var content = fs.readFileSync(indexPath).toString();
  replaceContent = getInitContent();
  nameAndSource = {};
  omitTans = false;
  babel.transform(content, {
    plugins: [
      {
        visitor: visitor,
      },
    ],
    filename: indexPath,
  });

  if (omitTans || !replaceContent.import) {
    return;
  }

  // 生成index.module.js文件
  fs.writeFileSync(
    outPath,
    format(indexModuleFileContentSlot, {
      replaceContent: replaceContent.import + replaceContent.export,
    }),
  );
  return true;
};

module.exports = work;
