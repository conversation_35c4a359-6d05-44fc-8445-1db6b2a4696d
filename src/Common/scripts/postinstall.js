const fs = require('fs-extra');
const path = require('path');
const loadingImgSrcPath = path.resolve(
  __dirname,
  '../src/Components/Basic/Loading/img',
);
// TIPS @rn_com_car 打入node_modules,loadingImgDistPath'../dist/src/Components/Basic/Loading/img',
const loadingImgDistPath = path.resolve(
  __dirname,
  '../../../build/src/Common/src/Components/Basic/Loading/img',
);

const DayJsPath = path.resolve(__dirname, '../src/Dayjs');
// TIPS @rn_com_car 打入node_modules,loadingImgDistPath'../dist/src/src/dist/src/Dayjs',
const DayJsDistPath = path.resolve(
  __dirname,
  '../../../build/src/Common/src/Dayjs',
);

const move = () => {
  fs.copySync(loadingImgSrcPath, loadingImgDistPath);
  fs.copySync(DayJsP<PERSON>, DayJsDistPath);
};

move();
