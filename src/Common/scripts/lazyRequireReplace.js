const path = require('path');
const replace = require('./addIndexModule');
const { recursion } = require('./utils');

const lazyRequireReplace = (file, match) => {
  if (file.includes(match)) {
    let success = true;
    try {
      success = replace({
        indexPath: file,
        outPath: file,
      });
    } catch (e) {
      console.log(`lazyRequireReplace file error: ${file}`);
      success = false;
    }
    if (success) {
      console.log(`lazyRequireReplace file: ${file}`);
    }
  }
};

// TIPS @rn_com_car 打入node_modules, distPath需要替换成'../dist'
const distPath = '../../../build/src/Common';

recursion(path.resolve(__dirname, distPath), file => {
  if (!file.includes('Dayjs')) {
    lazyRequireReplace(file, '/index.js');
  }
});
