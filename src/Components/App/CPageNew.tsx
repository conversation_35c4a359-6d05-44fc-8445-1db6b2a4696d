/* eslint-disable no-empty */
/* eslint-disable no-underscore-dangle */
/* eslint-disable class-methods-use-this */
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import React from 'react';
import { DeviceEventEmitter, Dimensions } from 'react-native';
import { Page, IBasePageProps, Log } from '@ctrip/crn';
import { AppContext, CarLog, CarDevHubTrace } from '../../Util/Index';
import { LogKey, LogKeyDev } from '../../Constants/Index';
import { PagePerfomance, PerformanceType } from './PagePerfomance';
import ViewPort from './CViewPort';
import ErrorKey from '../../Constants/ErrorKey';
import { PageSearchTraceType } from '../../Constants/CommonEnums';
import { logBasicInfo } from '../../Util/AppBaseInfoMapNew';

let enablePush = true;
let pushTimer = null;

export interface IStateType {
  lang?: string;
  messages?: any;
}

export interface ICPageProps extends IBasePageProps {
  performanceMonitor: PerformanceType;
  page: any;
}

export interface IExtrasTraceInfo {
  orderId?: number;
  extraData?: Object;
}

export default class CPage<
  P extends IBasePageProps,
  S extends IStateType,
> extends Page<P, S> {
  pageAppearCount: number = 0; // 标识是否首次加载页面，首次加载时是0

  isPageReady: Boolean = false; // 页面可交互标识

  isPageAppear: Boolean = true;

  performanceMonitor: PerformanceType;

  sceneConfig: any = null;

  needLogPage: boolean = false; // immediatelyResetRouteStack 需要手动发送 PV

  // @ts-ignore
  timerList: Array<NodeJS.Timeout>;

  /**
   * 是否是初始化页面 用于页面渲染未完成时禁用侧滑
   */
  isInitialPage: boolean = true;

  /**
   * 是否页面展示时刷新禁用侧滑状态
   */
  isResetEnableDragByAppear: boolean = false;

  /**
   * 当前页面禁用侧滑状态，true表示当前页面可以侧滑
   */
  isEnableDrag: boolean = true;

  __crnDimensionChangeEvent = null;

  constructor(prop: P) {
    super(prop);
    this.performanceMonitor = PagePerfomance(this);
    this.timerList = [];
    AppContext.setPageInstance(this);
    this.__crnDimensionChangeEvent = DeviceEventEmitter.addListener(
      'didUpdateDimensions',
      function (e) {
        if (e && e.fromCRN) {
          this.onWindowSizeChanged(Dimensions.get('window'));
        }
      }.bind(this),
    );
  }

  UNSAFE_componentWillMount() {
    this.performanceMonitor.firstRenderStart = new Date();
  }

  clearTimer() {
    this.timerList.map(item => clearTimeout(item));
  }

  // @ts-ignore
  addTimer(timerId: NodeJS.Timeout) {
    this.timerList.push(timerId);
  }

  getPageId() {
    return '';
  }

  getPageParamInfo() {
    return {};
  }

  setEnableDragBack = (enable: boolean = true) => {
    try {
      // 设置侧滑需在当前页面，否则设置页面展示时刷新禁用侧滑状态位
      if (!this.isPageAppear) {
        this.isResetEnableDragByAppear = true;
        // 设置当前禁用侧滑状态，用于页面展示时刷新禁用侧滑状态
        this.isEnableDrag = true;
        return;
      }
      this.isEnableDrag = enable;
      // 通过当前的路由数量来判断禁用Native测滑还是webView测试
      if (enable) {
        CPage.enableNativeDragBack();
      } else {
        CPage.disableNativeDragBack();
      }
    } catch (error) {
      CarLog.LogError(ErrorKey.e_cpage_set_enable_drag_back, { error });
    }
  };

  enableDragBack = () => {
    this.setEnableDragBack();
  };

  disableDragBack = () => {
    this.setEnableDragBack(false);
  };

  createPageName = (pageName = '') => {
    return `${pageName}${dayjs().format('YYYYMMDDHHmmssSSS')}`;
  };

  onPageReadyAfter() {
    // 子页面实现此方法，页面可操作后调用
  }

  // 子页面实现此方法，页面可追加其它reducer、logic、sharkMessage文件时调用
  lazyLoadOtherModules() {}

  lazyComponentDidMount() {
    // 子页面实现此方法，
    // 当页面进入时有复杂View需要渲染时，使用其优化页面切换速度
  }

  getPVOption() {
    if (!__DEV__) {
      return {
        ...logBasicInfo(),
      };
    }
    return {};
  }

  sendPagePV() {
    const pvInfo = this.getPageParamInfo ? this.getPageParamInfo() : {};
    CarLog.LogTrace({
      key: LogKey.c_car_page_search,
      info: {
        pageId: this.getPageId(),
        // @ts-ignore 解决埋点漂移
        targetPageRef: this.targetPageRef,
        ...pvInfo,
        traceType: PageSearchTraceType.PV,
      },
    });
  }

  pageDidAppear() {
    // 由于crn064版本第一次初始化时不会调用，crn070版本每次显示都会执行，包括初始化，所以加一个标记表示第一次执行
    AppContext.setPageInstance(this);
    this.pageAppearCount += 1;
    this.performanceMonitor.pageLastActiveTime = new Date();
    this.isPageAppear = true;
    if (this.isResetEnableDragByAppear) {
      this.isResetEnableDragByAppear = false;
      this.setEnableDragBack(this.isEnableDrag);
    }
  }

  onDidFocus(route) {
    this.logPage(route);
  }

  pageDidDisappear(extrasTraceInfo?: IExtrasTraceInfo) {
    const { orderId = '', extraData } = extrasTraceInfo || {};
    this.isPageAppear = false;
    if (this.performanceMonitor.pageLastActiveTime) {
      // 2021-06-01 对于停留时长埋点做一个负数过滤处理，如果是负数的，则不埋，且新增一个开发埋点，来记录出现负数的情况
      const stayTime = this.performanceMonitor.getPageActiveTime();
      const traceFun = stayTime > 0 ? CarLog.LogTrace : CarLog.LogTraceDev;
      const key =
        stayTime > 0
          ? LogKey.c_car_trace_page_active_time
          : LogKeyDev.c_car_trace_abnormal_stay_time;
      traceFun({
        key,
        info: {
          pageId: this.getPageId(),
          value: stayTime,
          orderId,
          extrasTraceInfo: extraData || {},
          // 2020-09-03 先注释,因为会引发数据太大导致埋点上传失败的问题
          // reduxActions: JSON.stringify(ReduxData.getActions()),
        },
      });
    }
    this.performanceMonitor.pageLastActiveTime = null;
  }

  push(name: string, ...args: any) {
    if (enablePush) {
      enablePush = false;
      // 定时器
      pushTimer = setTimeout(() => {
        enablePush = true;
      }, 1000); // 一秒内不能重复点击
      super.push(name, ...args);
    }
  }

  pop(name?: string, info?: any) {
    // @ts-ignore
    const { navigation = {} } = this.props;
    const routes = navigation.getCurrentRoutes() || [];
    if (name) {
      const popName = routes.find(item => item?.name === name);
      if (popName) {
        super.pop(name, info);
      } else {
        super.pop();
      }
    } else {
      super.pop();
    }
  }

  logPageInfo = () => {
    // 上报页面信息到devhub
    CarDevHubTrace.logPageInfo({
      otherInfo: this.getPageParamInfo?.(),
    });
  };

  componentDidMount() {
    this.performanceMonitor.firstRenderEnd = new Date();
    setTimeout(() => {
      this.lazyComponentDidMount();
    });
  }

  componentWillUnmount() {
    this.clearTimer();
    clearTimeout(pushTimer);
    enablePush = true;
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_page_componentWillUnmount,
      info: {},
    });
    if (this.__crnDimensionChangeEvent) {
      this.__crnDimensionChangeEvent.remove();
    }
  }

  onWindowSizeChanged() {
    // DO SIZE UPDATE
  }

  logPage(route) {
    if (!this.needLogPage) return;
    this.needLogPage = false; // 设置是否需要手动发送埋点标志位为false, 防止重复手动发送
    const pvOption: any = this.getPVOption ? this.getPVOption() : {};
    const orderId = pvOption.orderid || '';

    let params = '';
    if (route.params) {
      try {
        params = `&params=${encodeURIComponent(JSON.stringify(route.params))}`;
      } catch (err) {}
    }
    let pageName = '';
    if (route.name) {
      pageName = `&pageName=${route.name}`;
    }

    try {
      Log.logPage(String(this.getPageId()), {
        ...pvOption,
        // @ts-ignore 解决埋点漂移
        targetPageRef: this.targetPageRef,
        // @ts-ignore Page constructor 时赋值
        url: (this.url + pageName + params).substr(0, 2048),
        orderID: orderId,
      });
    } catch (err) {
      if (__DEV__) {
        throw new Error(err);
      }
    }
  }

  render() {
    // @ts-ignore
    return <ViewPort>{this.renderPage()}</ViewPort>;
  }

  renderPage() {
    // 子页面实现此方法，代替render
  }
}
