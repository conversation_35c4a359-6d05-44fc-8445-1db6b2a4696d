import { useEffect } from 'react';
import { getLocationSelectorPromises } from '../../../Pages/Location/Utils';
import { locationResStorageKey } from '../Utils/Config';
import { CarStorage } from '../../../Util/Index';
import { locationAndDateStore } from '../../../State/Index';

const PreFetchEffect = () => {
  const rentalLocation = locationAndDateStore(state => state.rentalLocation);
  const pcid = rentalLocation.pickUp.cid;
  const rcid = rentalLocation.dropOff.cid;
  useEffect(() => {
    async function fetchData() {
      const { isd, osd } = getLocationSelectorPromises().city();
      const promises = [isd, osd];
      if (pcid) {
        const area = getLocationSelectorPromises().area(`${pcid}`);
        promises.push(area);
      }
      if (rcid && rcid !== pcid) {
        const area = getLocationSelectorPromises().area(`${rcid}`);
        promises.push(area);
      }
      const [isdData, osdData] = await Promise.all(promises);
      if (isdData?.cityList?.length && osdData?.cityList?.length) {
        CarStorage.save(locationResStorageKey.ISD, isdData, '30d');
        CarStorage.save(locationResStorageKey.OSD, osdData, '30d');
      }
    }
    fetchData();
  }, [pcid, rcid]);
  return null;
};

PreFetchEffect.defaultProps = {
  pcid: 0,
  rcid: 0,
};

export default PreFetchEffect;
