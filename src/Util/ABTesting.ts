import { ABTesting, Util, Application, Log, Device } from '@qnpm/ctrip/crn';
import _ from 'lodash';
import BbkChannel from '../Common/src/Utils';
import AppContext from './AppContext';
import Utils from './Utils';
import LogKeyDev from '../Constants/LogKeyDev';
import { getResAbversion } from './ServerABTesting';

export interface AbItemType {
  key: string;
  defaultValue: string;
  isActive: boolean;
  noVersionVal: boolean;
  isSync: boolean;
}

export interface AbResultType {
  ExpCode: string;
  BeginTime: string;
  EndTime: string;
  ExpVersion: string;
  ExpDefaultVersion: string;
  State: boolean;
  Attrs: any;
  ExpResult: any;
}

/**
 * 新增与下线实验需更新conf http://conf.ctripcorp.com/pages/viewpage.action?pageId=347791611
 * defaultValue 定义哪个版本为新版
 * noVersionVal 当实验号下线后，不返回ExpVersion，值true/false代表的是走新版还是旧版
 */
export const AbTestingKey = {
  CitySelector: {
    key: '200911_DSJT_xsou',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
  CalendarPrice: {
    key: '210323_DSJT_rlj',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
  IsNewList: {
    key: '210413_DSJT_lbyxg',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
};

// AB按条件触发获取
export const ABOptionKey = {
  Calendar: {
    key: '210508_DSJT_nrili',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
  // 售前供应商虚拟小号
  VirtualNumber: {
    key: '220114_DSJT_gysdh',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
  ZcSortPrice: {
    key: '211008_jt_zc_teat_zcsortprice',
    defaultValue: 'B',
    isActive: true,
    isQunar: true,
    noVersionVal: false,
    isSync: true,
  },
  // HomeCombinTab: {
  //   key: '220519_jt_zc_lcrh',
  //   defaultValue: 'B',
  //   isActive: true,
  //   isQunar: true,
  //   noVersionVal: false,
  //   isSync: true,
  // },
  // 车型组名称调整
  ListGroupName: {
    key: '220425_DSJT_group',
  },
  // 订祥挽留及召回
  OrderDetain: {
    key: '230213_DSJT_dxzh3',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
};
// @qunar change
const getQunarActiveAbs = (isSync: boolean) =>
  Object.keys(AbTestingKey).filter(
    m =>
      AbTestingKey[m].isActive &&
      AbTestingKey[m].isSync === isSync &&
      AbTestingKey[m].isQunar,
  );

export const getActiveAbs = (isSync: boolean) =>
  Object.keys(AbTestingKey).filter(
    m => AbTestingKey[m].isActive && AbTestingKey[m].isSync === isSync,
  );

const getABsExpCodes = (keys: Array<string>) => {
  const expDatas = [];
  keys.map(m => expDatas.push({ expCode: AbTestingKey[m].key }));
  return expDatas;
};
// @qunar change
const getQunarIsSyncAbs = (isSync: boolean) => () =>
  getABsExpCodes(getQunarActiveAbs(isSync));

const getIsSyncAbs = (isSync: boolean) => () =>
  getABsExpCodes(getActiveAbs(isSync));

export const AbIsTrue = (result: AbResultType, defaultAb: AbItemType) => {
  const version = result && result.ExpVersion;
  if (version) {
    return version.toUpperCase() === defaultAb.defaultValue;
  }
  return defaultAb.noVersionVal;
};

const getAbsExpCodes = getIsSyncAbs(false);
const getAbsExpCodesSync = getIsSyncAbs(true);
const getQunarAbsExpCodes = getQunarIsSyncAbs(true);

const getWebAbsSync = async () => {
  const expCodes = getAbsExpCodesSync();
  const promises = expCodes.map(v =>
    ABTesting.getABTestingInfoSync(v.expCode, {}),
  );
  const resultAll = await Promise.all(promises);
  const result: any = {};
  resultAll.forEach((v, i) => {
    const { expCode } = expCodes[i];
    result[expCode] = v;
  });
  AppContext.setABTesting(result);
  // @qunar-change
  // Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
  //   name: 'getWebAbsSync',
  //   result: JSON.stringify(resultAll),
  // });
};

export const getAbBoolean = (AbInfo: AbItemType) => {
  const Ab = AppContext.ABTesting.datas[AbInfo.key];
  return AbIsTrue(Ab, AbInfo);
};

const getAbs = () => {
  const expCodes = getAbsExpCodes();
  if (BbkChannel.isWeb()) {
    getWebAbsSync();
    return;
  }
  // @ts-ignore
  ABTesting.getMultiABTestingInfo(expCodes, result => {
    AppContext.setABTesting(result);
    // @qunar-change
    // Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
    //   name: 'getAbs',
    //   result: JSON.stringify(result),
    // });
  });
};

/* eslint-disable no-unused-vars */
const getAbsSync = async () => {
  const expCodes = getQunarAbsExpCodes();
  if (BbkChannel.isWeb()) {
    getWebAbsSync();
    return;
  }
  expCodes.forEach(async item => {
    const result = await ABTesting.getMultiABTestingInfoPromise(item.expCode);
    AppContext.setABTesting(result);
  });
  // @qunar-change
  // Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
  //   name: 'getAbsSync',
  //   result: JSON.stringify(result),
  // });
};

const getAbsSyncV2 = () => {
  if (!Util.isInChromeDebug) {
    const expCodes = getAbsExpCodesSync();
    // @ts-ignore
    const result = ABTesting.getMultiABTestingInfoSync(expCodes);
    if (result) {
      AppContext.setABTesting(result);
    }
    // @qunar-change
    // Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
    //   name: 'getAbsSyncV2',
    //   result: JSON.stringify(result),
    // });
  }
};

export const getAbExpVersion = (AbInfo: AbItemType) =>
  AppContext.ABTesting.datas[AbInfo.key]
    ? AppContext.ABTesting.datas[AbInfo.key].ExpVersion
    : '';

export const initializeABTesting = () => {
  getAbs();
};

export const initializePreABTesting = async () => {
  await getAbsSync();
};

// @qunar change === true
export const isCreditRent = () => Utils.isCtripIsd();

// export const isListInBatch = () => Utils.isCtripIsd() && getAbBoolean(AbTestingKey.ListInBatch);

// 2021-2-1 分页上线时先下线分批实验
export const isListInBatch = () => false;
export const isListInPage = () => {
  const { listPageIsEffect } = AppContext.ABTesting;
  if (Utils.isCtripIsd() && listPageIsEffect) {
    return true;
  }
  if (Utils.isCtripOsd() && !listPageIsEffect) {
    return true;
  }
  return false;
};

export const isGoodsShelves = () =>
  !AppContext.isModifyOrderRebook && Utils.isCtripIsd();

// @qunar change 修改订单
export const isGoodsShelves2 = () => {
  // if (AppContext.isModifyOrderRebook) return false;
  return Utils.isCtripIsd();
};

// 非 Chrome模式下检查响应值
export const initializePreABTestingSync = () => {
  getAbsSyncV2();
};
// @qunar change
export const getAbVersionStr = () => {
  // 统一使用trace 来返回当前的abVersion，这种写法会导致CarLog中abVersion为空
  return AppContext.ABTesting.trace;
  // let abVersions = [];
  // getQunarAbsExpCodes().forEach(
  //   ({ expCode }) =>
  //     AppContext.ABTesting.datas[expCode] &&
  //     abVersions.push(`${expCode}|${AppContext.ABTesting.datas[expCode]}`),
  // );
  // return abVersions.join(',');
};

export const isZcSortPrice = () => true;

export const isNewCalendar = () =>
  Utils.compareVersion(Application.version, '8.35.0') >= 0;

const isOptionAbInitialized = key => !!AppContext.ABTesting.datas[key];

export const getOptionAb = (key, condition: boolean = true) => {
  if (!condition || isOptionAbInitialized(key)) return;
  const successCb = result => {
    const res: any = { [key]: result };
    AppContext.setABTesting(res);
    Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
      name: `getOptionAb-${key}`,
      result: JSON.stringify(result),
      trace: AppContext.ABTesting,
    });
  };
  if (Util.isInChromeDebug) {
    ABTesting.getABTestingInfo(key, {}, result => {
      successCb(result);
    });
  } else {
    const result = ABTesting.getABTestingInfoSync(key, {});
    successCb(result);
  }
};

export const getShowVirtualNumberAb = () => {
  if (
    !Utils.isCtripIsd() ||
    showVirtualNumberAbInitialized() ||
    Util.isInChromeDebug
  )
    return;
  const result = ABTesting.getABTestingInfoSync(
    ABOptionKey.VirtualNumber.key,
    {},
  );
  const res: any = { [ABOptionKey.VirtualNumber.key]: result };
  AppContext.setABTesting(res);
  Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
    name: 'getShowVirtualNumberAb',
    result: JSON.stringify(result),
    trace: AppContext.ABTesting,
  });
};

export const showVirtualNumber = () => Utils.isCtripIsd();

export const isZhimaRes = () =>
  Utils.isCtripIsd() && getAbBoolean(ABOptionKey.IsZhimaRes);

export const getZhimaResAb = () => {
  if (
    !Utils.isCtripIsd() ||
    isZhimaResAbInitialized() ||
    Util.isInChromeDebug
  ) {
    return;
  }

  const result = ABTesting.getABTestingInfoSync(ABOptionKey.IsZhimaRes.key, {});
  const res: any = { [ABOptionKey.IsZhimaRes.key]: result };
  AppContext.setABTesting(res);
  Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
    name: 'getZhimaResAb',
    result: JSON.stringify(result),
    trace: AppContext.ABTesting,
  });
};

// 订详整体优化
export const isOrderDetailOptimization = () => Utils.isCtripIsd();

// 首页重构实验
export const getIsHomeRefactor = () =>
  Utils.isCtripIsd() && AppContext.isHomeCombine;

export const getIsListFilter = () => Utils.isCtripIsd();

export const getIsNewPOI = () => true;

export const getIsListGroupName = () => {
  return Utils.isCtripIsd();
};
export const isNewCalendarAbInit = () =>
  !!AppContext.ABTesting.datas[ABOptionKey.Calendar.key];

export const getNewCalendarAb = cb => {
  if (isNewCalendarAbInit()) {
    cb();
  } else {
    ABTesting.getABTestingInfo(ABOptionKey.Calendar.key, null, result => {
      const res: any = { [ABOptionKey.Calendar.key]: result };
      AppContext.setABTesting(res);
      Log.logDevTrace(LogKeyDev.c_car_trace_abtest, {
        name: 'getNewCalendarAb',
        result: JSON.stringify(result),
        trace: AppContext.ABTesting,
      });
      cb();
    });
  }
};

export const isInfoFlow = () => {
  // 首页信息流暂不同步
  return false;
};

// 组装前端abversion+服务端abversion
export const packageAbversion = (data: any = null) => {
  const serverAbversion = getResAbversion(data);
  const curAbVersion = AppContext.ABTesting.trace;
  const splitStr = ',';
  if (serverAbversion) {
    const curAbList = curAbVersion ? curAbVersion.split(splitStr) : [];
    return curAbList.concat(serverAbversion.split(splitStr)).join(splitStr);
  }
  return curAbVersion;
};

export const isOrderDetainAbInitialized = () => false;

// 是否订详挽留B版
export const orderDetailIsB = () => false;

// 是否订详挽留C版
export const orderDetailIsC = () => false;

// 是否订详挽留D版
export const orderDetailIsD = () => false;

// 是否订详挽留新版
export const isOrderDetainNewVersion = () => false;

// 因为订祥挽留项目的实验在取消页面分流，订祥的实验版本的判断需要加一个缓存
export const isOrderDetainOnOrderPage = async () => false;

// 实验下线时这个isOrderDetainOnOrderPage函数默认要返回true，或者调用的地方用true代替
// @qunar-change
export const isOrderDetain = () => false;

// 触发售后供应商IM实验分流
export const getOrderDetailVendorImAb = () =>
  getOptionAb(ABOptionKey.OrderDetailVendorIM.key, Utils.isCtripIsd());

// 售后供应商IM(B版不展示，其它版本都展示)
export const isOrderDetailVendorIm = () => Utils.isCtripIsd();

// 触发售前供应商IM实验分流
export const getProductDetailVendorImAb = () =>
  getOptionAb(ABOptionKey.ProductDetailIsVendorIM.key, Utils.isCtripIsd());

// 是否售前供应商IM的B版
export const productDetailVendorImIsB = () =>
  getAbExpVersion(ABOptionKey.ProductDetailIsVendorIM)?.toUpperCase() === 'B';

// 是否售前供应商IM的C版
export const productDetailVendorImIsC = () => true;

// 是否售前供应商IM新版(实验版本为B或者C)
export const isProductDetailVendorIm = () =>
  productDetailVendorImIsB() || productDetailVendorImIsC();

// 详情页支持修改时间地点ab初始化
export const isVendorListChangeDateAbInitialized = () =>
  !!AppContext.ABTesting.datas[ABOptionKey.VendorListPageChangeData.key];

// 详情页支持修改时间分流
export const getVendorListChangeDateAb = () =>
  getOptionAb(ABOptionKey.VendorListPageChangeData.key, Utils.isCtripIsd());

// 详情页支持修改时间地点B版
export const vendorListChangeDateIsB = () =>
  getAbExpVersion(ABOptionKey.VendorListPageChangeData)?.toUpperCase() === 'B';

// 详情页支持修改时间C版
export const vendorListChangeDateIsC = () =>
  getAbExpVersion(ABOptionKey.VendorListPageChangeData)?.toUpperCase() === 'C';

export const vendorListSupportChangeDate = () => {
  return false;
};

// 触发订单卡片实验分流
export const getOrderCardShowTipImAb = () =>
  getOptionAb(ABOptionKey.OrderCardShowTip.key, Utils.isCtripIsd());

// 是否订单卡片信息推荐模块新版
export const isOrderCardShowTip = () => true;

// 新版门店弹窗实验B版
export const isNewStoreDetailConfirm = () => false;

// 触发新版门店弹窗实验
export const getNewStoreDetailConfirm = () => false;
// getOptionAb(ABOptionKey.NewStoreDetailConfirm.key, Utils.isCtripIsd());

export const IsNewDetail = () => Utils.isCtripIsd();

export const isNewList = () => Utils.isCtripIsd();
