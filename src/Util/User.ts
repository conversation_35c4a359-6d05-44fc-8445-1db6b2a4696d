import { User, Util } from '@ctrip/crn';
import { CookieManager } from 'qunar-react-native';
import BbkChannel from '../Common/src/Utils';
import AppContext from './AppContext';
import Utils from './Utils';
import { QUNAR_USER_URL, ENV_TYPE } from '../Constants/Platform';

async function updateCookieAndGlobalUserInfo(){
  await updateCookie();

  // @QunarModule special_logic_do_not_change
  // @ts-ignore
  if (global.__loggerSetUserInfo) {
    // @ts-ignore
    global.__loggerSetUserInfo();
  }
}


export const toLogin = (): Promise<boolean> =>
  new Promise(resolve => {
    User.userLogin((status, userInfo) => {
      if (userInfo && userInfo.data && userInfo.data.Auth) {
        AppContext.setUserInfo(userInfo);
        // @qunar change 解除循环依赖
        updateCookieAndGlobalUserInfo();
        resolve(true);
      } else {
        resolve(false);
      }
    });
  });

export const getQunarUserUrl = (env: any) => {
  return QUNAR_USER_URL[env] || QUNAR_USER_URL[ENV_TYPE.PROD];
};


const getRegularCookie = () => {
  return new Promise((resolve, reject) => {
    CookieManager.getCookieForKey('_s', getQunarUserUrl(AppContext.env), (cookie) => {
      if (cookie && cookie.value) {
        resolve({
          'scookie': cookie.value,
        });
      } else {
        resolve({});
      }
    }, () => {
      resolve({});
    });
  });
};

export const updateCookie = async () => {
  const cookie = await getRegularCookie();
  AppContext.setCookie(cookie);
};

const checkUserInfo = userInfo => {
  if (
    userInfo &&
    userInfo.data &&
    (userInfo.data.UserID || userInfo.data.Auth)
  ) {
    AppContext.setUserInfo(userInfo);
    // 解除循环依赖
    updateCookieAndGlobalUserInfo();
    return true;
  }
  return false;
};

export const isLogin = async (): Promise<boolean> => {
  if (AppContext.UrlQuery && AppContext.UrlQuery.snapshotOid) {
    return true;
  }
  if (checkUserInfo(AppContext.UserInfo)) {
    return true;
  }
  return new Promise(resolve => {
    if (Util.isInChromeDebug || Utils.isTrip() || BbkChannel.isWeb()) {
      User.getUserInfo((status, userInfo) => {
        const isUserLogin = checkUserInfo(userInfo);
        resolve(isUserLogin);
      });
    } else {
      const userInfo = User.getUserInfoSync();
      const isUserLogin = checkUserInfo(userInfo);
      resolve(isUserLogin);
    }
  });
};

export const isLoginSync = () => {
  if (AppContext.UserInfo.Auth) {
    return true;
  }

  if (!Util.isInChromeDebug) {
    const userInfo = User.getUserInfoSync();
    return checkUserInfo(userInfo);
  }

  return false;
};

export function login(param = {}) {
  return new Promise((resolve, reject) => {
    User.getUserInfo((status, userInfo) => {
      if (userInfo && userInfo.data && !userInfo.data.noLogin) {
        // @ts-ignore
        const { isNonUser } = param;
        if (isNonUser !== undefined && !isNonUser && userInfo.data.IsNonUser) {
          User.userLoginWithParams(
            { showNonMember: true, isNonMemberOrder: true, ...param },
            (tstatus, tuserInfo) => {
              if (tuserInfo && tuserInfo.data && !tuserInfo.data.noLogin) {
                resolve(tuserInfo);
              }
            },
          );
        } else {
          resolve(userInfo);
        }
      } else {
        User.userLoginWithParams(
          { showNonMember: true, isNonMemberOrder: true, ...param },
          (tstatus, tuserInfo) => {
            if (tuserInfo && tuserInfo.data && !tuserInfo.data.noLogin) {
              resolve(tuserInfo);
            }
          },
        );
      }
    });
  });
}

export function noMemberLogin(param = {}) {
  return new Promise((resolve, reject) => {
    User.getUserInfo((status, userInfo) => {
      if (userInfo && userInfo.data && !userInfo.data.noLogin) {
        // @ts-ignore
        const { isNonUser } = param;
        if (isNonUser !== undefined && !isNonUser && userInfo.data.IsNonUser) {
          User.userLoginWithParams(
            { showNonMember: false, isNonMemberOrder: false, ...param },
            (tstatus, tuserInfo) => {
              if (tuserInfo && tuserInfo.data && !tuserInfo.data.noLogin) {
                resolve(tuserInfo);
              }
            },
          );
        } else {
          resolve(userInfo);
        }
      } else {
        User.userLoginWithParams(
          { showNonMember: false, isNonMemberOrder: false, ...param },
          (tstatus, tuserInfo) => {
            if (tuserInfo && tuserInfo.data && !tuserInfo.data.noLogin) {
              resolve(tuserInfo);
            }
          },
        );
      }
    });
  });
}
export const checkToLogin = async (): Promise<boolean> => {
  const isLoginBefore = await isLoginSync();
  if (isLoginBefore) {
    return true;
  }
  const isLoginNow = await toLogin();
  if (isLoginNow) {
    return true;
  }
  return false;
};
