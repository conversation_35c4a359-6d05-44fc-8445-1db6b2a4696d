import Yallist, { Node } from 'yallist';
import DebugConsole from './DebugConsole';

export interface Cache<T = any> {
  key: string;
  value: T;
  timestamp: number;
}
export interface CacheManagerOptions<T = any> {
  capacity?: number;
  timeout?: number;
  latestCacheTimeout?: number;
  isCacheValid: (cache: T) => boolean;
  autoClean: boolean;
  cleanInterval: number;
  logger?: DebugConsole;
}

const defaultCacheManagerOptions: CacheManagerOptions = {
  isCacheValid: () => true,
  autoClean: true,
  cleanInterval: 5 * 1000,
};

function isNode<T = any>(data: any): data is Node<any> {
  const node = data as Node<T>;
  return node.next !== undefined && node.prev !== undefined;
}

const isCache = (data: any): data is Cache => {
  const cache = data as Cache;
  return cache.key !== undefined && cache.timestamp !== undefined;
};

function getCacheFilter(key: string | Cache) {
  return typeof key === 'string'
    ? (item: Cache) => item.key === key
    : (item: Cache) => item.key === key.key;
}

class CacheManager<C> {
  private cacheList: Yallist<Cache<C>>;

  private cleanIntervalTimeout: number;

  options: CacheManagerOptions;

  constructor(options?: Partial<CacheManagerOptions>) {
    this.options = {
      ...defaultCacheManagerOptions,
      ...options,
      latestCacheTimeout: options.latestCacheTimeout || options.timeout,
    } as CacheManagerOptions;
    this.cleanIntervalTimeout = 0;
    this.cacheList = Yallist.create();
  }

  /**
   * iterator generator of cacheList
   * @param {Function} fn iterator of cacheList,
   * `fn` returns a flag determine whether to traverse all nodes
   * true -> stop traverse,
   * false -> traverse all nodes
   * @param {Object} context context for this
   * @returns {void} void
   */
  private iterator(
    fn: (
      node: Yallist.Node<Cache<C>>,
      index: number,
      list: Yallist<Cache<C>>,
    ) => boolean,
    context?: any,
  ): void {
    const { cacheList } = this;
    const thisp = context || cacheList;
    for (let walker = cacheList.head, i = 0; walker !== null; i += 1) {
      const flag = fn.call(thisp, walker, i, cacheList);
      if (flag) {
        break;
      }
      walker = walker.next;
    }
  }

  private findNode(
    fn: (value: Cache<C>, index: number, list: Yallist<Cache<C>>) => boolean,
    context?: any,
  ): Node<Cache<C>> | void {
    let result: Node<Cache<C>> | void;
    this.iterator((node, index, list) => {
      const flag = fn.call(context || list, node.value, index, list);
      if (flag) {
        result = node;
      }
      return flag;
    });
    return result;
  }

  filter(
    fn: (value: Cache<C>, index: number, list: Yallist<Cache<C>>) => boolean,
    context?: any,
  ) {
    const res: Yallist<Cache<C>> = new Yallist();
    this.iterator((node, index, list) => {
      const flag = fn.call(context || list, node.value, index, list);
      if (flag) {
        res.push(node.value);
      }
      return false;
    });

    return res;
  }

  private setHeadNode(node: Node<Cache<C>>) {
    const { cacheList } = this;
    if (cacheList.head === node) return;
    cacheList.removeNode(node);
    cacheList.unshiftNode(node);
  }

  private isHeadNode(node: Node<Cache<C>>) {
    return this.cacheList.head === node;
  }

  getCacheList() {
    return this.cacheList;
  }

  setCacheList(cacheList: Yallist<Cache<C>>) {
    this.cacheList = cacheList;
  }

  toArray() {
    return this.cacheList.toArray();
  }

  // eslint-disable-next-line class-methods-use-this
  createCache(key: string, value: C): Cache<C> {
    return {
      key,
      value,
      timestamp: new Date().getTime(),
    };
  }

  getCacheNode(key: string | Cache, moveToHead: boolean = false) {
    let node: void | Yallist.Node<Cache<C>>;
    const cacheNode = this.findNode(getCacheFilter(key));
    if (cacheNode) {
      if (this.isCacheValid(cacheNode.value, this.isHeadNode(cacheNode))) {
        node = cacheNode;
      } else {
        this.removeCache(cacheNode);
      }
    }
    if (node && moveToHead) {
      this.setHeadNode(node);
    }
    return node;
  }

  findCache(key: string | Cache, moveToHead: boolean = false) {
    let cache: Cache<C> | undefined;
    const cacheNode = this.getCacheNode(key, moveToHead);
    if (cacheNode) {
      cache = cacheNode.value;
    }
    return cache;
  }

  removeCache(key: string | Cache | Node<Cache>) {
    let cacheNode;
    if (isNode(key)) {
      cacheNode = key;
    } else {
      cacheNode = this.findNode(getCacheFilter(key));
    }
    if (cacheNode) {
      this.cacheList.removeNode(cacheNode);
    }
  }

  pushCache(key: string, value: C | Cache<C>) {
    const { cacheList, options } = this;
    const val = isCache(value) ? value.value : value;
    const cacheNode = this.getCacheNode(key, true);
    if (!cacheNode) {
      cacheList.unshift(this.createCache(key, val));
    }
    if (options.capacity && cacheList.length > options.capacity) {
      cacheList.pop();
    }
    this.createCleanTask();
  }

  updateCache(key: string, value: C) {
    const cache = this.findCache(key);
    if (cache) {
      cache.value = value;
    }
  }

  isTimeout(cache: Cache<C>, isHead: boolean, now?: number) {
    const { options } = this;
    const { timeout, latestCacheTimeout } = options;
    if (!options.timeout) return false;
    const timeoutValue = isHead ? latestCacheTimeout : timeout;
    const current = now || new Date().getTime();
    return current - cache.timestamp > timeoutValue;
  }

  isCacheValid(cache: Cache<C>, isHead: boolean, now?: number) {
    const { options } = this;
    const { isCacheValid } = options;
    return !this.isTimeout(cache, isHead, now) && isCacheValid(cache.value);
  }

  clear() {
    this.cacheList = Yallist.create();
  }

  clean() {
    const { options } = this;
    const now = new Date().getTime();
    this.cacheList = this.filter((value, i) => {
      const flag = this.isCacheValid(value, i === 0, now);
      if (!flag && options.logger) {
        options.logger.subjectLog('cache clean', value.key);
      }
      return flag;
    });
  }

  createCleanTask() {
    const { options } = this;
    if (this.cleanIntervalTimeout) {
      clearTimeout(this.cleanIntervalTimeout);
    }
    if (options.autoClean) {
      // @ts-ignore
      this.cleanIntervalTimeout = setTimeout(() => {
        this.clean();
        if (this.cacheList.length) {
          this.createCleanTask();
        }
      }, options.cleanInterval);
    }
  }
}

export default CacheManager;
