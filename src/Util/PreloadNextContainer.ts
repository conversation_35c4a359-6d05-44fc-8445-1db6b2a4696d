import { Business, URL } from '@ctrip/crn';
import { BbkUtils } from '../Common/src/Utils';
import CarLog from './CarLog';
import LogKeyDev from '../Constants/LogKeyDev';

const { isAndroid } = BbkUtils;
// 主动预加载目前IOS关闭：容器跳转有问题，8.60.4修复后再接入
const canPreload = isAndroid;

export type PreloadData = {
  key: string;
  url: string;
};

// 接入文档 http://conf.ctripcorp.com/pages/viewpage.action?pageId=1553520292
export function getPreloadKey<T>(
  url: string,
  params?: T,
  force = false,
): PreloadData {
  let key = '';
  // @ts-ignore
  const CRNPreloadRunCRNApplication = Business?.preloadRunCRNApplication;

  const isOpen = force || canPreload;

  if (url && CRNPreloadRunCRNApplication && isOpen) {
    // @ts-ignore
    key = CRNPreloadRunCRNApplication(url, params);
  }
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_app_preload,
    info: {
      type: 'get key',
      key,
      url,
      isSupport: !!CRNPreloadRunCRNApplication,
    },
  });
  return { key, url };
}

export const openURLWithPreloadKey = (
  url: string,
  preloadInstanceKey: PreloadData,
  notCheckUrlMatch?: boolean,
) => {
  // @ts-ignore
  const CRNopen = URL?.openURLWithPreloadKey;
  const { url: preLoadUrl, key } = preloadInstanceKey || {};
  const isMatched = !notCheckUrlMatch && preLoadUrl === url;
  const isSupport = !!(CRNopen && isMatched && key);
  CarLog.LogTraceDev({
    key: LogKeyDev.c_car_dev_app_preload,
    info: {
      type: 'open',
      key,
      url,
      isMatched,
      isSupport,
      preLoadUrl,
    },
  });
  if (isSupport && canPreload) {
    // @ts-ignore
    CRNopen(url, '', key);
  } else {
    URL.openURL(url);
  }
};
