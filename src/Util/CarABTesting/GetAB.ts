// 函数命名规范：[prefix]+[description]
//      getAbVerOrderCardShowTip => string (A|B|C|D...)
//      isNewBookingCoupon => boolean
//      getAbVer...
//      isNew...
import { Application } from '@ctrip/crn';
import { BbkUtils } from '../../Common/src/Utils';
import Utils from '../Utils';
import { getAbByAppContext } from './Utils';
import AppContext from '../AppContext';
import { ABKey, ABValueType } from './ABKey';
import { getABTestingInfoSync } from './ABTesting';

const { versionCompare } = BbkUtils;

const isNewABVersion = (ABInfo: ABValueType) => {
  const [AB] = getABTestingInfoSync({ expCode: ABInfo.key });
  return ABInfo.newVersionCode.includes(AB.ExpVersion);
};

const isNewABVersionInQunar = (ABInfo: ABValueType) => {
  let ABExpVersion = AppContext?.ABTesting?.datas?.[ABInfo.key];
  if (!ABExpVersion) return false;
  return ABInfo.newVersionCode.includes(ABExpVersion);
};

// 填写页优化项目分流
// 根据ABKey配置，默认B版为新版 isNewBookingOptimization()
// 是否填写页优化B版(白色背景) isNewBookingOptimization('B')
// 是否填写页优化C版(蓝色背景) isNewBookingOptimization('C')
// 是否填写页优化新版(实验版本为B或者C) isNewBookingOptimization(['B','C'])
export const isNewBookingOptimization = (
  versions: string | string[] = ABKey.BookingOptimization.newVersionCode,
  isAutoInvokeNativeAB: boolean = false, // 没有初始化时，是否自动调用NativeAB获取AB值
) => {
  if (!Utils.isCtripIsd()) return false;

  const expectVersion = !Array.isArray(versions) ? [versions] : versions;

  if (!isAutoInvokeNativeAB) {
    const cachedAB = getAbByAppContext(ABKey.BookingOptimization.key);
    return expectVersion.includes(cachedAB?.ExpVersion);
  }

  const [AB] = getABTestingInfoSync({ expCode: ABKey.BookingOptimization.key });
  return expectVersion.includes(AB.ExpVersion);
};

// 是否填写页优化B版(白色背景)
export const bookingOptimizationIsB = () => Utils.isCtripIsd();

// 是否填写页优化C版(蓝色背景)
export const bookingOptimizationIsC = () => false;

// 是否填写页优化新版(实验版本为B或者C)
export const isBookingOptimization = () => Utils.isCtripIsd();

// ！！！注意：多个新版须按照上面注释中的写法区分不同新版，上面的命名是既有代码改造，新增代码不要这样命名！！！

// 新版填写页优惠模块
export const isNewBookingCoupon = () => Utils.isCtripIsd();

// 是否是 tab 拆分新版本
export const isNewTabSplit = () => {
  return isNewABVersionInQunar(ABKey.TabSplit)
}
// 是否是特定版本
export const isEqualABVersion = (ABInfo: ABValueType, abVersions: string[]) => {
  const [AB] = getABTestingInfoSync({ expCode: ABInfo.key });
  return abVersions.includes(AB.ExpVersion);
};

// B版国内列表页不回显车型报价数
export const isNotShowListCountToast = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ListCountToast);

// 供应商详情页海外售卖保险AB实验结果

// ！！！注意：多个新版须按照上面注释中的写法区分不同新版，上面的命名是既有代码改造，新增代码不要这样命名！！！

// 首页用户分层，APP版本 >= 8.59.6
export const isHomeUserLayering = () =>
  versionCompare(Application.version, '8.59.6') > -1;

// 出境证照信息优化实验B版
export const isLicenseApprove = () =>
  Utils.isCtripOsd() && isNewABVersion(ABKey.LicenseApprove);

// 多媒体信息二期
export const isMediaYiChe2 = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.MediaYiChe2);

// 首页用户分层二期，APP版本 >= 8.59.6
export const isHomeUserLayering2BVer = () =>
  versionCompare(Application.version, '8.59.6') > -1 && Utils.isCtripIsd();

// 境外列表页性能提升项目
export const isOSDListQuickPatch = () =>
  Utils.isCtripOsd() && isNewABVersion(ABKey.OSDListQuickPatch);

//  海外提升成交率，新版取消订单页面
export const isOSDNewOrderCancel = isKlbVersion =>
  Utils.isCtripOsd() && isKlbVersion;
// 订单填写页车辆信息优化实验B版
export const isISDBookVehicleInfo = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.BookIsdVehicleInfo);

// 填写页保险点击区域优化
export const isISDbookInsClickArea = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDbookInsClickArea);

// 国内填写页保险气泡更换位置
export const isISDBookingInsuranceTip = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDBookingInsuranceTip);

// 城市区域页新增汇合点
export const isMeetingPoint = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.MeetingPoint);

// 填写页头部优化
export const isISDBookHeadInfoB = () =>
  Utils.isCtripIsd() && isEqualABVersion(ABKey.ISDBookHeadInfo, ['B']);

export const isISDBookHeadInfoC = () =>
  Utils.isCtripIsd() && isEqualABVersion(ABKey.ISDBookHeadInfo, ['C']);

// 车型卡片车图缩小以及门座标签合并
export const isISDVehicleImageLabel = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDVehicleImageLabel);

// 车型卡片车图缩小以及门座标签合并
// @qunar change
export const isISDInterestPoints = () => false;

// 首页用户分流优化
export const isISDHomeFlowOptimize = () =>
  Utils.isCtripIsd() && isNewABVersion(ABKey.ISDHomeFlowOptimize);
