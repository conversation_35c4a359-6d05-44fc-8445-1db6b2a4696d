import {
  ABKey,
  ABValueType,
  GetABValueType,
  GetAllCacheABValueType,
} from './ABKey';
import AppContext from '../AppContext';
import Utils from '../Utils';
import CarLog from '../CarLog';
// import { Utils, CarLog } from '../Index';
import CarStorage from '../CarStorage';
import StorageKey from '../../Constants/StorageKey';
import LogKeyDev from '../../Constants/LogKeyDev';
import { KeyType } from './Types';

const CACHE_AB_EXPIRES = '365d';

/**
 * 异步缓存ab实验结果
 */
export const cacheAB = async (keys: Array<KeyType>) => {
  if (keys?.length > 0) {
    const resultList = {};
    // 获取需要缓存的ab实验结果
    keys.forEach(key => {
      const abValue = GetABValueType(key.expCode);
      // 需要缓存，则添加
      if (abValue.isCache) {
        resultList[key.expCode] = AppContext.ABTesting?.datas?.[key.expCode];
      }
    });

    // 如果有需要缓存的ab实验结果
    if (Object.keys(resultList).length > 0) {
      // 读取缓存ab实验
      const abCacheStr = await CarStorage.loadAsync(
        StorageKey.CAR_CACHE_AB_RESULT,
      );
      let abCache = null;
      try {
        abCache = JSON.parse(abCacheStr);
      } catch (e) {
        abCache = {};
      }
      CarStorage.save(
        StorageKey.CAR_CACHE_AB_RESULT,
        JSON.stringify({ ...abCache, ...resultList }),
        CACHE_AB_EXPIRES,
      );
    }
  }
};

/**
 * 同步读取缓存ab实验结果
 */
export const syncCacheAb = () => {
  const cacheABList = GetAllCacheABValueType();
  if (cacheABList?.length > 0) {
    try {
      const abCacheStr = CarStorage.loadSync(StorageKey.CAR_CACHE_AB_RESULT);
      const abCache = JSON.parse(abCacheStr);
      const syncCache: any = {};
      cacheABList.forEach(abItem => {
        if (abCache[abItem.key]) {
          syncCache[abItem.key] = abCache[abItem.key];
        }
      });
      if (Object.keys(syncCache).length > 0) {
        AppContext.setABTesting(syncCache);
      }
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_trace_sync_ab,
        info: {
          eventResult: !!syncCache,
          abKeys: cacheABList,
          abCache,
          abResult: syncCache,
        },
      });
      // eslint-disable-next-line no-empty
    } catch (e) {
      CarLog.LogTraceDev({
        key: LogKeyDev.c_car_trace_sync_ab,
        info: {
          eventResult: false,
          expMsg: e?.message,
        },
      });
    }
  }
};

// 从AppContext存储的实验版本数据判断是否是新版
export const isNewABVersionFromCache = (ABInfo: ABValueType) => {
  return ABInfo.newVersionCode.includes(
    AppContext.ABTesting?.datas?.[ABInfo.key]?.ExpVersion,
  );
};

export const isNewCVersionFromCache = (ABInfo: ABValueType) => {
  return AppContext.ABTesting?.datas?.[ABInfo.key]?.ExpVersion === 'C';
};

// 从AppContext判断 出境证照信息优化实验B版 是否是新版
export const isLicenseApprove = () =>
  isNewABVersionFromCache(ABKey.LicenseApprove);

export const isLicenseApproveCVer = () =>
  isNewCVersionFromCache(ABKey.LicenseApprove);

export const isMeetingPointFromCache = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.MeetingPoint);

// 从AppContext判断 多媒体二期
export const isMediaYiChe2 = () =>
  Utils.isCtripIsd() && isNewABVersionFromCache(ABKey.MediaYiChe2);
