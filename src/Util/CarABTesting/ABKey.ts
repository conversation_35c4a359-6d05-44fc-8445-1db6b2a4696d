/**
 * 新增与下线实验需更新conf http://conf.ctripcorp.com/pages/viewpage.action?pageId=347791611
 */

export type ABValueType = {
  key: string;
  newVersionCode: string[]; // 定义哪个版本为新版, eg: ['B', 'C']
  defaultVersionCode: string; // 当实验号下线后，不返回ExpVersion时，当前值表示该场景下走哪个版本
  isCache: boolean; // 是否存储到localStorage中
};

type ABKeyType = {
  // 填写页优化项目
  BookingOptimization: ABValueType;
  // 新版填写页优惠模块
  BookingNewCoupon: ABValueType;
  // 订单卡片新增信息推荐实验号
  OrderCardShowTip: ABValueType;
  // 订祥挽留及召回
  OrderDetain: ABValueType;
  // 融合首页 tab 拆分
  TabSplit: ABValueType;
  // 列表页增加取还条件曝光
  ListLocationDateExpose: ABValueType;
  // 列表页不回显车型报价数
  ListCountToast: ABValueType;
  // 出境证照信息优化
  LicenseApprove: ABValueType;
  // 多媒体信息二期
  MediaYiChe2: ABValueType;
  // 海外详情页车图展示标准车图
  showStandardImg: ABValueType;
  // C出境列表页性能提升
  OSDListQuickPatch: ABValueType;
  // 海外提升成交率，新版取消订单页面
  OSDNewOrderCancel: ABValueType;
  // 订单填写页车辆信息优化
  BookIsdVehicleInfo: ABValueType;
  // 填写页保险点击区域优化
  ISDbookInsClickArea: ABValueType;
  ISDBookingInsuranceTip: ABValueType;
  // 城市区域页新增汇合点
  MeetingPoint: ABValueType;
  // 填写页头部优化
  ISDBookHeadInfo: ABValueType;
  // 车型卡片车图缩小以及门座标签合并
  ISDVehicleImageLabel: ABValueType;
  // 利益点显性化
  ISDInterestPoints: ABValueType;
  // 首页信息流优化
  ISDHomeFlowOptimize: ABValueType;
};

export const ABKey: ABKeyType = {
  // 列表页不回显车型报价数
  ListCountToast: {
    key: '230515_DSJT_count',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 海外取车材料优化二期
  LicenseApprove: {
    key: '231218_DSJT_qccl',
    newVersionCode: ['B', 'C'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 海外详情页车图展示标准车图
  showStandardImg: {
    key: '230821_DSJT_cltpC',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // C出境列表页性能提升
  OSDListQuickPatch: {
    key: '231128_DSJT_kuai',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  OSDNewOrderCancel: {
    key: '230907_DSJT_cjcjl',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 订单填写页车辆信息优化
  BookIsdVehicleInfo: {
    key: '230828_DSJT_txyyh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 填写页保险点击区域优化
  ISDbookInsClickArea: {
    key: '231010_DSJT_bxjh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 国内填写页保险气泡更换位置
  ISDBookingInsuranceTip: {
    key: '231114_DSJT_bxwa',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 城市区域页新增汇合点
  MeetingPoint: {
    key: '231024_DSJT_xzhhd',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 多媒体信息二期
  MediaYiChe2: {
    key: '231102_DSJT_dmt2',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 填写页头部优化
  ISDBookHeadInfo: {
    key: '231127_DSJT_txy2',
    newVersionCode: ['B', 'C'],
    defaultVersionCode: 'A',
    isCache: false,
  },

  // 车型卡片车图缩小以及门座标签合并
  ISDVehicleImageLabel: {
    key: '231218_DSJT_lbyh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 利益点显性化
  ISDInterestPoints: {
    key: '240122_DSJT_lyd',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 首页信息流优化
  ISDHomeFlowOptimize: {
    key: '240118_DSJT_xxlyh',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
    isCache: false,
  },
  // 融合首页 tab 拆分
  TabSplit: {
    key: '231208_jt_zc_test_6tab',
    newVersionCode: ['B'],
    defaultVersionCode: 'A',
  },
};

/**
 * 根据实验号获取实验的配置相关数据
 * @param key 实验号
 * @returns 实验的配置相关数据
 */
export const GetABValueType = key => {
  let abValue = null;
  Object.keys(ABKey).forEach(objKey => {
    if (ABKey[objKey].key === key) {
      abValue = ABKey[objKey];
    }
  });
  return abValue;
};

/**
 * 获取需要缓存的所有Ab实验相关配置数据
 * @returns 需要缓存的所有Ab实验相关配置数据
 */
export const GetAllCacheABValueType = () => {
  const allList = [];
  Object.keys(ABKey).forEach(objKey => {
    if (ABKey[objKey].isCache) {
      allList.push(ABKey[objKey]);
    }
  });
  return allList;
};

export default ABKey;
