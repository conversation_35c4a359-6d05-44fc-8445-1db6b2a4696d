import dayjs from '../Common/src/Dayjs/src';
import CarLog from './CarLog';
import Utils from './Utils';
import { LANDING_PAGE } from '../State/Market/Types';
import { PageIdIsd, PageIdOsd } from '../Constants/PageId/Index';
import { LogKeyDev } from '../Constants/Index';

const PageEnum = [];
const LandingToEnum = [];

const initialEnum = () => {
  if (PageEnum.length === 0) {
    Object.keys(PageIdIsd).forEach(pageName => {
      PageEnum.push(PageIdIsd[pageName].EN);
    });
    Object.keys(PageIdOsd).forEach(pageName => {
      PageEnum.push(PageIdOsd[pageName].EN);
    });
  }
  if (LandingToEnum.length === 0) {
    Object.keys(LANDING_PAGE).forEach(pageName => {
      LandingToEnum.push(pageName);
    });
  }
};

initialEnum();

enum CheckFieldType {
  DateTime = 'DateTime',
  Number = 'Number',
  Boolean = 'Boolean',
  String = 'String',
  Enum = 'Enum',
  Value = 'Value',
}

enum CheckErrorType {
  None = 'None',
  NotExist = 'NotExist',
  ObjectParse = 'ObjectParse',
  DateTime = 'DateTime',
  Number = 'Number',
  Boolean = 'Boolean',
  Enum = 'Enum',
  Value = 'Value',
}

export interface FiledCheckRule {
  fieldType?: CheckFieldType;
  fieldRange?: Array<any>;
  fieldValue?: any;
  fieldSupportAb?: boolean;
  fieldValueSupportAb?: boolean;
}

export interface ErrorInfo {
  key: string;
  value: any;
  errorType: CheckErrorType;
  message?: string;
}

export const SupportFields = {
  CRNModuleName: {},
  cmapping_origin_url: {},
  backPageName: {},
  CRNType: {
    fieldType: CheckFieldType.Number,
  },
  apptype: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['ISD_C_APP', 'OSD_C_APP'],
  },
  isHomeCombine: {},
  groupId: {
    fieldType: CheckFieldType.Value,
    fieldValue: 'PlatHomeCar',
  },
  pageChannel: {},
  abVersion: {},
  language: {},
  tabId: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['car', 'jnt', 'train'],
  },
  from: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['car', 'CtqHome'],
  },
  dragBack: {
    fieldType: CheckFieldType.Boolean,
  },
  searchDiff: {
    fieldType: CheckFieldType.Boolean,
  },
  comeFrom: {},
  fromPage: {},
  referenceId: {},
  cityselectorlocation: {},
  fromurl: {},
  t: {
    fieldType: CheckFieldType.Number,
  },
  channelid: {
    fieldType: CheckFieldType.Number,
    fieldSupportAb: true,
  },
  aid: {
    fieldType: CheckFieldType.Number,
    fieldSupportAb: true,
  },
  sid: {
    fieldType: CheckFieldType.Number,
    fieldSupportAb: true,
  },
  ctm_ref: {},
  filters: {},
  initialPage: {
    fieldType: CheckFieldType.Enum,
    fieldRange: PageEnum,
  },
  pageName: {
    fieldType: CheckFieldType.Enum,
    fieldRange: PageEnum,
  },
  isShowSearchConditionTip: {},
  homeSearchFormRefEvent: {},
  queryListCacheId: {},
  rankingStatusCode: {},
  landingto: {
    fieldType: CheckFieldType.Enum,
    fieldRange: LandingToEnum,
    fieldValueSupportAb: true,
  },
  st: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['ser', 'client'],
  },
  pcid: {
    fieldType: CheckFieldType.Number,
  },
  rcid: {
    fieldType: CheckFieldType.Number,
  },
  plid: {
    fieldType: CheckFieldType.Number,
  },
  ordertype: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['hotel', 'flight', 'train'],
  },
  orderId: {
    fieldType: CheckFieldType.Number,
  },
  p_province_id: {
    fieldType: CheckFieldType.Number,
  },
  ptime: {
    fieldType: CheckFieldType.DateTime,
  },
  rtime: {
    fieldType: CheckFieldType.DateTime,
  },
  p_country_id: {
    fieldType: CheckFieldType.Number,
  },
  vehgroupid: {},
  labels: {},
  vehicleid: {
    fieldType: CheckFieldType.Number,
    fieldSupportAb: true,
  },
  storeCode: {
    fieldType: CheckFieldType.Number,
  },
  ctripOrderId: {
    fieldType: CheckFieldType.Number,
  },
  encryptUid: {},
  fromType: {},
  originalCouponCode: {},
  originOrderId: {
    fieldType: CheckFieldType.Number,
  },
  originVendorId: {
    fieldType: CheckFieldType.Number,
  },
  eid: {},
  env: {},
  vendorId: {
    fieldType: CheckFieldType.Number,
  },
  ctripVehicleCode: {
    fieldType: CheckFieldType.Number,
  },
  modifyVendorOrderCode: {
    fieldType: CheckFieldType.Number,
  },
  plname: {},
  homeTabAbVersionInfo: {},
  directOpen: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['1', '2', '7', '11', '13', '14', '15', '21', 'undefined'],
  },
  selectedId: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['pickup', 'dropoff'],
  },
  showBtn: {
    fieldType: CheckFieldType.Boolean,
  },
  abtforsubtabkey: {
    fieldSupportAb: true,
  },
  abtforsubtab: {
    fieldSupportAb: true,
  },
  minUseablePkgId: {},
  passenger: {
    isObject: true,
    isJson: true,
    fullName: {},
    mobile: {
      fieldType: CheckFieldType.Number,
    },
    certificateList: {
      isArray: true,
      certificateType: {
        fieldType: CheckFieldType.Enum,
        fieldRange: ['1', '2', '7', '8'],
      },
      certificateNo: {},
    },
  },
  params: {},
  data: {
    isJson: true,
    isObject: true,
    isShowDropOff: {
      fieldType: CheckFieldType.Boolean,
    },
    rentalDate: {
      isObject: true,
      dropOff: {
        isObject: true,
        dateTime: {
          fieldType: CheckFieldType.DateTime,
        },
      },
      pickUp: {
        isObject: true,
        dateTime: {
          fieldType: CheckFieldType.DateTime,
        },
      },
    },
    rentalLocation: {
      isObject: true,
      isHasInfoFlow: {
        fieldType: CheckFieldType.Boolean,
      },
      historyCityId: {
        fieldType: CheckFieldType.Number,
      },
      dropOff: {
        isObject: true,
        sequenceId: {},
        area: {
          isObject: true,
          id: {},
          ename: {},
          typename: {},
          lat: {
            fieldType: CheckFieldType.Number,
          },
          lng: {
            fieldType: CheckFieldType.Number,
          },
          name: {},
          type: {},
          locationName: {},
          countryShortName: {},
        },
        cid: {
          fieldType: CheckFieldType.Number,
        },
        __crn_container_sequenceid: {},
        realcountry: {},
        version: {},
        eventName: {},
        province: {},
        isDomestic: {
          fieldType: CheckFieldType.Boolean,
        },
        cname: {},
        country: {},
        isFromPosition: {
          fieldType: CheckFieldType.Boolean,
        },
        sortIndex: {
          fieldType: CheckFieldType.Number,
        },
      },
      pickUp: {
        isObject: true,
        sequenceId: {},
        area: {
          isObject: true,
          id: {},
          ename: {},
          typename: {},
          lat: {
            fieldType: CheckFieldType.Number,
          },
          lng: {
            fieldType: CheckFieldType.Number,
          },
          name: {},
          type: {},
          locationName: {},
          countryShortName: {},
        },
        cid: {
          fieldType: CheckFieldType.Number,
        },
        __crn_container_sequenceid: {},
        realcountry: {},
        version: {},
        eventName: {},
        province: {},
        isDomestic: {
          fieldType: CheckFieldType.Boolean,
        },
        cname: {},
        country: {},
        isFromPosition: {
          fieldType: CheckFieldType.Boolean,
        },
        sortIndex: {
          fieldType: CheckFieldType.Number,
        },
      },
      isShowDropOff: {
        fieldType: CheckFieldType.Boolean,
      },
      isNotShowDropOff: {
        fieldType: CheckFieldType.Boolean,
      },
    },
    age: {},
    adultSelectNum: {
      fieldType: CheckFieldType.Number,
    },
    childSelectNum: {
      fieldType: CheckFieldType.Number,
    },
  },
  listParameter: {
    isObject: true,
    rentalDate: {
      isObject: true,
      dropOff: {
        isObject: true,
        dateTime: {
          fieldType: CheckFieldType.DateTime,
        },
      },
      pickUp: {
        isObject: true,
        dateTime: {
          fieldType: CheckFieldType.DateTime,
        },
      },
    },
    rentalLocation: {
      isObject: true,
      dropOff: {
        isObject: true,
        area: {
          isObject: true,
          id: {},
          lat: {
            fieldType: CheckFieldType.Number,
          },
          lng: {
            fieldType: CheckFieldType.Number,
          },
          name: {},
          type: {},
        },
        cid: {
          fieldType: CheckFieldType.Number,
        },
        realcountry: {},
        isDomestic: {
          fieldType: CheckFieldType.Boolean,
        },
        cname: {},
        country: {},
      },
      pickUp: {
        isObject: true,
        area: {
          isObject: true,
          id: {},
          lat: {
            fieldType: CheckFieldType.Number,
          },
          lng: {
            fieldType: CheckFieldType.Number,
          },
          name: {},
          type: {},
        },
        cid: {
          fieldType: CheckFieldType.Number,
        },
        realcountry: {},
        isDomestic: {
          fieldType: CheckFieldType.Boolean,
        },
        cname: {},
        country: {},
      },
      isShowDropOff: {
        fieldType: CheckFieldType.Boolean,
      },
    },
  },
  location: {
    isJson: true,
    isObject: true,
    country: {},
    cname: {},
    cid: {
      fieldType: CheckFieldType.Number,
    },
    area: {
      isObject: true,
      id: {},
      locationName: {},
      countryShortName: {},
      name: {},
      ename: {},
      lat: {
        fieldType: CheckFieldType.Number,
      },
      lng: {
        fieldType: CheckFieldType.Number,
      },
      type: {},
      typename: {},
    },
  },
  showType: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['present'],
  },
  cityId: {
    fieldType: CheckFieldType.Number,
  },
  pickType: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['pickup', 'dropOff'],
  },
  pageType: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['City', 'Area'],
  },
  cityName: {},
  areaId: {},
  areaName: {},
  // 不支持
  cache: {},
  filterPoi: {
    fieldType: CheckFieldType.Enum,
    fieldRange: ['0', '1', '2'],
  },
  klbVersion: {
    fieldType: CheckFieldType.Number,
  },
};

let isValueFieldError = 0;
let isObjectFieldError = 0;
let valueFieldError: Array<ErrorInfo> = [];
let objectFieldError: Array<ErrorInfo> = [];

const initialCheckParams = () => {
  isValueFieldError = 0;
  isObjectFieldError = 0;
  valueFieldError = [];
  objectFieldError = [];
};

const checkValue = (
  item,
  filedStruct: FiledCheckRule,
  isObject = false,
  paramKey = '',
) => {
  let isValidate = true;
  let errorType = CheckErrorType.None;
  let numberVar;
  let dateVar;
  const {
    fieldType,
    fieldRange = [],
    fieldValue,
    fieldValueSupportAb,
  } = filedStruct;
  let value = item;
  if (typeof value === 'string' && fieldValueSupportAb) {
    value = item.toLowerCase();
  }
  switch (fieldType) {
    case CheckFieldType.Number:
      numberVar = Number(value);
      errorType = CheckErrorType.Number;
      isValidate = Math.abs(numberVar) >= 0;
      break;
    case CheckFieldType.DateTime:
      dateVar = dayjs(value);
      errorType = CheckErrorType.DateTime;
      isValidate = dateVar.isValid();
      break;
    case CheckFieldType.Boolean:
      errorType = CheckErrorType.Boolean;
      isValidate =
        value === true ||
        value === false ||
        value === 'true' ||
        value === 'false';
      break;
    case CheckFieldType.Enum:
      errorType = CheckErrorType.Enum;
      isValidate = fieldRange.includes(value);
      break;
    case CheckFieldType.Value:
      errorType = CheckErrorType.Value;
      isValidate = value === fieldValue;
      break;
    default:
      isValidate = true;
      break;
  }
  if (!isValidate) {
    const errorInfo = {
      key: paramKey,
      value,
      errorType,
    };
    if (isObject) {
      isObjectFieldError += 1;
      objectFieldError.push(errorInfo);
    } else {
      isValueFieldError += 1;
      valueFieldError.push(errorInfo);
    }
  }
  return isValidate;
};

const checkParam = (
  urlQuery = {},
  supportFields = SupportFields,
  isObject = false,
  paramKey = '',
) => {
  Object.keys(urlQuery).forEach(item => {
    let supportKey = item || '';
    if (
      !supportFields[supportKey] &&
      supportFields[supportKey.toLowerCase()] &&
      supportFields[supportKey.toLowerCase()].fieldSupportAb
    ) {
      supportKey = supportKey.toLowerCase();
    }
    const paramUrl = `${paramKey ? `${paramKey}.` : ''}${item}`;
    if (!supportFields[supportKey]) {
      if (isObject) {
        isObjectFieldError += 1;
        objectFieldError.push({
          key: paramUrl,
          value: urlQuery[item],
          errorType: CheckErrorType.NotExist,
        });
      } else {
        isValueFieldError += 1;
        valueFieldError.push({
          key: item,
          value: urlQuery[item],
          errorType: CheckErrorType.NotExist,
        });
      }
    } else if (supportFields[supportKey].isObject) {
      let paramObject = urlQuery[item];
      try {
        if (supportFields[supportKey].isJson) {
          const paramStr = decodeURIComponent(urlQuery[item]);
          paramObject = JSON.parse(paramStr);
        }
        checkParam(
          paramObject,
          supportFields[supportKey],
          supportFields[supportKey].isObject,
          paramUrl,
        );
      } catch (e) {
        isObjectFieldError += 1;
        objectFieldError.push({
          key: paramUrl,
          value: urlQuery[item],
          errorType: CheckErrorType.ObjectParse,
          message: e?.message,
        });
      }
    } else if (supportFields[supportKey].isArray) {
      const arrayObj = urlQuery[item];
      arrayObj.map((arrItem, index) => {
        checkParam(
          arrItem,
          supportFields[supportKey],
          supportFields[supportKey].isArray,
          `${paramUrl}.${index}`,
        );
        return null;
      });
    } else if (supportFields[supportKey].fieldType) {
      checkValue(urlQuery[item], supportFields[supportKey], isObject, paramUrl);
    }
  });
};

export const checkUrl = fakeUrl => {
  return new Promise(resolve => {
    resolve(null);
  }).then(() => {
    const urlQuery = Utils.getQueryParams(fakeUrl);
    const {
      initialPage,
      landingto,
      isHomeCombine = false,
      params,
      data,
    } = urlQuery || {};
    initialCheckParams();
    checkParam(urlQuery);

    const info = {
      isValidate: isValueFieldError + isObjectFieldError === 0,
      isMarket: initialPage === 'Market',
      isValueFieldError,
      isObjectFieldError,
      valueFieldError,
      objectFieldError,
      initialPage,
      landingto,
      isHomeCombine,
      fakeUrl,
      hasParams: !!params,
      hasData: !!data,
    };
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_trace_url_check,
      info,
    });
  });
};
