import { AbItemType } from './ABType';

/*
 * @Author: rwang9 <EMAIL>
 * @Date: 2023-03-30 14:46:09
 * @LastEditors: rwang9 <EMAIL>
 * @LastEditTime: 2023-04-03 20:13:04
 * @FilePath: /mini-program/Applications/code/rn_car_qunar/source/rn_car_app/src/Util/ServerABTesting.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// @qunar 循环引用
import ListReqAndResData from '../Global/Cache/ListReqAndResData';

const splitStr = ',';

export const AbTestingKey = {
  // 无少结果推荐
  IsRecommend: {
    key: '220901_DSJT_RECV',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
  // 筛选无结果服务端实验结果
  IsFilteredRecommend: {
    key: '230104_DSJT_fil10',
    defaultValue: 'B',
    isActive: true,
    noVersionVal: false,
    isSync: true,
  },
};

// 列表页接口返回的服务端实验,格式为："实验号|实验值,实验号|实验值"
export const getListServerAbversion = (data: any = null) => {
  const res =
    data || ListReqAndResData.getData(ListReqAndResData.keyList.listProductRes);
  return res?.extras?.abVersion || '';
};

// 将多个服务返回的AB实验结果转化为数组
export const getServerAbversions = (data: any = null) => {
  let serverAbversion = [];
  const listAbversion = getListServerAbversion(data);
  if (listAbversion) {
    serverAbversion = serverAbversion.concat(listAbversion.split(splitStr));
  }
  return serverAbversion;
};

// 组装服务端返回的abversioin 组装各接口的服务端实验结果拼接成字符窜
export const getResAbversion = (data: any = null) => {
  return getServerAbversions(data).join(splitStr);
};

// 获取服务端返回的abversion
export const getAbBoolean = (AbInfo: AbItemType, data = null) => {
  const trueValue = `${AbInfo.key}|${AbInfo.defaultValue}`;
  const serverAbversion = getServerAbversions(data);
  return !!serverAbversion.find(item => item === trueValue);
};

export const isRecommend = (data: any = null) => {
  const res = data || ListReqAndResData.getData(ListReqAndResData.keyList.listProductRes);
  return !!res?.extras?.hasListSign;
};

// 目前已全量切换 B 版，无需读取服务 AB
export const isNewLicensePlate = () => true;

export const isFilteredRecommendAb = (data: any = null) => {
  return getAbBoolean(AbTestingKey.IsFilteredRecommend, data);
};
