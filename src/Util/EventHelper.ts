import { Event } from '@ctrip/crn';
import { isAndroid } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { AppContext, CarLog } from './Index';
import { LogKeyDev } from '../Constants/Index';

export interface IEventOptions {
  /**
   * 是否需要精确匹配对应的渠道apptype
   */
  isSyncAppType?: boolean;
  /**
   * 事件发送类型，具体查看枚举字段EventTriggerType
   */
  type?: EventTriggerType;
  /**
   * 延迟执行方法
   */
  lazyDispatch?: () => void;
}

export enum EventTriggerType {
  /**
   * 默认触发事件
   */
  Default = 'Default',
  /**
   * 发送事件，延迟执行
   */
  LazyDispatch = 'LazyDispatch',
}

/**
 * 延迟执行事件列表，
 * object.key为事件名，object.value为事件数组
 */
const LazyDispatchList = new Map<string, Array<any>>();

/**
 * 添加事件监听
 * @param eventName 事件名称
 * @param callback 回调函数
 * @param options 发送事件的可选参数
 */
export const addEventListener = (
  eventName: string,
  callback?: (data) => void,
  options?: IEventOptions,
) => {
  const { isSyncAppType, type = EventTriggerType.Default } = options || {};
  let callbackHandle = null;
  let execEventList = null;
  switch (type) {
    case EventTriggerType.LazyDispatch:
      execEventList = LazyDispatchList.get(eventName);
      LazyDispatchList.delete(eventName);
      execEventList?.forEach(func => {
        if (typeof func === 'function') {
          func();
        }
      });
      break;
    case EventTriggerType.Default:
    default:
      callbackHandle = data => {
        if (isSyncAppType) {
          const { appType } = data;
          if (appType === AppContext.CarEnv.appType) {
            callback(data);
          }
        } else {
          callback(data);
        }
        CarLog.LogTraceDev({
          key: LogKeyDev.c_car_trace_event_callback,
          info: {
            name: eventName,
            cacheFrom: data.appType,
            sourceFrom: AppContext.CarEnv.appType,
            params: data,
          },
        });
      };
      /**
       * 2023-6-1 融合首页tab拆分版本中，安卓机型，只有第一次注册的事件会生效，导致打开后一个容器时即使注册相同名字的事件，触发的event在后一个容器中都不能接收到
       * 比如：从国内切换到境外，再从境外改成国内时，不能切回到国内, 因为EventName.ApptypeChange只在国内租车容器中监听到了
       * 所以在注册之前先进行一次清除，回到当前页面时会再进行一次注册
       */
      if (isAndroid) {
        Event.removeEventListener(eventName);
      }
      Event.addEventListener(eventName, callbackHandle);
      break;
  }
};

/**
 * 发送事件
 * @param eventName 事件名称
 * @param data 回调参数
 * @param options 发送事件可选参数
 */
export const sendEvent = (
  eventName: string,
  data: any,
  options?: IEventOptions,
) => {
  const { type = EventTriggerType.Default, lazyDispatch } = options || {};
  switch (type) {
    case EventTriggerType.LazyDispatch:
      if (!LazyDispatchList.has(eventName)) {
        LazyDispatchList.set(eventName, []);
      }
      if (typeof lazyDispatch === 'function') {
        const disPatchList = LazyDispatchList.get(eventName);
        // 延迟执行事件列表
        disPatchList.push(lazyDispatch);
      }
      break;
    case EventTriggerType.Default:
    default:
      Event.sendEvent(eventName, {
        ...data,
        appType: AppContext.CarEnv.appType,
      });
      break;
  }
};

/**
 * 删除事件
 * @param eventName 事件名称
 * @param type 事件发送类型，具体查看枚举字段EventTriggerType, 当类型为DidAppear时，此方法为删除缓存数据，以防止全部退出后重进，事件依然监听有效
 */
export const removeEventListener = (
  eventName: string,
  type: EventTriggerType = EventTriggerType.Default,
) => {
  switch (type) {
    case EventTriggerType.LazyDispatch:
      LazyDispatchList.delete(eventName);
      break;
    case EventTriggerType.Default:
    default:
      Event.removeEventListener(eventName);
      break;
  }
};
