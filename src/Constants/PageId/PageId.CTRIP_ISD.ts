export default {
  Middle: { EN: 'Middle', ID: '' },
  License: { EN: 'License', ID: '' },
  Debug: { EN: 'Debug', ID: 'Debug' },
  Home: { EN: 'Home', ID: '10650048749' },
  HomeHeader: { EN: 'HomeHeader', ID: '' },
  List: { EN: 'List', ID: '10650048753' },
  Image: { EN: 'Image', ID: '10650048755' },
  Guide: { EN: 'Guide', ID: '10650048757' },
  Reviews: { EN: 'Reviews', ID: '10650048759' },
  VehicleDetail: { EN: 'VehicleDetail', ID: '10650048761' },
  Order: { EN: 'OrderDetail', ID: '10650048763' },
  Book: { EN: 'Book', ID: '10650048765' },
  Coupon: { EN: 'Coupon', ID: '10650048767' },
  Product: { EN: 'VendorList', ID: '***********' },
  Materials: { EN: 'Materials', ID: '10650048771' },
  FeeDetail: { EN: 'FeeDetail', ID: '10650048773' },
  DriverList: { EN: 'DriverList', ID: '10650048775' },
  DriverEdit: { EN: 'DriverEdit', ID: '10650048777' },
  Policy: { EN: 'Policy', ID: '10650048779' },
  InsuranceSuits: { EN: 'InsuranceSuits', ID: '' }, // 国内的保险详情页为 AddValueServices
  InsuranceDetail: { EN: 'InsuranceDetail', ID: '10650048785' },
  InsuranceCompare: { EN: 'InsuranceCompare', ID: '10650048787' },
  Extras: { EN: 'Extras', ID: '10650048789' },
  PackageIncludes: { EN: 'PackageIncludes', ID: '10650048791' },
  WhyChooseEasylife: { EN: 'WhyChooseEasylife', ID: '10650048793' },
  EasylifePrivilege: { EN: 'EasylifePrivilege', ID: '10650048795' },
  FlightDelayRules: { EN: 'FlightDelayRules', ID: '10650048797' },
  City: { EN: 'City', ID: '10650048799' },
  Area: { EN: 'Area', ID: '10650048801' },
  Subway: { EN: 'Subway', ID: '' },
  LimitRulesPage: { EN: 'LimitRulesPage', ID: '***********' },
  OnlineAuth: { EN: 'OnlineAuth', ID: '10650051688' },
  OptimizeModal: { EN: 'OptimizeModal', ID: '' },
  LimitMap: { EN: 'LimitMap', ID: '' },
  CrossPlace: { EN: 'CrossPlace', ID: '' },
  Credentials: { EN: 'Credentials', ID: '' },
  // 订单详情页费用明细页
  OrderPriceDetail: { EN: 'OrderPriceDetail', ID: '10650078569' },
  // 取消订单二级页面
  OrderCancel: { EN: 'OrderCancel', ID: '10650078545' },
  OrderReviews: { EN: 'OrderReviews', ID: '' },
  OrderRefundDetail: { EN: 'OrderRefundDetail', ID: '' },
  OrderChange: { EN: 'OrderChange', ID: '' },
  SupplementList: { EN: 'SupplementList', ID: '' },
  RenewList: { EN: 'RenewList', ID: '' },
  ViolationDetail: { EN: 'ViolationDetail', ID: '' },
  DamageDetail: { EN: 'DamageDetail', ID: '' },
  Supplement: { EN: 'Supplement', ID: '' },
  InsuranceAgreement: { EN: 'InsuranceAgreement', ID: '10650004918' }, // @qunar-change
  QunarCity: { EN: 'QunarCity', ID: '10650048799' }, // @qunar-change
  QunarSubway: { EN: 'QunarSubway', ID: '10650004916' }, // @qunar-change
  IsdAgreement: { EN: 'IsdAgreement', ID: '10650004917' }, // @qunar-change
  TravelInsurance: { EN: 'TravelInsurance', ID: '' }, // @qunar-change
  AddValueServices: { EN: 'AddValueServices', ID: '10650048783' },
  // 修改订单二级页面
  ModifyOrder: { EN: 'ModifyOrder', ID: '10650078549' },
  // 修改订单确认页
  ModifyOrderConfirm: { EN: 'ModifyOrderConfirm', ID: '***********' },
  // 取消重订搜索页
  RebookHome: { EN: 'RebookHome', ID: '***********' },
  Rerent: { EN: 'Rerent', ID: '***********' },
AdvanceReturn: { EN: 'AdvanceReturn', ID: '***********' },//todo-v7 lxj 替换pageid
  OrderLimitRulesPage: { EN: 'OrderLimitRulesPage', ID: '***********' },
  InsuranceOrderDetail: { EN: 'InsuranceOrderDetail', ID: '***********' },
  OrderVialationRule: { EN: 'OrderVialationRule', ID: '***********' },
  BusinessLicense: { EN: 'BusinessLicense', ID: '***********' },
  OrderAddValueServices: { EN: 'OrderAddValueServices', ID: '' },
  ModifyCoupon: { EN: 'ModifyCoupon', ID: '' },
  // 常见问题页
  Faq: { EN: 'Faq', ID: '***********' },
  // 租车权益页
  Member: { EN: 'Member', ID: '***********' },
  // 权益详情页
  MemberDetail: { EN: 'MemberDetail', ID: '***********' },
  // 生日福利页
  MemberBirth: { EN: 'MemberBirth', ID: '***********' },
  // 积分抵现福利页
  MemberPoints: { EN: 'MemberPoints', ID: '***********' },
  Market: {
    EN: 'Market',
    ID: '***********',
  },
  CarRentalCenter: {
    EN: 'CarRentalCenter',
    ID: '***********'
  },
  VendorList: {
    EN: 'VendorList',
    ID: '***********'
  },
  CitySelector: {
    EN: 'CitySelector',
    ID: '***********'
  },
  // 订单详情页消息助手二级页
  MessageAssistant: { EN: 'MessageAssistant', ID: '***********' },
  // 订单详情页用车指南二级页
  Instructions: { EN: 'Instructions', ID: '***********' },
  VehicleDamageProve: { EN: 'VehicleDamageProve', ID: '' },
  SafetyProtection: { EN: 'SafetyProtection', ID: '' },
  ChineseService: { EN: 'ChineseService', ID: '' },
  CustomerService: { EN: 'CustomerService', ID: '' },
  CSQuestionList: { EN: 'CSQuestionList', ID: '' },
  Search: { EN: 'Search', ID: '' },
  RecommendVehicle: { EN: 'RecommendVehicle', ID: '10650085936' },
  Location: { EN: 'Location', ID: '10650082355' },
  DepositFree: { EN: 'DepositFree', ID: '' },
  Album: { EN: 'Album', ID: '10650132112' },
};
