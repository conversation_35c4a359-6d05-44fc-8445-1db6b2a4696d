/* eslint-disable @typescript-eslint/naming-convention */
export enum INVOKE_FROM {
  HOME = 1,
  LIST = 2,
  PRODUCT = 3,
  ORDER = 4,
  OTHER = '',
  VENDORLIST = 5, // 新报价列表页
}

// http://conf.ctripcorp.com/pages/viewpage.action?pageId=647715157
// http://conf.ctripcorp.com/pages/viewpage.action?pageId=460368041
export enum FEE_CODES {
  /**
   * 租车费 (对客展示)
   */
  CAR_RENTAL_FEE = 'CAR_RENTAL_FEE',
  /**
   * 租车费
   */
  RENTAL_FEE = '1001',
  /**
   * 总价
   */
  TOTAL_FEE = '10001',
  /**
   * 返现
   */
  PAYOFF = '11032',
  /**
   * 优惠券
   */
  COUPON = '11037',
  /**
   * 活动
   */
  PROMOTION = '12038',
  /**
   * 租车押金
   */
  RENTAL_DEPOSIT = '4003',
  /**
   * @deprecated
   * 携程自营人身财物险，在价格一致性之前使用，只做兼容处理
   */
  CTRIP_PERSONAL_PROPERTY_INSURANCE_OLD = '3001',
  /**
   * 携程自营人身财物险
   */
  CTRIP_PERSONAL_PROPERTY_INSURANCE = '2000896',
  /**
   * 附加产品(对客展示)
   */
  CAR_ADDITIONAL_FEE = 'CAR_ADDITIONAL_FEE',
  /**
   * 提前还车退费
   */
  PENALTY_EARLY_RETURN = 'PENALTY_EARLY_RETURN',
  /**
   * 异地还车费
   */
  OnewayFee = 'OnewayFee',
}

export enum FEE_DETAIL_CODES {
  /**
   * 租车费描述
   */
  RENTAL_FEE_DESC = '100020',
}

export const EXTRATAGS_SERVICE_SET = ['18631/queryPriceCalendar'];

export default { INVOKE_FROM, FEE_CODES, EXTRATAGS_SERVICE_SET };
