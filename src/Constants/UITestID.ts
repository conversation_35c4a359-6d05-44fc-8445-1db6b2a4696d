/**
 * UI自动化TestID维护
 * http://conf.ctripcorp.com/pages/viewpage.action?pageId=426318044
 */

const UITestId = {
  car_osd_orderdetail_price_modal: 'car_osd_orderdetail_price_modal',
  car_osd_pick_return_content: 'car_osd_pick_return_content',
  car_osd_pick_return_item: 'car_osd_pick_return_item',
  car_testid_page_home_content_wrap: 'car_testid_page_home_content_wrap',
  car_testid_page_home_pickup_city: 'car_testid_page_home_pickup_city',
  car_testid_page_home_pickup_address: 'car_testid_page_home_pickup_address',
  car_testid_page_home_return_city: 'car_testid_page_home_return_city',
  car_testid_page_home_return_address: 'car_testid_page_home_return_address',
  car_testid_page_home_offsite_switch: 'car_testid_page_home_offsite_switch',
  car_testid_page_home_pickup_time: 'car_testid_page_home_pickup_time',
  car_testid_page_home_return_time: 'car_testid_page_home_return_time',
  car_testid_page_home_site_take: 'car_testid_page_home_site_take',
  car_testid_page_home_pickup_ondoor: 'car_testid_page_home_pickup_ondoor',
  car_testid_page_home_search_btn: 'car_testid_page_home_search_btn',
  // 列表页头部
  car_testid_page_list_header: 'car_testid_page_list_header',
  // 列表页搜索弹层
  car_testid_page_list_search_pannel_modal:
    'car_testid_page_list_search_pannel_modal',
  // 列表页搜索弹层 - 蒙层关闭按钮
  car_testid_page_list_search_pannel_close_mask:
    'car_testid_page_list_search_pannel_close_mask',
  // 列表页搜索弹层时间组件
  car_testid_page_list_search_pannel_timeline:
    'car_testid_page_list_search_pannel_timeline',
  // 列表也搜索弹层地区组件-取车组件
  car_testid_page_list_search_pannel_locationItem_pickup:
    'car_testid_page_list_search_pannel_locationItem_pickup',
  // 列表也搜索弹层地区组件-取车组件
  car_testid_page_list_search_pannel_locationItem_dropoff:
    'car_testid_page_list_search_pannel_locationItem_dropoff',
  // 列表页车型列表组件
  car_testid_page_list_vehicleList: 'car_testid_page_list_vehicleList',
  // 列表页车型列表合并车型组车型——供应商报价组件
  car_testid_page_list_vehicleList_group_vendoritem:
    'car_testid_page_list_vehicleList_group_vendoritem',
  // 列表页优惠券入口
  car_testid_page_list_coupon_entry: 'car_testid_page_list_coupon_entry',
  car_testid_page_list_filter_bar: 'car_testid_page_list_filter_bar',
  // 筛选弹窗底部清除按钮
  car_testid_page_list_filter_modal_clear_btn:
    'car_testid_page_list_filter_modal_clear_btn',
  car_testid_page_list_vehicle_bar: 'car_testid_page_list_vehicle_bar',
  car_testid_page_list_vehicle_sku: 'car_testid_page_list_vehicle_sku',
  car_testid_page_list_vehicle_osd: 'car_testid_page_list_vehicle_osd',
  car_testid_page_list_vendor_list_enter_osd:
    'car_testid_page_list_vendor_list_enter_osd',
  car_testid_page_list_vendor_list_modal_osd:
    'car_testid_page_list_vendor_list_modal_osd',
  car_testid_page_list_vendor_list_modal_boot_osd:
    'car_testid_page_list_vendor_list_modal_boot_osd',
  car_testid_page_list_vendor_list_modal_item_osd:
    'car_testid_page_list_vendor_list_modal_item_osd',
  car_testid_page_product_header_osd: 'car_testid_page_product_header_osd',
  car_testid_page_product_image_header_osd:
    'car_testid_page_product_image_header_osd',
  car_testid_page_product_page_osd: 'car_testid_page_product_page_osd',
  car_testid_page_product_vehicle_osd: 'car_testid_page_product_vehicle_osd',
  car_testid_page_product_vendor_osd: 'car_testid_page_product_vendor_osd',
  car_testid_page_product_vendor_package_osd:
    'car_testid_page_product_vendor_package_osd',
  car_testid_page_product_localtion_detail_osd:
    'car_testid_page_product_localtion_detail_osd',
  car_testid_page_product_vendor_comment_osd:
    'car_testid_page_product_vendor_comment_osd',
  car_testid_comp_vendor_headpic: 'car_testid_comp_vendor_headpic',
  car_testid_page_vendor_detail_btn: 'car_testid_page_vendor_detail_btn',
  car_testid_page_vendor_card: 'car_testid_page_vendor_card',
  car_testid_comp_bookbar_price: 'car_testid_comp_bookbar_price',
  car_testid_page_deposit_item: 'car_testid_page_deposit_item',
  car_testid_page_order_continue_pay: 'car_testid_page_order_continue_pay',
  car_testid_page_order_price_detail: 'car_testid_page_order_price_detail',
  car_testid_page_area_history: 'car_testid_page_area_history',
  car_testid_page_area_recommend: 'car_testid_page_area_recommend',
  car_testid_comp_filter_modal_item: 'car_testid_comp_filter_modal_item',
  // 车型卡片图片
  car_testid_comp_vehicle_car_image: 'car_testid_comp_vehicle_car_image',
  // 车型卡片名称
  car_testid_comp_vehicle_name: 'car_testid_comp_vehicle_name',
  // 车型卡片车型描述
  car_testid_comp_vehicle_desc: 'car_testid_comp_vehicle_desc',
  // 车型卡片标签
  car_testid_comp_vehicle_tag: 'car_testid_comp_vehicle_tag',
  // 价格问号
  car_testid_comp_vehicle_price_help: 'car_testid_comp_vehicle_price_help',
  // 车型日价
  car_testid_comp_vehicle_day_price: 'car_testid_comp_vehicle_day_price',
  // 车型总价
  car_testid_comp_vehicle_total_price: 'car_testid_comp_vehicle_total_price',
  // 价格弹层日价
  car_testid_comp_price_modal_day_price:
    'car_testid_comp_price_modal_day_price',
  // 价格弹层总价
  car_testid_comp_price_modal_total_price:
    'car_testid_comp_price_modal_total_price',
  // 价格弹层
  car_testid_comp_price_modal: 'car_testid_comp_price_modal',
  // 快筛bar
  car_testid_comp_quick_filter_bar: 'car_testid_comp_quick_filter_bar',
  // 价格弹层下一步
  car_testid_comp_price_modal_next: 'car_testid_comp_price_modal_next',
  // 详情页车型名
  car_testid_comp_head_vehicle_name: 'car_testid_comp_head_vehicle_name',
  // 详情页供应商名
  car_testid_comp_vendor_name: 'car_testid_comp_vendor_name',
  // 详情页点评
  car_testid_comp_vendor_comment: 'car_testid_comp_vendor_comment',
  // 供应商取还车信息
  car_testid_comp_vendor_pick_drop_desc:
    'car_testid_comp_vendor_pick_drop_desc',
  // 供应商日价
  car_testid_comp_vendor_day_price: 'car_testid_comp_vendor_day_price',
  // 供应商总价
  car_testid_comp_vendor_total_price: 'car_testid_comp_vendor_total_price',
  // 供应商价格明细问号
  car_testid_comp_vendor_price_help: 'car_testid_comp_vendor_price_help',
  // 填写页底部Bar
  car_testid_comp_book_bar_total_price: 'car_testid_comp_book_bar_total_price',
  // 首页非融合首页BannerA
  car_testid_page_home_banner_first_line_right_B:
    'car_testid_page_home_banner_first_line_right_B',
  // 城市页索引
  car_testid_page_location_anchor_side: 'car_testid_page_location_anchor_side',
  // 首页周租入口
  car_testid_page_home_week_rent: 'car_testid_page_home_week_rent',
  // 首页月租入口
  car_testid_page_home_month_rent: 'car_testid_page_home_month_rent',
  // 会员权益
  car_testid_page_home_membership_rights:
    'car_testid_page_home_membership_rights',
  car_testid_page_home_member_grow_up: 'car_testid_page_home_member_grow_up',
  car_testid_page_home_member_my_point: 'car_testid_page_home_member_my_point',
  car_testid_page_home_member_title: 'car_testid_page_home_member_title',
  car_testid_page_home_member_to_view: 'car_testid_page_home_member_to_view',
  car_testid_page_home_member_desc: 'car_testid_page_home_member_desc',
  car_testid_page_home_member_welfare_title:
    'car_testid_page_home_member_welfare_title',
  car_testid_page_home_member_go_rent: 'car_testid_page_home_member_go_rent',
  car_testid_page_home_member_go_back: 'car_testid_page_home_member_go_back',
  car_testid_page_home_member_page_title:
    'car_testid_page_home_member_page_title',
  car_testid_page_home_member_level: 'car_testid_page_home_member_level',
  car_testid_page_home_member_welfare_item:
    'car_testid_page_home_member_welfare_item',
  car_testid_page_home_member_rights_text_1:
    'car_testid_page_home_member_rights_text_1',
  car_testid_page_home_member_rights_text_2:
    'car_testid_page_home_member_rights_text_2',
  car_testid_page_home_member_rights_title_1:
    'car_testid_page_home_member_rights_title_1',
  car_testid_page_home_member_rights_title_2:
    'car_testid_page_home_member_rights_title_2',
  car_testid_page_home_member_rights_title_3:
    'car_testid_page_home_member_rights_title_3',
  car_testid_page_home_member_rights_icon:
    'car_testid_page_home_member_rights_icon',
  // 区域切换城市按钮
  car_testid_page_location_go_city: 'car_testid_page_location_go_city',
  // 首页行程卡
  car_testid_page_itinerary_card: 'car_testid_page_itinerary_card',
  car_testid_page_itinerary_card_osd: 'car_testid_page_itinerary_card_osd',
  // 首页行程卡
  car_testid_page_itinerary_card_item: 'car_testid_page_itinerary_card_item',
  // 首页订单卡片
  car_testid_page_home_order_card: 'car_testid_page_home_order_card',
  // 首页订单卡片按钮组
  car_testid_page_home_order_card_btn: 'car_testid_page_home_order_card_btn',
  // 首页订单卡片地图
  car_testid_home_order_card_btn_guide: 'car_testid_home_order_card_btn_guide',
  // 首页新手指南入口
  car_testid_page_home_guide_rent: 'car_testid_page_home_guide_rent',
  // 榜单tab
  car_testid_page_home_ranking_tab: 'car_testid_page_home_ranking_tab',
  // 首页券包入口
  car_testid_page_home_coupon_entry: 'car_testid_page_home_coupon_entry',
  // 首页券包入口标题图片
  car_testid_page_home_coupon_entry_title_image:
    'car_testid_page_home_coupon_entry_title',
  // 首页券包item
  car_testid_page_home_coupon_item: 'car_testid_page_home_coupon_item',
  // 首页券包显示更多按钮
  car_testid_page_home_coupon_show_more_btn:
    'car_testid_page_home_coupon_show_more_btn',
  // 首页券包已领取
  car_testid_page_home_coupon_status2: 'car_testid_page_home_coupon_status2',
  // 详情页租车券包-展示券列表
  car_testid_page_vendorList_show_coupon_modal:
    'car_testid_page_vendorList_show_coupon_modal',
  // 详情页租车券包-展示券列表
  car_testid_page_vendorList_coupon_modal:
    'car_testid_page_vendorList_coupon_modal',
  // 详情页租车券包-展示券列表-全部领取按钮
  car_testid_page_vendorList_coupon_modal_footer_button:
    'car_testid_page_vendorList_coupon_modal_footer_button',
  // 详情页-限行信息
  car_testid_page_vendorList_limit_tip: 'car_testid_page_vendorList_limit_tip',
  // 详情页-安心订 放心行模块
  car_testid_page_vendorList_relieved_booking:
    'car_testid_page_vendorList_relieved_booking',
  // 详情页-ScrollView
  car_testid_page_vendorList_ScrollView:
    'car_testid_page_vendorList_ScrollView',
  // 详情页-车型图片
  car_testid_page_vendorList_VehicleImage:
    'car_testid_page_vendorList_VehicleImage',
  // 详情页-页面回退按钮
  car_testid_page_vendorList_PageBack: 'car_testid_page_vendorList_PageBack',
  car_testid_page_vendorList_carInfo: 'car_testid_page_vendorList_carInfo',
  car_testid_page_vendorList_carInfoModal:
    'car_testid_page_vendorList_carInfoModal',
  car_testid_page_vendorList_carInfoModal_image:
    'car_testid_page_vendorList_carInfoModal_image',
  car_testid_page_vendorList_carInfoModal_config:
    'car_testid_page_vendorList_carInfoModal_config',
  car_testid_page_vendorList_carInfo_newEnergyVeh_btn:
    'car_testid_page_vendorList_carInfo_newEnergyVeh_btn',
  // 详情页- 报价列表 - 订按钮
  car_testid_comp_vendor_list_book: 'car_testid_comp_vendor_list_book',
  // 详情页- 总价弹层
  car_testid_comp_vendor_total_price_pop:
    'car_testid_comp_vendor_total_price_pop',
  // 详情页- 供应商弹层Tab
  car_testid_comp_vendor_modal_tab: 'car_testid_comp_vendor_modal_tab',
  // 详情页- 供应商弹层标题logo
  car_testid_comp_vendor_modal_title_logo:
    'car_testid_comp_vendor_modal_title_logo',
  // 详情页- 供应商弹层标题
  car_testid_comp_vendor_modal_title: 'car_testid_comp_vendor_modal_title',
  // 详情页- 供应商弹层地址
  car_testid_comp_vendor_modal_address: 'car_testid_comp_vendor_modal_address',
  // 详情页- 供应商弹层车型标签
  car_testid_comp_vendor_modal_tags: 'car_testid_comp_vendor_modal_tags',
  // 详情页- 供应商弹层门店实拍图
  car_testid_comp_vendor_modal_vehicle_pic:
    'car_testid_comp_vendor_modal_vehicle_pic',
  // 详情页- 供应商弹层评分
  car_testid_comp_vendor_modal_score: 'car_testid_comp_vendor_modal_score',
  // 详情页- 供应商弹层 - BookBar
  car_testid_comp_vendor_modal_book_bar:
    'car_testid_comp_vendor_modal_book_bar',
  // 详情页- 供应商弹层 - 更多政策按钮
  car_testid_comp_vendor_modal_more_policy_btn:
    'car_testid_comp_vendor_modal_more_policy_btn',
  // 详情页- 供应商弹层 - 门店政策页面
  car_testid_comp_vendor_modal_more_policy_wrap:
    'car_testid_comp_vendor_modal_more_policy_wrap',
  car_testid_comp_booking_vehicle_info: 'car_testid_comp_booking_vehicle_info',
  car_testid_comp_booking_vehicle_modal:
    'car_testid_comp_booking_vehicle_modal',
  // 详情页- 供应商弹层 - 标题弹层关闭按钮
  car_testid_comp_vendor_modal_title_close_mask:
    'car_testid_comp_vendor_modal_title_close_mask',
  // 详情页- 供应商弹层 - 取还车地图
  car_testid_comp_vendor_modal_location_map:
    'car_testid_comp_vendor_modal_location_map',
  // 详情页- 供应商弹层 - 无忧租入口
  car_testid_comp_vendor_modal_easy_life_entry:
    'car_testid_comp_vendor_modal_easy_life_entry',
  // 详情页- 供应商弹层 - 查看全部评价
  car_testid_comp_vendor_modal_reviews_all_btn:
    'car_testid_comp_vendor_modal_reviews_all_btn',
  // 详情页- 供应商弹层 - 更多政策
  car_testid_comp_vendor_modal_more_policy:
    'car_testid_comp_vendor_modal_more_policy',
  // 详情页- 保障服务 - 保险
  car_testid_page_insurance_item: 'car_testid_page_insurance_item',
  // 详情页- 保障服务 - 星级分数展示
  car_testid_page_insurance_score_osd: 'car_testid_page_insurance_score_osd',
  // 详情页- 保障服务 - 套餐名称
  car_testid_page_insurance_name_osd: 'car_testid_page_insurance_name_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐构成
  car_testid_page_insurance_compose_osd:
    'car_testid_page_insurance_compose_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐构成图片
  car_testid_page_insurance_support_img_osd:
    'car_testid_page_insurance_support_img_osd',
  // 详情页 - 保险套餐详情 - 弹层套餐
  car_testid_page_insurance_nav_osd: 'car_testid_page_insurance_nav_osd',
  // 详情页 - 保险套餐详情 - 弹层关闭按钮
  car_testid_page_insurance_modal_close_icon:
    'car_testid_page_insurance_modal_close_icon',
  // 详情页 - 保险套餐详情 - 保障详情
  car_testid_page_insurance_modal_detail:
    'car_testid_page_insurance_modal_detail',
  // 详情页-取还车时间回显区-展示逻辑
  car_testid_page_vendorList_date_ptime:
    'car_testid_page_vendorList_date_ptime',
  car_testid_page_vendorList_date_rtime:
    'car_testid_page_vendorList_date_rtime',
  car_testid_page_booking: 'car_testid_page_booking',
  car_testid_page_booking_vehicleLocation:
    'car_testid_page_booking_vehicleLocation',
  // 填写页激励信息模块
  car_testid_comp_booking_advantageDom: 'car_testid_comp_booking_advantageDom',
  // 填写页优惠券押金模块
  car_testid_comp_booking_NewCouponAndDeposit:
    'car_testid_comp_booking_NewCouponAndDeposit',
  // 填写页优惠券押金模块活动
  car_testid_comp_booking_NewCouponAndDeposit_activity:
    'car_testid_comp_booking_NewCouponAndDeposit_activity',
  // 填写页-押金方式及说明-产品支持免押-提示文案
  car_testid_page_booking_deposit_title:
    'car_testid_page_booking_deposit_title',
  car_testid_page_booking_deposit_subtitle:
    'car_testid_page_booking_deposit_subtitle',
  // 填写页-押金方式及说明-产品支付免押-勾选放弃
  car_testid_page_booking_deposit_check_give_up:
    'car_testid_page_booking_deposit_check_give_up',
  // 填写页-个人信息说明弹窗
  car_testid_page_booking_approve_explain_modal:
    'car_testid_page_booking_approve_explain_modal',
  // 详情页- 供应商弹层 - 查看更多
  car_testid_comp_vendor_modal_image_all_btn:
    'car_testid_comp_vendor_modal_image_all_btn',
  // 详情页- 供应商弹层 - 优质门店 - 营业执照
  car_testid_comp_vendor_modal_store_license:
    'car_testid_comp_vendor_modal_store_license',
  // 列表页-租车中心-筛选按钮
  car_testid_page_rent_center_filter_btn:
    'car_testid_page_rent_center_filter_btn',
  // 列表页-租车中心-banner 点击
  car_testid_page_rent_center_banner: 'car_testid_page_rent_center_banner',
  // 填写页- 车行险模块 - 标题
  car_testid_comp_booking_ins_title: 'car_testid_comp_booking_ins_title',
  // 填写页- 车行险模块 - 内容
  car_testid_comp_booking_ins_content: 'car_testid_comp_booking_ins_content',
  // 填写页- 车行险模块 - 内容 - 表头选项
  car_testid_comp_booking_ins_content_headitem:
    'car_testid_comp_booking_ins_content_headitem',
  // 填写页- 车行险模块 - 服务分块（基础、优享、尊享）
  car_testid_comp_booking_ins_content_item:
    'car_testid_comp_booking_ins_content_item',
  // 填写页- 车行险模块 - 选择按钮
  car_testid_comp_booking_ins_content_item_btn:
    'car_testid_comp_booking_ins_content_item_btn',
  // 填写页- 车行险模块 - 查看详情
  car_testid_comp_booking_ins_content_btn:
    'car_testid_comp_booking_ins_content_btn',
  // 填写页- 车行险详情模块 - tab（基础、优享、尊享）
  car_testid_comp_booking_ins_detail_tab:
    'car_testid_comp_booking_ins_detail_tab',
  // 填写页- 车行险模块 - 查看理赔相关政策
  car_testid_comp_booking_ins_tip_btn: 'car_testid_comp_booking_ins_tip_btn',
  // 填写页优惠券押金模块优惠券入口
  car_testid_comp_booking_NewCouponAndDeposit_coupon:
    'car_testid_comp_booking_NewCouponAndDeposit_coupon',
  // 填写页优惠券押金模块优惠券弹窗
  car_testid_comp_booking_couponModal: 'car_testid_comp_booking_couponModal',
  // 填写页优惠券押金模块优惠券弹窗可用优惠券
  car_testid_comp_booking_couponModal_usable_item:
    'car_testid_comp_booking_couponModal_usable_item',
  // 填写页-门店政策跳转-点击【门店政策】
  car_testid_page_booking_store_policy: 'car_testid_page_booking_store_policy',
  // 填写页-预定条款跳转-点击【预定条款】
  car_testid_page_booking_isd_agreement_title:
    'car_testid_page_booking_isd_agreement_title',
  // 填写页-个人信息说明跳转-点击【个人信息说明】
  car_testid_page_booking_approve_explain:
    'car_testid_page_booking_approve_explain',
  // 填写页-程信分免押服务协议跳转-点击【程信分免押服务协议】
  car_testid_page_booking_cheng_xin_fen_agreement:
    'car_testid_page_booking_cheng_xin_fen_agreement',
  // 门店政策页面返回按钮
  car_testid_comp_policy_back_btn: 'car_testid_comp_policy_back_btn',
  // 订详操作栏按钮
  car_testid_page_order_op_btn: 'car_testid_page_order_op_btn',
  // 订详消息入口卡片入口
  car_testid_comp_orderDetail_message_card:
    'car_testid_comp_orderDetail_message_card',
  // 订详咨询进度弹窗
  car_testid_comp_order_ServiceProgressModal:
    'car_testid_comp_order_ServiceProgressModal',
  // 订详消息助手
  car_testid_page_order_status_round_btn:
    'car_testid_page_order_status_round_btn',
  // 订详未加购保险描述
  car_testid_order_detail_insurance_desc:
    'car_testid_order_detail_insurance_desc',
  car_testid_comp_order_priceDetail_point:
    'car_testid_comp_order_priceDetail_point',
  car_testid_comp_order_priceDetail_tip:
    'car_testid_comp_order_priceDetail_tip',
  car_testid_order_detail_orderIdCopyBtn:
    'car_testid_order_detail_orderIdCopyBtn',
  car_testid_comp_orderDetail_refund_entry:
    'car_testid_comp_orderDetail_refund_entry',
  car_testid_comp_orderDetail_isd_refund_modal:
    'car_testid_comp_orderDetail_isd_refund_modal',
  // 订详页
  car_testid_page_order_detail_deposit_block:
    'car_testid_page_order_detail_deposit_block',
  car_testid_page_order_detail_deposit_detail_modal:
    'car_testid_page_order_detail_deposit_detail_modal',
  car_testid_page_order_detail_invoice: 'car_testid_page_order_detail_invoice',
  car_testid_page_order_detail_renew_days:
    'car_testid_page_order_detail_renew_days',
  car_testid_page_order_detail_renew_day_plus:
    'car_testid_page_order_detail_renew_day_plus',
  car_testid_page_order_detail_renew_day_minus:
    'car_testid_page_order_detail_renew_day_minus',
  car_testid_page_order_detail_violation_damage_entry:
    'car_testid_page_order_detail_violation_damage_entry',
  car_testid_page_supplementlist_carviolationrules:
    'car_testid_page_supplementlist_carviolationrules',
  car_testid_page_supplementlist_header_tab:
    'car_testid_page_supplementlist_header_tab',
  car_testid_page_supplementlist_cardamages:
    'car_testid_page_supplementlist_cardamages',
  car_testid_page_order_detail_upgrade_module:
    'car_testid_page_order_detail_upgrade_module',
  car_testid_page_order_detail_upgrade_button:
    'car_testid_page_order_detail_upgrade_button',
  car_testid_page_order_detail_upgrade_single:
    'car_testid_page_order_detail_upgrade_single',
  car_testid_page_order_detail_car_service_modal:
    'car_testid_page_order_detail_car_service_modal',
  car_testid_page_order_detail_upgrade_various:
    'car_testid_page_order_detail_upgrade_various',
  car_testid_page_order_detail_car_service:
    'car_testid_page_order_detail_car_service',
  car_testid_page_order_detail_car_service_detail:
    'car_testid_page_order_detail_car_service_detail',
  car_testid_page_order_detail_car_service_detail_modal:
    'car_testid_page_order_detail_car_service_detail_modal',
  car_testid_page_order_detail_car_service_tips:
    'car_testid_page_order_detail_car_service_tips',
  car_testid_page_order_detail_car_service_claim_more:
    'car_testid_page_order_detail_car_service_claim_more',
  car_testid_page_order_detail_pick_return_map:
    'car_testid_page_order_detail_pick_return_map',
  car_testid_page_order_detail_car_detail_btn:
    'car_testid_page_order_detail_car_detail_btn',
  car_testid_comp_orderDetail_goCredentialEntry:
    'car_testid_comp_orderDetail_goCredentialEntry',
  car_testid_comp_Credentia_CredentiaItem:
    'car_testid_comp_Credentia_CredentiaItem',
  car_testid_list_filterListWithNav: 'car_testid_list_filterListWithNav',
  car_testid_page_address_copy: 'car_testid_page_address_copy',
  car_testid_page_doorAddress_copy: 'car_testid_page_doorAddress_copy',
  car_testid_page_guide_store_call: 'car_testid_page_guide_store_call',
  // 海外人数选中组件
  car_testid_page_home_num_select: 'car_testid_page_home_num_select',
  // 海外年龄选中组件
  car_testid_page_home_age_select: 'car_testid_page_home_age_select',
  // // 海外年龄弹窗组件
  // car_testid_page_home_age_modal: 'car_testid_page_home_age_modal',
  car_testid_page_guide_map_walk_tab: 'car_testid_page_guide_map_walk_tab',
  car_testid_page_guide_map_driver_tab: 'car_testid_page_guide_map_driver_tab',
  car_testid_page_guide_map_location: 'car_testid_page_guide_map_location',
  car_testid_page_order_confirm_modal_button:
    'car_testid_page_order_confirm_modal_button',
  car_testid_advance_return_record_btn: 'car_testid_advance_return_record_btn',
  car_testid_advance_return_record_modal:
    'car_testid_advance_return_record_modal',
  car_testid_list_osd_carVehicleList: 'car_testid_list_osd_carVehicleList',
  car_testid_advance_return_time_modify_item:
    'car_testid_advance_return_time_modify_item',
  car_testid_list_VehicleGroupBar: 'car_testid_list_VehicleGroupBar',
  car_testid_product_mustread_more_policy:
    'car_testid_product_mustread_more_policy',
  car_testid_product_mustread_materials:
    'car_testid_product_mustread_materials',
  car_testid_advance_fee_detail_modal: 'car_testid_advance_fee_detail_modal',
  car_testid_order_confirm_modal: 'car_testid_order_confirm_modal',
  car_testid_advance_return_time_pick_modal:
    'car_testid_advance_return_time_pick_modal',
  car_testid_comp_orderDetail_optionButtons:
    'car_testid_comp_orderDetail_optionButtons',
  car_testid_page_product_PickupMaterials:
    'car_testid_page_product_PickupMaterials',
  car_testid_IdtypeModal: 'car_testid_IdtypeModal',
  car_testid_page_product_extrasInfo: 'car_testid_page_product_extrasInfo',
  car_testid_extrasModal_extrasItem: 'car_testid_extrasModal_extrasItem',
  car_testid_page_list_tiplist: 'car_testid_page_list_tiplist',
  car_testid_renew_card_minus: 'car_testid_renew_card_minus',
  car_testid_renew_card_plus: 'car_testid_renew_card_plus',
  // 海外产品详情页保险模块
  car_testid_page_product_insurancebox: 'car_testid_page_product_insurancebox',
  // 海外产品详情页保险模块套餐选项
  car_testid_page_product_insurancebox_item:
    'car_testid_page_product_insurancebox_item',
  // 海外订单详情页英文说明弹窗
  car_testid_page_order_detail_insurance_reminder_english_modal:
    'car_testid_page_order_detail_insurance_reminder_english_modal',
  // 海外产品详订单详情页保险模块查看电子保单按钮
  car_testid_page_order_detail_insurancebox_item_electronicdetail:
    'car_testid_page_order_detail_insurancebox_item_electronicdetail',
  // 海外产品详情页保险模块详情按钮
  car_testid_page_product_insurancebox_detail:
    'car_testid_page_product_insurancebox_detail',
  // 海外产品详情页保险详情弹窗
  car_testid_page_product_insurancesuits_modal:
    'car_testid_page_product_insurancesuits_modal',
  // 海外产品详情页保险详情弹窗投保须知按钮
  car_testid_page_product_insurancesuits_modal_mustread:
    'car_testid_page_product_insurancesuits_modal_mustread',
  // 海外产品详情页保险详情弹窗保险条款按钮
  car_testid_page_product_insurancesuits_modal_noticerule:
    'car_testid_page_product_insurancesuits_modal_noticerule',
  // 海外产品详情页保险详情弹窗保障详情水平滑动Item按钮
  car_testid_page_product_insurancesuits_modal_detail_horizontal:
    'car_testid_page_product_insurancesuits_modal_detail_horizontal',
  // 海外产品详情页保险详情弹窗分类水平滑动Item按钮
  car_testid_page_product_insurancesuits_modal_category_horizontal:
    'car_testid_page_product_insurancesuits_modal_category_horizontal',
  // 海外产品详情页保险详情弹窗投保须知弹窗
  car_testid_page_product_insurancesuits_modal_mustread_modal:
    'car_testid_page_product_insurancesuits_modal_mustread_modal',
  // 海外产品详情页保险详情弹窗查看保障详情按钮
  car_testid_page_product_insurancesuits_modal_anchordetail:
    'car_testid_page_product_insurancesuits_modal_anchordetail',
  // 海外产品详情页保险详情弹窗什么是起赔额按钮
  car_testid_page_product_insurancesuits_modal_excess_btn:
    'car_testid_page_product_insurancesuits_modal_excess_btn',
  // 海外产品详情页什么是起赔额弹窗
  car_testid_page_product_excessintroduce_modal:
    'car_testid_page_product_excessintroduce_modal',
  // 海外产品详情页套餐比较弹窗
  car_testid_page_product_insurancecompare_modal:
    'car_testid_page_product_insurancecompare_modal',
  // 海外产品详情页套餐售卖弹窗
  car_testid_page_product_insuranceselling_modal:
    'car_testid_page_product_insuranceselling_modal',
  // 海外产品详情页套餐售卖弹窗关闭蒙层
  car_testid_page_product_insuranceselling_close_mask:
    'car_testid_page_product_insuranceselling_close_mask',
  // 海外产品详情页套餐比较弹窗保险套餐选项
  car_testid_page_product_insurancecompare_modal_item:
    'car_testid_page_product_insurancecompare_modal_item',
  car_testid_policy_cancel_noshow: 'car_testid_policy_cancel_noshow',
  car_testid_comp_vendor_modal_more_policy_collapse:
    'car_testid_comp_vendor_modal_more_policy_collapse',
  car_testid_page_list_limitTip: 'car_testid_page_list_limitTip',
  car_testid_page_product_materials_detail:
    'car_testid_page_product_materials_detail',
  car_testid_page_materials: 'car_testid_page_materials',
  car_testid_instructions_tab: 'car_testid_instructions_tab',
  car_testid_page_location_SubAreaButton:
    'car_testid_page_location_SubAreaButton',
  car_testid_book_osd_driver: 'car_testid_book_osd_driver',
  car_testid_comp_bookbar_button: 'car_testid_comp_bookbar_button',
  car_testid_comp_driver_edit_close: 'car_testid_comp_driver_edit_close',
  car_testid_comp_coupon_item: 'car_testid_comp_coupon_item',
  car_testid_page_online_auth_button: 'car_testid_page_online_auth_button',
  // 列表页排序弹窗
  car_testid_page_list_filter_sort_modal:
    'car_testid_page_list_filter_sort_modal',
  car_testid_page_driverlist: 'car_testid_page_driverlist',
  // 驾驶员列表页内容
  car_testid_page_driverlist_content: 'car_testid_page_driverlist_content',
  // 驾驶员列表页驾驶员Item
  car_testid_page_driverlist_item: 'car_testid_page_driverlist_item',
  // 驾驶员编辑页面填写规则弹窗
  car_testid_page_driveredit_rule: 'car_testid_page_driveredit_rule',
  car_testid_comp_booking_new_couponAndActivity:
    'car_testid_comp_booking_new_couponAndActivity',
  // 国内产品详情页头部图片的子分类
  car_testid_page_vendorlist_header_image_bannersub:
    'car_testid_page_vendorlist_header_image_bannersub',
  // 国内相册页面分组
  car_testid_page_album_media_group_item:
    'car_testid_page_album_media_group_item',
  // 国内相册页面底bar
  car_testid_page_album_footer: 'car_testid_page_album_footer',
  // 国内相册页menu
  car_testid_page_album_menu: 'car_testid_page_album_menu',
  // 自助取还
  // 订详页面标题
  car_testid_page_order_detail_page_header:
    'car_testid_page_order_detail_page_header',
  car_testid_page_product_page_timeLine:
    'car_testid_page_product_page_timeLine',
};

export default UITestId;
