import Utils from "../Util/Utils";
import { COMPONENT_CHANNEL } from "./Platform";

export default {
  DOMAIN: 'rn_car_app',
  DEBUG: 'debugEntrance',
  CAR_CROSS_DATA: 'car_cross_data',
  CAR_CROSS_DATA_ISD: 'car_cross_data_isd',
  CAR_CROSS_DATA_AREA: 'car_cross_data_area',
  DRIVER: 'Driver',
  REVIEWS: 'REVIEWS',
  DETAILSHOT: 'DETAILSHOT',
  FeeDeduction: 'fee_deduction', // 扣费查询
  CAR_CROSS_LIST_PARAMETER_TRIP: 'car_cross_list_parameter_trip',
  CAR_AREA_HISTORY_CTRIP: 'car_area_history_ctrip',
  CAR_LOCATION_HISTORY_ISD: 'car_location_history_isd',
  CAR_LOCATION_HISTORY_OSD: 'car_location_history_osd',
  CAR_AREA_HISTORY_TRIP: 'car_area_history_trip',
  CAR_LIST_PARAM_HISTORY: 'car_all_in_one_user_history',
  CAR_LOCATION_DATE_HISTORY: 'car_location_date_history',
  // 境外已查询过的时区数据
  CAR_LOCATION_CITY_TIMEZONE: 'CAR_LOCATION_CITY_TIMEZONE',
  CAR_ALIPAY_WELCOME_HISTORY: 'car_alipay_welcome_history',
  CAR_ISD_VENDORINSURANCE: 'vendorInsurance',
  ONLINE_AUTH_ORDERS: 'online_auth_orders',
  OrderSnapStatus: 'order_snap_status',
  ONLINE_AUTH_DRIVER_NERNER_TIP: 'online_auth_driver_nerver_tip',
  ONLINE_AUTH_IDCARD_NERNER_TIP: 'online_auth_idcard_nerver_tip',
  SUPPLEMENT_LIST_LENGTH: 'supplement_list_length',
  ORDER_GO_IDS: 'order_go_ids',
  // 总价说明pop
  TOTAL_PRICE_POP: 'total_price_pop',
  // @qunar-change, 区域数据
  AREA_RESPONSE_CACHE: 'area_response_cache',
  AREA_HISTORY: 'area_history',
  CITY_HISTORY: 'cityHistory',
  // 是否弹过修改订单的弹窗
  SHOWED_MODIFY_ORDER_POP: 'showed_modify_order_pop',
  // 是否关闭过修改结果提示
  MODIFY_ORDER_TIP_CLOSED: 'modify_order_tip_closed',
  // 续租状态是否已被查看的标记
  CAR_RENWWAL_STATUS_VISIBLE: 'car_renewal_status_visible',
  FREQUENCY_LIMIT: 'frequency_limit',
  // 上传证件C1类型报错次数记录
  CAR_ONLINEAUTH_ERROR_C1_COUNT: 'car_onlineauth_error_c1_count',
  // 首页国内租车送车上门选择记录
  CAR_ISD_PICK_UP_ON_DOOR: 'car_isd_pick_up_on_door',
  CAR_HOME_PRIVILEGE_POP: 'car_home_privilege_pop',
  // 新特权权益
  CAR_HOME_NEWMEMBER_POP: 'car_home_newmember_pop',
  // 是否关闭过续租申请时间提示
  ORDER_RENEW_TIP_CLOSED_STORAGE: 'ORDER_RENEW_TIP_CLOSED_STORAGE',
  HOME_COMBINE_PERFORMANCE: 'HOME_COMBINE_PERFORMANCE',
  // 是否展示过当前订单，当前订单状态下的voc反馈问题
  CAR_ORDERDETAIL_VOC_MODAL: 'car_orderdetail_voc_modal',
  // 记录选中行程卡信息
  SELECTED_ITINERARYCARD_STORAGE: 'car_selected_itinerarycard',
  // 记录事件处理进度中已经催处理成功的事件号
  URGE_SERVICE_IDS: 'car_urge_service_ids',
  // 已经出现转场提示
  HAS_SHOW_AIRPORT_TRANSFER: 'has_show_airport_tansfer',
  get TIME_TO_HOME_COMBIN () {
    return `time_to_home_combin_${Utils.getType()}`
  },
  get LOCATION_TO_HOME_COMBIN () {
    return `time_to_home_combin_${Utils.getType()}`
  },
  get FILTER_TO_HOME_COMBIN () {
    return `filter_to_home_combin_${Utils.getType()}`
  },
  // 订详卡片是否已看过
  CAR_ORDER_DETAIL_CARDS: 'car_order_detail_cards',
  // 新增车损图片已阅状态
  VEHICLE_DAMAGE_NEW_ADD_SCANNED: 'vehicle_damage_new_add_scanned',
  // 已提示过的领券订券id
  CAR_COUPON_BOOK_IDS: 'car_coupon_book_ids',
  // 是否展示过免押弹窗
  CAR_ORDERDETAIL_DEPOSITPAYMENT_MODAL: 'car_orderdetail_depositPaymentModal',
  // 融合首页国内境外tab拆分版本-环境切换时的搜索条件
  CAR_HOME_COMBINE_TAB_LOCATION: 'CAR_HOME_COMBINE_TAB_LOCATION',
  // 取消页面订祥挽留实验的ab版本记录
  CAR_ORDERDETAIL_ORDERDETAIN_AB: 'car_orderdetail_OrderDetainAb',
  // 信息流delegateTag(注意：与接送机需要共用同一个，才能起到tab切换时能刷新的作用)
  INFOFLOW_DELEGATETAG_STORE: 'INFOFLOW_DELEGATETAG_STORE',
  // 信息流delegateTag(需要和接送机保持同一个domain)
  INFOFLOW_DOMAIN: 'igt.stark',
  // 产品详情页openUrl下一个产品详情页需要存储的数据
  CAR_VENDORLISTPAGE_PAGEPARAMSINFO: 'car_vendorListPage_pageParamsInfo',
  CAR_VENDORLISTPAGE_CURRENTSTATE: 'car_vendorListPage_currentState',
  CAR_VENDORLISTPAGE_LOCATIONANDDATE: 'car_vendorListPage_LocationAndDate',
  // fix: 兼容OpenURL打开VendorList时，后续页面触发列表页刷新的场景
  // http://conf.ctripcorp.com/pages/viewpage.action?pageId=1369256764
  // 详细描述：No.10001
  CAR_MULTI_PAGE_REFRESH_ID: 'car_multi_page_refresh_id',
  // 特殊牌照AB
  CAR_ORDERDETAIL_PLATE_AB: 'car_orderdetail_plate_ab',
  // 国内填写页挽留弹窗
  CAR_BOOKING_CONFIRM: 'car_booking_confirm',
  // 是否需要刷新首页价格相关模块 (领券、预加载、信息流)
  CAR_HOME_REFRESH_PRICE: 'car_home_refresh_price',
  // 国内首页缓存的首页预请求数据
  CAR_HOME_PREFETCH_DATA_ISD: 'car_home_prefetch_data_isd',
};
