import React from 'react';
import { StyleSheet, View, Image, TextStyle, ViewStyle } from 'react-native';
import { URL } from '@ctrip/crn';
import { font, color, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import {
  ContentObject,
  StringStyle,
} from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import { Constants } from '../../Common';

const { MemberNumber } = Constants;
const { getPixel, uuid, autoProtocol } = BbkUtils;
const styles = StyleSheet.create({
  img: {
    width: getPixel(592),
    height: getPixel(291),
    marginTop: getPixel(24),
  },
  content: {
    ...font.title3LightStyle,
    color: color.fontSecondary,
  },
  blodContent: {
    ...font.title3BoldStyle,
    color: color.fontPrimary,
  },
});

interface IContentObject {
  data: ContentObject;
  isUrl?: boolean;
  isSingleRow?: boolean;
  textStyle?: TextStyle;
  textBlodStyle?: TextStyle;
  style?: ViewStyle;
  isNumberIcon?: boolean;
  text1TestID?: string;
  text2TestID?: string;
}

const BbkContentObject: React.FC<IContentObject> = ({
  data,
  isUrl = false,
  isSingleRow = false,
  style,
  textStyle,
  textBlodStyle,
  isNumberIcon = false,
  text1TestID,
  text2TestID,
}) => {
  const { stringObjs = [] } = data || {};
  const isTextWrap = isSingleRow && !isNumberIcon;
  const Wrap = isTextWrap ? BbkText : View;
  return (
    <Wrap
      style={[
        style,
        isTextWrap && textStyle,
        isSingleRow && isNumberIcon && layout.flexRow,
      ]}
    >
      {stringObjs.map((item, index) => {
        const { content, style: itemStyle, url } = item;
        const isNumber = isNumberIcon && MemberNumber[content];
        const Container = isTextWrap
          ? BbkText
          : isUrl && url
          ? BbkTouchable
          : View;
        let func = {};
        if ((!isSingleRow || isNumberIcon) && isUrl && url) {
          func = {
            onPress: () => {
              if (url) {
                URL.openURL(url);
              }
            },
          };
        }
        let testID;
        switch (index) {
          case 0:
            testID = text1TestID;
            break;
          case 2:
            testID = text2TestID;
            break;
          default:
            testID = '';
        }
        return !isTextWrap || !!content ? (
          <Container style={isTextWrap && textStyle} {...func} key={uuid()}>
            {!!content && (
              <View testID={testID}>
                <BbkText
                  type={isNumber && 'icon'}
                  style={[
                    styles.content,
                    textStyle,
                    itemStyle === StringStyle.Blod && {
                      ...styles.blodContent,
                      ...textBlodStyle,
                    },
                  ]}
                >
                  {isNumber ? MemberNumber[content] : content}
                </BbkText>
              </View>
            )}
            {url && !isUrl ? (
              <Image
                source={{
                  uri: autoProtocol(url),
                }}
                resizeMode="contain"
                style={styles.img}
              />
            ) : null}
          </Container>
        ) : null;
      })}
    </Wrap>
  );
};

export default BbkContentObject;
