/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable react/prop-types */
/* eslint-disable global-require */
/* eslint-disable react/jsx-fragments */
/* eslint-disable react/jsx-props-no-spreading */
import React, { useEffect, useState, useRef, memo, useMemo } from 'react';
import {
  View,
  StyleSheet,
  TextStyle,
  DeviceEventEmitter,
  Dimensions,
  Image,
} from 'react-native';
import { URL, Toast, Device } from '@ctrip/crn';
import useLazyLoad from '@ctrip/rn_com_car/dist/src/Hooks';
import BbkText from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import Button from '@ctrip/rn_com_car/dist/src/Components/Basic/Button/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import {
  font,
  layout,
  color,
  icon,
  tokenType,
} from '@ctrip/rn_com_car/dist/src/Tokens';
import { Utils, CarLog, GetAB } from '../../../Util/Index';
import {
  ManualLocatingBar,
  ItineraryCard,
} from '../../../Pages/Home/Components/Index';
import Texts from './Texts';
import { LottieSearchButton } from '../../SearchPanel/index';
import { LogKeyDev, PickType, UITestID } from '../../../Constants/Index';
import { ILocationItem, INewSearchPanel, ITimeLine } from './Types';
import AgeModal from '../../AgeModal';

const OtherComponent = {
  get BusinessLicense() {
    return require('../../../Wrap/HomeBusinessLicenseWrap').default;
  },
  get getLocationUrl() {
    return require('../../../Pages/Location/Utils').default;
  },
  get CalendarModal() {
    return require('../../../Components/Calendar/Index').default;
  },
  get BbkLayer() {
    return require('@ctrip/rn_com_car/dist/src/Components/Basic/Layer').default;
  },
  get DateFormatter() {
    return require('@ctrip/rn_com_car/dist/src/Utils').DateFormatter;
  },
  get BbkSwitch() {
    return require('@ctrip/rn_com_car/dist/src/Components/Basic/Switch')
      .default;
  },
};

const {
  getPixel,
  getDayGap,
  ensureFunctionCall,
  htmlDecode,
  useMemoizedFn,
  isIos,
} = BbkUtils;

// @ts-ignore
const { isFoldDevice } = Device;
const { PICKUP, DROPOFF } = PickType;

const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: getPixel(32),
    backgroundColor: color.white,
  },
  titleWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: getPixel(24),
  },
  titleText: {
    color: color.fontSubDark,
    ...font.body3LightStyle,
    lineHeight: getPixel(36),
  },
  mr6: {
    marginRight: getPixel(6),
  },
  locationWrap: {
    flexDirection: 'row',
  },
  contentWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  pv24: {
    paddingVertical: getPixel(24),
  },
  pickUpContent: {
    paddingBottom: getPixel(24),
  },
  pb24: {
    paddingBottom: getPixel(24),
  },
  borderBottom: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: color.blueGrayBg,
  },
  cityWrap: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  cityNameWrap: {
    maxWidth: getPixel(isIos ? 200 : 210),
  },
  areaWrap: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginLeft: getPixel(24),
  },
  locationText: {
    color: color.fontPrimary,
    ...font.subHeadMediumStyle,
  },
  mt3: {
    marginTop: getPixel(3),
  },
  newLineText: {
    ...font.title2MediumStyle,
  },
  locationIcon: {
    color: color.grayDescLine,
    fontSize: getPixel(42),
    marginTop: getPixel(isIos ? 2 : 4),
  },
  leftLocationIcon: {
    marginLeft: getPixel(2),
  },
  rightLocationIcon: {
    marginRight: getPixel(-12),
  },
  labelWrap: {
    width: getPixel(36),
    height: getPixel(36),
    backgroundColor: color.couponSplit,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: getPixel(20),
    marginTop: getPixel(7),
    borderRadius: getPixel(4),
  },
  labelText: {
    ...font.labelLStyle,
  },
  splitLine: {
    position: 'absolute',
    width: getPixel(1),
    height: getPixel(41),
    backgroundColor: color.blueGrayBg,
    top: getPixel(50),
    left: getPixel(18),
  },
  tipWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingBottom: getPixel(16),
  },
  tipIcon: {
    fontSize: getPixel(28),
    color: color.searchTip,
    marginRight: getPixel(7),
    marginTop: -getPixel(4),
  },
  tipText: {
    color: color.searchTip,
    ...font.body3LightStyle,
    lineHeight: getPixel(32),
  },
  subTitleStyle: {
    fontWeight: 'bold',
    ...font.body3Medium2Style,
    marginTop: getPixel(4),
  },
  timeMainWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: getPixel(24),
  },
  width35: { width: '35%' },
  timeWrap: {},
  flexEnd: {
    alignItems: 'flex-end',
    width: '35%',
  },
  timeText: {
    color: color.fontPrimary,
    ...font.subHeadMediumStyle,
  },
  timeTextNew: {
    ...font.subHeadMediumStyle,
    fontFamily: font.fontTripNumberSemiBold,
  },
  timeTextNewRight: {
    ...font.subHeadMediumStyle,
    fontFamily: font.fontTripNumberSemiBold,
    marginRight: getPixel(-6),
  },
  yearText: {
    marginRight: getPixel(12),
    ...font.body3LightStyle,
  },
  yearTextNew: {
    marginRight: getPixel(12),
    ...font.F_26_10_regular_TripNumberRegular,
  },
  weekText: {
    marginRight: getPixel(12),
    ...font.body3LightStyle,
  },
  weekTextNew: {
    marginRight: getPixel(12),
    ...font.F_26_10_regular_TripNumberRegular,
  },
  hourText: {
    ...font.body3LightStyle,
  },
  hourTextNew: {
    ...font.F_26_10_regular_TripNumberRegular,
  },
  gapWrap: {
    width: '30%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gapView: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  gapSplit: {
    width: getPixel(26),
    height: getPixel(2.6),
    backgroundColor: color.searchGapSplit,
    marginRight: getPixel(13),
  },
  gapText: {
    color: color.fontSubDark,
  },
  oneWayIcon: {
    fontSize: getPixel(32),
    color: color.searchGapSplit,
    marginLeft: getPixel(10),
  },
  lottieWrap: {
    left: getPixel(-30),
    top: getPixel(-3),
    marginTop: getPixel(16),
    marginBottom: getPixel(14),
    paddingVertical: 0,
  },
  lottieWrapNew: {
    left: getPixel(-30),
    top: getPixel(-3),
    marginBottom: getPixel(14),
    paddingVertical: 0,
  },
  foldLottie: {
    width: getPixel(1362),
    height: getPixel(118),
  },
  lottie: {
    width: getPixel(702),
    height: getPixel(118),
  },
  buttonBgWrapper: {
    backgroundColor: color.transparentBase,
    marginTop: 0,
    paddingVertical: 0,
  },
  buttonBg: {
    width: getPixel(702),
    height: getPixel(96),
    marginVertical: getPixel(32),
  },
  buttonBgNew: {
    width: getPixel(702),
    height: getPixel(96),
    marginBottom: getPixel(28),
    marginTop: getPixel(8),
  },
  foldButtonBg: {
    width: getPixel(1362),
    height: getPixel(118),
    marginVertical: getPixel(32),
  },
  foldButtonBgNew: {
    width: getPixel(1362),
    height: getPixel(118),
    marginBottom: getPixel(32),
  },
  buttonText: {
    height: getPixel(40),
    ...font.title4MediumStyle,
    lineHeight: getPixel(45),
    paddingLeft: getPixel(2),
  },
  button: {
    marginVertical: getPixel(32),
    height: getPixel(88),
    borderRadius: getPixel(12),
    flexDirection: 'row',
    backgroundColor: color.deepBlueBase,
  },
  buttonNew: {
    marginTop: getPixel(12),
    marginLeft: getPixel(2),
    marginRight: getPixel(4),
    marginBottom: getPixel(32),
    height: getPixel(88),
    borderRadius: getPixel(12),
    flexDirection: 'row',
    backgroundColor: color.deepBlueBase,
  },
  numberWrap: {
    flex: 1,
    marginRight: getPixel(32),
    borderBottomWidth: 0,
    paddingVertical: 0,
    paddingTop: getPixel(24),
  },
  titleIconStyle: {
    position: 'absolute',
    top: getPixel(3),
    right: 0,
    fontSize: getPixel(30),
  },
  ageWrap: {
    flex: 1,
    borderBottomWidth: 0,
    paddingVertical: 0,
    paddingTop: getPixel(24),
  },
  numWrap: {
    flex: 1,
    borderBottomWidth: 0,
    paddingVertical: 0,
    paddingTop: getPixel(24),
  },
  numTitleText: {
    color: color.fontSubDark,
    ...font.body3LightStyle,
  },
  numContentText: {
    color: color.fontPrimary,
    ...font.subHeadMediumStyle,
  },
  numIcon: {
    fontSize: getPixel(30),
    color: color.fontSecondary,
  },
  cardWrap: {
    marginTop: getPixel(20),
  },
  advantageWrap: {
    paddingTop: 0,
  },
  mtf14: {
    marginTop: -getPixel(20),
    marginBottom: getPixel(12),
  },
  ageName: {
    ...font.body3LightStyle,
    marginRight: getPixel(8),
  },
  ageNameNew: {
    ...font.body3LightStyle,
    marginRight: getPixel(8),
  },
  ageValueNew: {
    ...font.F_26_10_regular_TripNumberMedium,
    marginRight: getPixel(8),
    paddingTop: isIos ? 0 : getPixel(6),
  },
  ageValue: {
    ...font.body3Medium2Style,
    marginRight: getPixel(8),
  },
  textTouchBlock: {
    ...layout.betweenHorizontal,
    paddingTop: getPixel(26),
    marginBottom: getPixel(32),
  },
  pickTextWrap: {
    ...layout.flexRow,
    height: getPixel(38, 'ceil'),
  },
  dropTextWrap: {
    marginRight: getPixel(77),
  },
  subsidyImg: {
    width: getPixel(150),
    height: getPixel(50),
    position: 'absolute',
    right: isIos ? getPixel(0) : getPixel(-1.5),
    top: getPixel(-16),
  },
  buttonWrap: {
    position: 'relative',
    backgroundColor: 'yellow'
  },
});

const TimeLine: React.FC<ITimeLine> = memo(
  ({ ptime, rtime, onPressPtime, onPressRtime, onPressRental, isOsd }) => {
    const { pickUpDateStr, dropOffDateStr } =
      OtherComponent.DateFormatter.ctripDateFormat({
        ptime,
        rtime,
        mode: 'noYearShort',
      });

    const { pickUpYearStr, dropOffYearStr } =
      OtherComponent.DateFormatter.ctripYearFormat({
        ptime,
        rtime,
      });

    const { pickUpWeekStr, dropOffWeekStr } =
      OtherComponent.DateFormatter.ctripWeekDayFormat({
        ptime,
        rtime,
      });

    const ptimeStr = OtherComponent.DateFormatter.formatter(ptime).hmString();
    const rtimeStr = OtherComponent.DateFormatter.formatter(rtime).hmString();

    const diff = getDayGap(ptime, rtime);
    const detailDiff = BbkUtils.isd_dhm(ptime, rtime);
    const diffVal = isOsd ? Texts.day(diff) : detailDiff;
    return (
      <View
        testID={UITestID.car_testid_page_list_search_pannel_timeline}
        style={styles.timeMainWrap}
      >
        <BbkTouchable
          style={styles.width35}
          onPress={onPressPtime}
          testID={UITestID.car_testid_page_home_pickup_time}
        >
          <BbkText style={styles.timeTextNew}>{pickUpDateStr}</BbkText>
          <View style={layout.flexRow}>
            {!!pickUpYearStr && (
              <BbkText style={styles.yearTextNew}>{pickUpYearStr}</BbkText>
            )}
            {!!pickUpWeekStr && (
              <BbkText style={styles.weekTextNew}>{pickUpWeekStr}</BbkText>
            )}
            <BbkText style={styles.hourTextNew}>{ptimeStr}</BbkText>
          </View>
        </BbkTouchable>
        <BbkTouchable
          style={styles.gapWrap}
          onPress={onPressRental}
          testID={UITestID.car_testid_page_home_gap_day}
        >
          <View style={styles.gapView}>
            <View style={styles.gapSplit} />
            <BbkText style={styles.gapText}>{diffVal}</BbkText>
            <BbkText type="icon" style={styles.oneWayIcon}>
              {htmlDecode(icon.oneWay)}
            </BbkText>
          </View>
        </BbkTouchable>
        <BbkTouchable
          style={styles.flexEnd}
          onPress={onPressRtime}
          testID={UITestID.car_testid_page_home_return_time}
        >
          <BbkText style={styles.timeTextNewRight}>{dropOffDateStr}</BbkText>
          <View style={layout.flexRow}>
            {!!dropOffYearStr && (
              <BbkText style={styles.yearTextNew}>{dropOffYearStr}</BbkText>
            )}
            {!!dropOffWeekStr && (
              <BbkText style={styles.weekTextNew}>{dropOffWeekStr}</BbkText>
            )}
            <BbkText style={styles.hourTextNew}>{rtimeStr}</BbkText>
          </View>
        </BbkTouchable>
      </View>
    );
  },
);

const LocationItem: React.FC<ILocationItem> = memo(
  ({
    isPickUp,
    showDropoff,
    cityId,
    cityName,
    locationCode,
    locationName,
    style,
    isHideAutonomy,
    testID,
    setPickType,
    onPressCity,
    onPressLocation,
    onLayoutCallback,
    isOsd,
  }) => {
    const jumpToLocationPage = useMemoizedFn(pageType => {
      const data = {
        pageType,
        pickType: isPickUp ? 'pickup' : 'dropOff',
        cityId,
        cityName,
        areaId: locationCode,
        areaName: locationName,
        isHideAutonomy,
        isIsd: !isOsd,
      };
      ensureFunctionCall(setPickType, null, {
        pickType: isPickUp ? PICKUP : DROPOFF,
      });
      // @ts-ignore
      const url = OtherComponent.getLocationUrl(data);
      setTimeout(() => {
        URL.openURL(url);
      }, 150);
    });

    const handlePressCity = useMemoizedFn(() => {
      jumpToLocationPage('City');
      ensureFunctionCall(onPressCity, null, isPickUp);
    });

    const handlePressLocation = useMemoizedFn(() => {
      jumpToLocationPage('Area');
      ensureFunctionCall(onPressLocation, null, isPickUp);
    });

    const cityIsNewLine = getCityIsNewLine(cityName);
    const locationIsNewLine = getLocationIsNewLine({
      cityName,
      locationName,
      showDropoff,
    });
    return (
      <View
        testID={testID}
        style={[styles.locationWrap, style]}
        onLayout={onLayoutCallback}
      >
        {showDropoff && (
          <View style={styles.labelWrap}>
            <BbkText style={styles.labelText}>
              {isPickUp ? Texts.pickUp : Texts.return}
            </BbkText>
          </View>
        )}
        <View
          style={[
            styles.contentWrap,
            isPickUp && styles.pickUpContent,
            isPickUp && showDropoff && styles.borderBottom,
          ]}
        >
          <BbkTouchable
            style={styles.cityWrap}
            onPress={handlePressCity}
            testID={
              isPickUp
                ? UITestID.car_testid_page_home_pickup_city
                : UITestID.car_testid_page_home_return_city
            }
          >
            <View style={styles.cityNameWrap}>
              <BbkText
                style={[
                  styles.locationText,
                  cityIsNewLine && styles.newLineText,
                ]}
              >
                {cityName}
              </BbkText>
            </View>
            <BbkText
              type="icon"
              style={[styles.locationIcon, styles.leftLocationIcon]}
            >
              {icon.arrowFilterDownFilled}
            </BbkText>
          </BbkTouchable>
          <BbkTouchable
            style={styles.areaWrap}
            onPress={handlePressLocation}
            testID={
              isPickUp
                ? UITestID.car_testid_page_home_pickup_address
                : UITestID.car_testid_page_home_return_address
            }
          >
            <View
              style={[
                layout.flex1,
                !cityIsNewLine && locationIsNewLine && styles.mt3,
              ]}
            >
              <BbkText
                style={[
                  styles.locationText,
                  locationIsNewLine && styles.newLineText,
                ]}
              >
                {locationName}
              </BbkText>
            </View>
            <BbkText
              type="icon"
              style={[styles.locationIcon, styles.rightLocationIcon]}
            >
              {icon.arrowFilterDownFilled}
            </BbkText>
          </BbkTouchable>
        </View>
      </View>
    );
  },
);

interface ISearchButton {
  searchBtnLottieJson?: string;
  foldScreenSearchBtnLottieJson?: string;
  searchBtnBg?: string;
  boldScreenSearchBtnBg?: string;
  searchButtonText?: string;
  searchBtnTextIcon?: string;
  searchBtnTextIconStyle?: TextStyle;
  searchPanelButtonType?: string;
  onPressSearch: () => void;
  onLayout?: (event: any) => void;
}
const SearchButton: React.FC<ISearchButton> = memo(
  ({
    searchBtnLottieJson,
    foldScreenSearchBtnLottieJson,
    searchBtnBg,
    boldScreenSearchBtnBg,
    searchButtonText,
    searchBtnTextIcon,
    searchBtnTextIconStyle,
    onPressSearch,
    onLayout,
  }) => {
    const searchBtnText = searchButtonText || Texts.goRent;

    const [isFoldDeviceExpand, setIsFoldDeviceExpand] = useState(
      parseFloat(
        (
          Dimensions.get('screen').width / Dimensions.get('screen').height
        ).toFixed(1),
      ) > 0.8,
    );

    const getUpdateDimensions = useMemoizedFn(() => {
      const { width, height } = Dimensions.get('screen');
      setIsFoldDeviceExpand(parseFloat((width / height).toFixed(1)) > 0.8);
    });

    useEffect(() => {
      DeviceEventEmitter.addListener(
        'didUpdateDimensions',
        getUpdateDimensions,
      );
    });

    const isShowFoldStyle = useMemo(() => {
      return isFoldDevice && isFoldDeviceExpand;
    }, [isFoldDeviceExpand]);

    // 正常手机动态图
    if (!isFoldDevice && searchBtnLottieJson) {
      return (
        <LottieSearchButton
          style={styles.lottie}
          containerStyle={styles.lottieWrapNew}
          data={searchBtnLottieJson}
          text={searchBtnText}
          onPress={onPressSearch}
          onLayout={onLayout}
          testID={UITestID.car_testid_page_home_search_btn}
        />
      );
    }

    // 折叠手机动态图
    if (isFoldDevice && searchBtnLottieJson && foldScreenSearchBtnLottieJson) {
      return (
        <LottieSearchButton
          style={isShowFoldStyle ? styles.foldLottie : styles.lottie}
          containerStyle={styles.lottieWrapNew}
          data={
            isShowFoldStyle
              ? foldScreenSearchBtnLottieJson
              : searchBtnLottieJson
          }
          text={searchBtnText}
          onPress={onPressSearch}
          onLayout={onLayout}
          testID={UITestID.car_testid_page_home_search_btn}
        />
      );
    }

    // 正常手机静态图
    if (!isFoldDevice && searchBtnBg) {
      return (
        <Button
          text={searchBtnText}
          iconCode={searchBtnTextIcon}
          iconStyle={searchBtnTextIconStyle}
          buttonStyle={styles.buttonBgWrapper}
          textStyle={styles.buttonText}
          onPress={onPressSearch}
          onLayout={onLayout}
          imageUrl={searchBtnBg}
          imageProps={{
            style: styles.buttonBgNew,
            resizeMode: 'cover',
          }}
          testID={UITestID.car_testid_page_home_search_btn}
        />
      );
    }

    // 折叠手机静态图
    if (isFoldDevice && searchBtnBg && boldScreenSearchBtnBg) {
      const foldButtonBgStyle = styles.foldButtonBgNew;

      const buttonBgStyle = styles.buttonBgNew;
      return (
        <Button
          text={searchBtnText}
          iconCode={searchBtnTextIcon}
          iconStyle={searchBtnTextIconStyle}
          buttonStyle={styles.buttonBgWrapper}
          textStyle={styles.buttonText}
          onPress={onPressSearch}
          onLayout={onLayout}
          imageUrl={isShowFoldStyle ? boldScreenSearchBtnBg : searchBtnBg}
          imageProps={{
            style: isShowFoldStyle ? foldButtonBgStyle : buttonBgStyle,
            resizeMode: 'cover',
          }}
          testID={UITestID.car_testid_page_home_search_btn}
        />
      );
    }

    return (
      <Button
        buttonType={tokenType.ButtonType.Default}
        text={searchBtnText}
        iconCode={searchBtnTextIcon}
        iconStyle={searchBtnTextIconStyle}
        buttonStyle={styles.buttonNew}
        textStyle={styles.buttonText}
        onPress={onPressSearch}
        onLayout={onLayout}
        testID={UITestID.car_testid_page_home_search_btn}
      />
    );
  },
);

interface ITextTouchBlock {
  ageValue: string;
  onPress: () => void;
  onPressIcon?: () => void;
  testID?: string;
}
const TextTouchBlock: React.FC<ITextTouchBlock> = memo(
  ({ ageValue, onPress, onPressIcon, testID }) => {
    return (
      <BbkTouchable
        testID={testID}
        style={styles.textTouchBlock}
        onPress={onPress}
      >
        <View style={layout.flexRow}>
          <BbkText style={styles.ageName}>驾驶员年龄</BbkText>
          <BbkText style={styles.ageValue}>{`${ageValue} 周岁`}</BbkText>
          <BbkText onPress={onPressIcon} style={styles.numIcon} type="icon">
            {icon.help}
          </BbkText>
        </View>
        <BbkText style={styles.numIcon} type="icon">
          {icon.signBtnArrow}
        </BbkText>
      </BbkTouchable>
    );
  },
);

const getCityIsNewLine = (cityName: string) => {
  return cityName?.length > 5;
};

// 地点展示字号缩小换行的规则配置(与城市名称字数相关，具体规则由UED提供), 其中0代表异地还车按钮未打开，1代表打开
const locationNewLineConfig = {
  2: {
    0: 11,
    1: 10,
  },
  3: {
    0: 10,
    1: 9,
  },
  4: {
    0: 9,
    1: 8,
  },
  5: {
    0: 8,
    1: 7,
  },
};

const getStringLength = (str: string = '') => {
  let len = 0;
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < str?.length; i++) {
    const charCode = str?.charCodeAt(i);
    if (charCode >= 0x4e00 && charCode <= 0x9fff) {
      // 汉字
      len += 1;
    } else {
      // 字母和字符
      len += 0.5;
    }
  }
  return len;
};

const getLocationIsNewLine = (data: {
  cityName: string;
  locationName: string;
  showDropoff: boolean;
}) => {
  const { cityName, locationName, showDropoff } = data;
  const showDropoffNum = showDropoff ? 1 : 0;
  const cityNameLen = cityName?.length > 5 ? 5 : cityName?.length;
  const maxLen = locationNewLineConfig[cityNameLen]?.[showDropoffNum];
  // fix error: undefined is not an object (evaluating 'N[o][n]')
  if (locationNewLineConfig[cityNameLen] === undefined) {
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_cityName_empty,
      info: { showCityName: cityName },
    });
  }
  return getStringLength(locationName) > maxLen;
};

const getSplitHeight = (pickupHeight: number) => {
  return pickupHeight - getPixel(46) + getPixel(20); // 其中46代表的是取车label所占的距离，20代表的还车区域线条占的高度
};

const poiBlackList = [
  '美兰机场T2航站楼-测试汇合点01-勿动',
  '美兰机场T1航站楼-测试汇合点03-勿动',
  '启东站-测试汇合点02-勿动',
  '虹桥国际机场T2航站楼-测试数据修改再修改',
  '虹桥国际机场T2航站楼-测试汇合点',
];

const NewBbkSearchPanel: React.FC<INewSearchPanel> = memo(
  ({
    style,
    ptime,
    rtime,
    pcity,
    rcity,
    showDropoff,
    searchBtnLottieJson,
    foldScreenSearchBtnLottieJson,
    searchButtonText,
    searchBtnTextIcon,
    searchBtnTextIconStyle,
    searchBtnBg,
    age,
    boldScreenSearchBtnBg,
    isShowLocationBar,
    hasLocationPermission,
    isShowItineraryCard,
    selectedItineraryCard,
    travelItems,
    searchPanelButtonType,
    isHideAutonomy,
    timeWarning,
    isOsd,
    insufficientTimeWarning,
    onTimeChange,
    onIsShowDropOffChange,
    onPressSearch,
    onPressCity,
    onPressLocation,
    onAgeChange,
    onAgeCancel,
    onPressPosition,
    setDateInfo,
    setLocationInfo,
    updateSelectedItineraryCard,
    onPressPickUpDate,
    onPressDropOffDate,
    onPressRentalTime,
    onPressAgeSelect,
    onPressAgeTip,
    onAgeTipClose,
    setPickType,
    isShowBusinessLicense,
  }) => {
    const isModalLazyLoad = useLazyLoad(1000);
    const isSwitchLazyLoad = useLazyLoad(100);
    const [showCalendar, setShowCalendar] = useState(false);
    const [ageVisible, setAgeVisible] = useState(false);
    const [ageTipVisible, setAgeTipVisible] = useState(false);
    const [curShowDropoff, setCurShowDropoff] = useState(showDropoff);
    const [pickupHeight, setPickupHeight] = useState(getPixel(69)); // 69是取车地点一行所占的高度
    const [searchButtonLayout, setSearchButtonLayout] = useState(null);
    const CalendarModalRef = useRef(null);

    const isOsdNow = isOsd !== undefined ? isOsd : false;

    useEffect(() => {
      if (showCalendar) {
        if (CalendarModalRef?.current) {
          CalendarModalRef.current.showCalendar();
        } else {
          setShowCalendar(false);
        }
      }
    }, [showCalendar]);

    useEffect(() => {
      setCurShowDropoff(showDropoff);
    }, [showDropoff]);

    const onPressPtime = useMemoizedFn(() => {
      setShowCalendar(true);
      ensureFunctionCall(onPressPickUpDate);
    });

    const onPressRtime = useMemoizedFn(() => {
      setShowCalendar(true);
      ensureFunctionCall(onPressDropOffDate);
    });

    const onPressRental = useMemoizedFn(() => {
      setShowCalendar(true);
      ensureFunctionCall(onPressRentalTime);
    });

    const onConfirmTime = useMemoizedFn(data => {
      onTimeChange(data);
    });

    const handleCheckChange = useMemoizedFn(() => {
      setCurShowDropoff(!curShowDropoff);
      setTimeout(() => {
        onIsShowDropOffChange(!curShowDropoff);
      }, 200);
    });

    const handleAgeChange = useMemoizedFn((...args) => {
      setAgeVisible(false);
      ensureFunctionCall(onAgeChange, null, ...args);
    });

    const haneleCancelAge = useMemoizedFn(() => {
      setAgeVisible(false);
      ensureFunctionCall(onAgeCancel);
    });

    const onPressAge = useMemoizedFn(() => {
      setAgeVisible(true);
      ensureFunctionCall(onPressAgeSelect);
    });

    const onPressAgeIcon = useMemoizedFn(() => {
      setAgeTipVisible(true);
      ensureFunctionCall(onPressAgeTip);
    });

    const handleCloseAgeTip = useMemoizedFn(() => {
      setAgeTipVisible(false);
      ensureFunctionCall(onAgeTipClose);
    });

    const handlePickupLocationLayOut = useMemoizedFn(e => {
      setPickupHeight(e.nativeEvent.layout.height);
    });

    const handleSearchButtonLayout = useMemoizedFn(e => {
      setSearchButtonLayout(e.nativeEvent.layout);
    });

    // 计算 Image 的动态样式
    const dynamicImageStyle = useMemo(() => {
      if (!searchButtonLayout) {
        return styles.subsidyImg;
      }

      // SearchButton 在 buttonWrap 容器内的位置信息
      // 注意：SearchButton 可能有负的 left 偏移（如 lottieWrapNew 的 left: -30px）
      // searchButtonLayout.x 可能是负值
      // searchButtonLayout.width 是 SearchButton 的宽度

      // 我们需要让 Image 的右边缘与 SearchButton 的右边缘对齐
      // SearchButton 的右边缘位置 = searchButtonLayout.x + searchButtonLayout.width

      // 简化逻辑：直接基于 SearchButton 的 x 偏移
      // 如果 SearchButton 有负偏移（如 left: -30px），那么 Image 也应该有相应的偏移
      // 但由于实际情况可能更复杂，我们使用一个简单的映射关系

      // 如果 searchButtonLayout.x 是负值，说明 SearchButton 向左偏移
      // Image 的 right 值应该是这个偏移的相反数（但要考虑实际的对齐需求）
      let finalRight = 0;

      if (searchButtonLayout.x < 0) {
        // SearchButton 向左偏移，Image 也需要相应调整
        // 使用绝对值，但可能需要微调
        finalRight = Math.abs(searchButtonLayout.x);
      } else if (searchButtonLayout.x > 0) {
        // SearchButton 向右偏移，Image 需要向左调整
        finalRight = -searchButtonLayout.x;
      }

alert(finalRight)
      return {
        ...styles.subsidyImg,
        right: 0,
      };
    }, [searchButtonLayout]);

    const handlePressSearch = useMemoizedFn(() => {
      const isPickupInvalid = poiBlackList.includes(pcity.locationName);
      const isDropOffInvalid = poiBlackList.includes(rcity.locationName);
      if (isPickupInvalid && isDropOffInvalid) {
        Toast.show('取还车地点已下线，请重新选择');
        return;
      }
      if (isPickupInvalid) {
        Toast.show('取车地点已下线，请重新选择');
        return;
      }
      if (isDropOffInvalid) {
        Toast.show('还车地点已下线，请重新选择');
        return;
      }
      onPressSearch();
    });

    return (
      <>
        <View style={[styles.wrapper, style]}>
          {/** 定位 */}
          {isShowLocationBar && (
            <ManualLocatingBar
              onPress={onPressPosition}
              isShow={!hasLocationPermission}
            />
          )}
          {/** 标题 */}
          <View style={styles.titleWrap}>
            <BbkText style={styles.titleText}>
              {Texts.pickAndDropLocation}
            </BbkText>
            <BbkTouchable
              style={styles.pickTextWrap}
              onPress={handleCheckChange}
              testID={UITestID.car_testid_page_home_offsite_switch}
            >
              <BbkText
                style={[
                  styles.titleText,
                  styles.mr6,
                  !isSwitchLazyLoad && styles.dropTextWrap,
                ]}
              >
                {Texts.dropoffOtherLocation}
              </BbkText>
              {isSwitchLazyLoad && (
                <OtherComponent.BbkSwitch
                  testID={UITestID.car_testid_page_home_offsite_switch}
                  value={curShowDropoff}
                  onPress={handleCheckChange}
                />
              )}
            </BbkTouchable>
          </View>
          {/** 取还车地点 */}
          <View>
            <LocationItem
              isOsd={isOsdNow}
              isPickUp={true}
              testID={
                UITestID.car_testid_page_list_search_pannel_locationItem_pickup
              }
              {...pcity}
              isHideAutonomy={isHideAutonomy}
              showDropoff={showDropoff}
              onPressCity={onPressCity}
              onPressLocation={onPressLocation}
              setPickType={setPickType}
              onLayoutCallback={handlePickupLocationLayOut}
            />
            {showDropoff && (
              <LocationItem
                isOsd={isOsdNow}
                isPickUp={false}
                testID={
                  UITestID.car_testid_page_list_search_pannel_locationItem_dropoff
                }
                {...rcity}
                isHideAutonomy={isHideAutonomy}
                showDropoff={showDropoff}
                style={styles.pv24}
                onPressCity={onPressCity}
                onPressLocation={onPressLocation}
                setPickType={setPickType}
              />
            )}
            {/** 行程卡 */}
            {isShowItineraryCard && (
              <>
                <ItineraryCard
                  pcity={pcity}
                  rcity={rcity}
                  selectedItineraryCard={selectedItineraryCard}
                  travelItems={travelItems}
                  setDateInfo={setDateInfo}
                  setLocationInfo={setLocationInfo}
                  updateSelectedItineraryCard={updateSelectedItineraryCard}
                />
              </>
            )}
            {!showDropoff && <View style={styles.borderBottom} />}
            {showDropoff && pcity?.locationName !== rcity?.locationName && (
              <View style={[styles.tipWrap, styles.borderBottom]}>
                <BbkText type="icon" style={styles.tipIcon}>
                  {icon.circleWithI}
                </BbkText>
                <BbkText style={styles.tipText}>
                  {Texts.diffLocationFeeWarning}
                </BbkText>
              </View>
            )}
            {showDropoff && (
              <View
                style={[
                  styles.splitLine,
                  { height: getSplitHeight(pickupHeight) },
                ]}
              />
            )}
          </View>
          {/** 取还车时间 */}
          <View style={styles.borderBottom}>
            <TimeLine
              ptime={ptime}
              rtime={rtime}
              onPressPtime={onPressPtime}
              onPressRtime={onPressRtime}
              onPressRental={onPressRental}
              isOsd={isOsdNow}
            />
            <View>
              {!!insufficientTimeWarning && (
                <View style={styles.tipWrap}>
                  <BbkText type="icon" style={styles.tipIcon}>
                    {icon.circleWithI}
                  </BbkText>
                  <BbkText style={styles.tipText}>
                    {insufficientTimeWarning}
                  </BbkText>
                </View>
              )}
              {!!timeWarning && (
                <View style={styles.tipWrap}>
                  <BbkText type="icon" style={styles.tipIcon}>
                    {icon.circleWithI}
                  </BbkText>
                  <BbkText style={styles.tipText}>{timeWarning}</BbkText>
                </View>
              )}
            </View>
          </View>
          {/** 驾龄和人数 */}
          {isOsdNow && (
            <TextTouchBlock
              ageValue={age}
              onPress={onPressAge}
              onPressIcon={onPressAgeIcon}
              testID={UITestID.car_testid_page_home_age_select}
            />
          )}
          <View style={styles.buttonWrap}>
            <SearchButton
              searchBtnLottieJson={searchBtnLottieJson}
              foldScreenSearchBtnLottieJson={foldScreenSearchBtnLottieJson}
              searchButtonText={searchButtonText}
              searchBtnBg={searchBtnBg}
              boldScreenSearchBtnBg={boldScreenSearchBtnBg}
              searchBtnTextIcon={searchBtnTextIcon}
              searchBtnTextIconStyle={searchBtnTextIconStyle}
              searchPanelButtonType={searchPanelButtonType}
              onPressSearch={handlePressSearch}
              onLayout={handleSearchButtonLayout}
            />
            {!GetAB.isSuperSubsidy() && (
              <Image
                source={{
                  uri: 'https://dimg04.c-ctrip.com/images/1tg5s12000lqitl4m3516.png',
                }}
                style={dynamicImageStyle}
              />
            )}
          </View>
          {!!isShowBusinessLicense && (
            <OtherComponent.BusinessLicense style={styles.mtf14} />
          )}
        </View>
        {showCalendar && (
          <OtherComponent.CalendarModal
            ref={CalendarModalRef}
            ptime={ptime}
            rtime={rtime}
            maxMonths={Utils.getRentalMaxMonth()}
            onConfirm={onConfirmTime}
            setShowCalendar={setShowCalendar}
          />
        )}
        {isModalLazyLoad && (
          <>
            <AgeModal
              age={age}
              visible={ageVisible}
              confirmCallback={handleAgeChange}
              cancelCallback={haneleCancelAge}
            />
            <OtherComponent.BbkLayer
              modalVisible={ageTipVisible}
              onRequestClose={handleCloseAgeTip}
              title="为什么要填写驾驶员年龄"
            >
              <BbkText style={{ ...font.body2LightStyle }}>
                30岁以下或60岁以上的驾驶员可能需支付额外费用，以及可租车辆会不同，具体以车行政策为准。
              </BbkText>
            </OtherComponent.BbkLayer>
          </>
        )}
      </>
    );
  },
);

export default NewBbkSearchPanel;
