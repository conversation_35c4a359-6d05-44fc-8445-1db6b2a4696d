import React from 'react';
import { StyleSheet, Image, View } from 'react-native';
import { LinearGradient } from '@ctrip/crn';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, layout } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import NoteCards from './NoteCards';
import VehicleCards from './VehicleCards';
import {
  IUserAttrInfo,
  TripRecommendItem,
} from '../../../State/Home/FunctionTypes';
import SkeletonLoading, { PageType } from '../../SkeletonLoading';
import CarLog from '../../../Util/CarLog';
import { ExposureKey } from '../../../Constants/Index';

const { getPixel, vw, isAndroid, isHarmony } = BbkUtils;

const styles = StyleSheet.create({
  wrap: {
    width: vw(100),
    paddingBottom: getPixel(40),
  },
  linearWrap: {
    width: vw(100),
    borderTopLeftRadius: getPixel(12),
    borderTopRightRadius: getPixel(12),
    height: getPixel(1200),
    position: 'absolute',
    zIndex: 0,
  },
  linearWrapFirst: {
    width: vw(100),
    height: getPixel(1200),
    position: 'absolute',
    zIndex: 0,
  },
  content: {
    position: 'relative',
    zIndex: 10,
  },
  title: {
    ...font.homeFlowTitle,
    color: color.white,
    marginBottom: getPixel(7),
  },
  subTitleSlash: {
    ...font.body3LightStyle,
    color: color.white,
    top: getPixel(30),
    opacity: 0.4,
    marginLeft: getPixel(2),
  },
  subTitle: {
    ...font.body3LightStyle,
    color: color.white,
    top: getPixel(30),
    width: getPixel(570),
    opacity: 0.8,
  },
  headImgBg: {
    width: getPixel(604),
    height: getPixel(300),
    position: 'absolute',
    overflow: 'hidden',
    right: 0,
    top: getPixel(-25),
  },
  titleWrap: {
    paddingHorizontal: getPixel(36),
    paddingTop: isAndroid ? getPixel(26) : getPixel(28),
    marginBottom: isAndroid ? getPixel(20) : getPixel(18),
    flexDirection: 'row',
    position: 'relative',
    zIndex: 10,
    alignItems: 'flex-start',
  },
  loadingBg: {
    width: getPixel(704),
    height: getPixel(866),
    marginLeft: getPixel(22),
  },
});

interface LocationRecommendProps {
  data: TripRecommendItem;
  onLayout: (e: any) => void;
  index: number;
  moduleRowTotal: number;
  userAttrInfo: IUserAttrInfo;
}

const LocationRecommend = ({
  data,
  onLayout,
  index,
  moduleRowTotal,
  userAttrInfo,
}: LocationRecommendProps) => {
  const {
    title,
    recCityName,
    desc,
    streamArticles,
    topProductGroups,
    headImage,
    color: bgColors,
    areaName,
  } = data || {};
  const linearGradientBgColor = bgColors.replace(/\s+/g, '')?.split(',');
  const exposureInfo =
    index === 0
      ? {
          enName: ExposureKey.C_HOME_TRIP_RECOMMEND_LOCATION_NEAR.KEY,
          info: {
            nonFixedName: areaName,
            nonFixedCity: recCityName,
            ...userAttrInfo,
          },
        }
      : {
          enName: ExposureKey.C_HOME_TRIP_RECOMMEND_LOCATION_FIXED.KEY,
          info: {
            fixedName: areaName,
            fixedCity: recCityName,
            ...userAttrInfo,
          },
        };

  const logCommonData = {
    moduleName: title,
    moduleType: areaName,
    moduleRow: index,
    moduleRowTotal,
  };

  return (
    <View style={styles.wrap} onLayout={onLayout} key={areaName}>
      <LinearGradient
        start={{ x: 0.5, y: 0 }}
        end={{ x: 0.5, y: 0.6 }}
        locations={[0, 1]}
        colors={linearGradientBgColor || [color.transparent, color.transparent]}
        style={index === 0 ? styles.linearWrapFirst : styles.linearWrap}
      />
      <Image source={{ uri: headImage }} style={styles.headImgBg} />
      <View style={styles.content}>
        <View
          style={styles.titleWrap}
          testID={CarLog.LogExposure(exposureInfo)}
        >
          <Text style={styles.title}>{title}</Text>
          <View style={layout.alignHorizontal}>
            <Text style={styles.subTitleSlash}>/</Text>
            <Text numberOfLines={1} style={styles.subTitle}>
              {desc}
            </Text>
          </View>
        </View>

        {streamArticles ? (
          <>
            {/* 笔记卡片 */}
            <NoteCards
              log={logCommonData}
              items={streamArticles}
              userAttrInfo={userAttrInfo}
              recCity={recCityName}
            />
            {/* 推荐车型卡片 */}
            <VehicleCards items={topProductGroups} recCity={recCityName} />
          </>
        ) : (
          <SkeletonLoading
            visible={true}
            style={styles.loadingBg}
            isShowBreathAnimation={!isHarmony}
            pageName={PageType.HomeFlowRecommend}
          />
        )}
      </View>
    </View>
  );
};

export default LocationRecommend;
