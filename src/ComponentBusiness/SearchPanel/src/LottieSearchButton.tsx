/**
 * 首页查询按钮Lottie动画版本
 * 需求：http://idev.ctripcorp.com/share/prod-requirement/2422/1213586?kanban=28169
 */
import _ from 'lodash';
import React, { useEffect, useRef, memo } from 'react';
import { ViewStyle, StyleSheet } from 'react-native';
import Touchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';

import { LottieAnimation } from '@ctrip/crn';

const styles = StyleSheet.create({
  text: {
    position: 'absolute',
    color: color.white,
    left: 0,
    right: 0,
    top: 10,
    textAlign: 'center',
    zIndex: -1,
  },
});

interface ILottieSearchButton {
  style: ViewStyle;
  containerStyle: ViewStyle;
  text?: string;
  onPress: () => void;
  data: Object;
  testID?: string;
  onLayout?: (event: any) => void;
}

const isEqual = (prevProps, nextProps) => _.isEqual(prevProps, nextProps);

const LottieSearchButton: React.FC<ILottieSearchButton> = memo(
  ({ style, containerStyle, text, onPress, data, testID, onLayout }) => {
    const Lottie = useRef(null);

    useEffect(() => {
      if (data) {
        Lottie?.current?.play(-1, -1);
      }
    }, [data]);

    return (
      <Touchable
        debounce={true}
        onPress={onPress}
        style={containerStyle}
        testID={testID}
        onLayout={onLayout}
      >
        {!!data && (
          <LottieAnimation
            ref={Lottie}
            source={data}
            loop={true}
            style={style}
          />
        )}
        <Text style={styles.text}>{text}</Text>
      </Touchable>
    );
  },
  isEqual,
);

export default LottieSearchButton;
