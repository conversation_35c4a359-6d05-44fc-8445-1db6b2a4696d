import { ReactNode } from 'react';
import { ViewStyle } from 'react-native';

export interface HalfPageModalProps {
  style?: ViewStyle;
  contentStyle?: ViewStyle | ViewStyle[];
  pageModalProps: any;
  modalHeaderProps?: any;
  children?: ReactNode | ReactNode[];
  headerDom?: ReactNode | ReactNode[];
  bgDom?: ReactNode | ReactNode[];
  testID?: string;
  hasBottomBorder?: boolean;
  footerShadow?: boolean;
}
