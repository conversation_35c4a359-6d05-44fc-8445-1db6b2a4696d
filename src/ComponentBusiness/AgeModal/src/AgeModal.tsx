import React, { useState, useMemo, memo, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { layout, font, color } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import BbkTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { useWindowSizeChanged } from '@ctrip/rn_com_car/dist/src/Hooks';
import { useMemoizedFn } from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import BbkHalfPageModal from '../../HalfPageModal';
import Texts from './Texts';
import { ageData } from './AgeData';
import { UITestID } from '../../../Constants/Index';

const { getPixel, getPixelWithIphoneXBottom } = BbkUtils;

const styles = StyleSheet.create({
  content: {
    paddingTop: getPixel(16),
    paddingHorizontal: getPixel(32),
    paddingBottom: getPixelWithIphoneXBottom(140, 100),
    ...layout.flexRowWrap,
  },
  itemWrap: {
    backgroundColor: color.tableGrayBg,
    height: getPixel(64),
    marginBottom: getPixel(16),
    marginRight: getPixel(15),
    borderRadius: getPixel(8),
    ...layout.alignHorizontal,
  },
  itemText: {
    ...font.title3LightStyle,
    color: color.blueGrayBase,
  },
  itemTextSelected: {
    color: color.ageModifyBlue,
    ...font.title3MediumStyle,
  },
  itemWrapSelected: {
    backgroundColor: color.blueBgSecondary,
  },
  modal: {
    maxHeight: '40%',
  },
  modalContent: {
    paddingHorizontal: 0,
    paddingTop: 0,
  },
  mr0: {
    marginRight: 0,
  },
  titleStyle: {
    ...font.title4MediumStyle,
    color: color.recommendProposeBg,
    textAlign: 'center',
    marginTop: getPixel(4),
    backgroundColor: color.transparent,
    marginLeft: -getPixel(32),
  },
  headWrap: {
    minHeight: getPixel(88),
  },
});

interface DriverSelectModalProps {
  visible: boolean;
  confirmCallback: (age) => void;
  cancelCallback: () => void;
  age: string;
}
const AgeModal = memo(
  ({
    visible,
    confirmCallback,
    cancelCallback,
    age,
  }: DriverSelectModalProps) => {
    const [curAge, setCurAge] = useState(age);
    useEffect(() => {
      setCurAge(age);
    }, [age]);
    const pageModalProps = useMemo(
      () => ({
        visible,
        onMaskPress: cancelCallback,
        useCRNModal: true,
      }),
      [visible, cancelCallback],
    );

    const modalHeaderProps = useMemo(
      () => ({
        title: Texts.age,
        style: styles.headWrap,
        titleStyle: styles.titleStyle,
      }),
      [],
    );
    const { width } = useWindowSizeChanged();
    const itemWidthObj = useMemo(
      () => ({ width: (width - getPixel(110)) / 4 }),
      [width],
    );
    const onPress = useMemoizedFn((item: string) => {
      setCurAge(item);
      confirmCallback(item);
    });
    return (
      <BbkHalfPageModal
        pageModalProps={pageModalProps}
        modalHeaderProps={modalHeaderProps}
        style={styles.modal}
        contentStyle={styles.modalContent}
      >
        <ScrollView showsHorizontalScrollIndicator={false}>
          <View style={styles.content}>
            {ageData.list.map((item, index) => (
              <BbkTouchable
                key={item}
                style={[
                  styles.itemWrap,
                  itemWidthObj,
                  curAge === item && styles.itemWrapSelected,
                  (index + 1) % 4 === 0 && styles.mr0,
                ]}
                onPress={() => onPress(item)}
                testID={`${UITestID.car_testid_page_home_age_modal_item}${item}`}
              >
                <Text
                  style={[
                    styles.itemText,
                    curAge === item && styles.itemTextSelected,
                  ]}
                >
                  {item}
                </Text>
              </BbkTouchable>
            ))}
          </View>
        </ScrollView>
      </BbkHalfPageModal>
    );
  },
);

export default AgeModal;
