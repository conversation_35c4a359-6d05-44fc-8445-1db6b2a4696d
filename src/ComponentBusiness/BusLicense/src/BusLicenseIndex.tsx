/* eslint-disable react/require-default-props */
/* eslint-disable react/no-unused-prop-types */
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, StyleSheet, ScrollView, ImageBackground } from 'react-native';
import { Loading } from '@ctrip/crn';
import PictureBox from '@ctrip/rn_com_car/dist/src/Components/Basic/PhotoBrowser';
import BbkComponentModal from '@ctrip/rn_com_car/dist/src/Components/Basic/Modal';
import BbkComponentTouchable from '@ctrip/rn_com_car/dist/src/Components/Basic/Touchable/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { color, font, layout, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import Text from '@ctrip/rn_com_car/dist/src/Components/Basic/Text';
import { Utils } from '../../../Util/Index';
import { ImageUrl } from '../../../Constants/Index';

const { autoProtocol, vw, vh, getPixel } = BbkUtils;

const noop = () => {};
const styles = StyleSheet.create({
  wrapper: {
    width: vw(100),
    backgroundColor: color.white,
    minHeight: vh(30) + getPixel(70),
    maxHeight: vh(80),
    borderTopRightRadius: getPixel(24),
    borderTopLeftRadius: getPixel(24),
    alignItems: 'center',
  },
  companyName: {
    ...font.body3LightFlatStyle,
    color: color.grayBase,
    textAlign: 'center',
    marginTop: getPixel(36),
  },
  scrollWarp: {
    width: vw(100),
    backgroundColor: color.white,
  },
  contentWarp: {
    marginTop: getPixel(5),
    alignItems: 'center',
    marginBottom: getPixel(143),
  },
  imageStyle: {
    paddingVertical: 0,
    margin: 0,
  },
  imgStyle: {
    width: getPixel(620),
    marginRight: 0,
    marginBottom: 0,
  },
  imgShadowStyle: {
    width: vw(100),
    height: getPixel(570),
    marginTop: getPixel(-20),
  },
  bottomTip: {
    ...font.body3LightFlatStyle,
    color: color.grayBase,
    marginTop: getPixel(15),
  },
  shadowBottomTip: {
    marginTop: getPixel(5),
  },
  iconView: {
    width: getPixel(60),
  },
  iconClose: {
    fontSize: getPixel(42),
  },
  headerWrapper: {
    width: vw(100),
    height: getPixel(116),
    borderTopRightRadius: getPixel(24),
    borderTopLeftRadius: getPixel(24),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getPixel(32),
    overflow: 'hidden',
  },
  titleStyle: {
    ...font.title4MediumStyle,
    color: color.fontPrimary,
  },
  headerRight: {
    width: getPixel(60),
    alignItems: 'flex-end',
  },
});
interface ILicenseModals {
  supplierData: any;
  hwRatio?: number;
  isModal?: boolean;
  visible?: boolean;
  onClose: () => void;
  title?: string;
}

interface IImgContent {
  supplierData: any;
  hwRatio?: number;
}

const ImgContent = React.memo((props: IImgContent) => {
  const { supplierData, hwRatio } = props;
  const isImageWithShadow =
    supplierData?.licenseImgUrl === ImageUrl.licenseImgUrl;
  const scrollViewRef = useRef<ScrollView>();

  return (
    <ScrollView
      ref={scrollViewRef}
      style={styles.scrollWarp}
      bounces={false}
      scrollEventThrottle={100}
    >
      <View style={styles.contentWarp}>
        <PictureBox
          onPress={noop}
          data={[autoProtocol(supplierData && supplierData.licenseImgUrl)]}
          // 后续支持imgStyle传入数组
          // @ts-ignore
          imgStyle={[
            styles.imgStyle,
            { height: getPixel(620) * hwRatio },
            isImageWithShadow && styles.imgShadowStyle,
          ]}
          style={styles.imageStyle}
        />
        {!!supplierData && !!supplierData.companyName && (
          <View>
            <Text style={styles.companyName}>{supplierData.companyName}</Text>
          </View>
        )}
        {!!supplierData?.bottomTip && !!hwRatio && (
          <View style={layout.flexRow}>
            <Text
              style={[
                styles.bottomTip,
                isImageWithShadow && styles.shadowBottomTip,
              ]}
            >
              {supplierData?.bottomTip}
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
});

const ModalHeader = ({
  onClose,
  title,
}: {
  onClose: () => void;
  title?: string;
}) => {
  return (
    <ImageBackground
      source={{ uri: ImageUrl.busLicenseModalHeaderBg }}
      style={styles.headerWrapper}
    >
      <BbkComponentTouchable style={styles.iconView} onPress={onClose}>
        <Text type="icon" style={styles.iconClose}>
          {icon.cross}
        </Text>
      </BbkComponentTouchable>
      <Text style={styles.titleStyle}>
        {title || '营业执照'}
      </Text>
      <View style={styles.headerRight} />
    </ImageBackground>
  );
};

const BusLicenseIndexIncludes: React.FC<ILicenseModals> = ({
  supplierData,
  onClose,
  visible,
  title,
}) => {
  const [hwRatio, setHwRatio] = useState(0);
  useEffect(() => {
    if (visible && supplierData?.licenseImgUrl) {
      Loading.showMaskLoading({
        cancelable: true,
      });
      Utils.getImageSize(
        supplierData.licenseImgUrl,
        (width, height) => {
          setHwRatio(height / width);
          Loading.hideMaskLoading();
        },
        () => {
          // 图片加载失败，按目前设计稿默认宽高计算
          setHwRatio(1027 / 686);
          Loading.hideMaskLoading();
        },
      );
    }
  }, [visible, supplierData]);

  const handleClose = useCallback(() => {
    onClose?.();
    Loading.hideMaskLoading();
  }, [onClose]);

  return (
    <BbkComponentModal
      modalVisible={visible && !!hwRatio}
      onRequestClose={handleClose}
    >
      <View style={styles.wrapper}>
        <ModalHeader title={title} onClose={onClose} />
        {!!supplierData?.licenseImgUrl && (
          <ImgContent supplierData={supplierData} hwRatio={hwRatio} />
        )}
      </View>
    </BbkComponentModal>
  );
};

export default React.memo(BusLicenseIndexIncludes);
