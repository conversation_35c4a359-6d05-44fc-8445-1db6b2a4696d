/* eslint-disable */

/* bbk-component-business-migrate */
import React from 'react';
import { View, StyleSheet, Image, ViewStyle } from 'react-native';
import { LinearGradient } from '@qnpm/ctrip/crn';
import { BbkUtils } from '../../../Common/src/Utils';
import BbkText from '../../../Common/src/Components/Basic/Text';
import { color, font, icon } from '../../../Common/src/Tokens';
import { withTheme } from '../../../Common/src/Theming';
import { getSharkValue } from '../../../Common/src/Shark/src/Index';

const styles = StyleSheet.create({
  mainContainer: {
    width: BbkUtils.vw(100),
    backgroundColor: color.white,
    paddingTop: BbkUtils.getPixel(32),
  },
  wrap: {
    height: BbkUtils.getPixel(40),
    width: BbkUtils.getPixel(218),
    overflow: 'hidden',
    position: 'relative',
    paddingLeft: BbkUtils.getPixel(26),
    justifyContent: 'center',
  },
  textStyle: {
    color: color.white,
    ...font.body3BoldStyle,
  },
  iconStyle: {
    fontFamily: icon.getIconFamily(),
    color: color.white,
    fontSize: BbkUtils.getPixel(30),
    marginRight: BbkUtils.getPixel(4),
    marginTop: BbkUtils.getPixel(2),
  },
  imgStyle: {
    width: BbkUtils.getPixel(16),
    height: BbkUtils.getPixel(40),
    position: 'absolute',
    right: 0,
  },
  singleContainer: {
    backgroundColor: color.white,
    marginRight: BbkUtils.getPixel(10),
  },
  singleWrap: {
    height: BbkUtils.getPixel(36),
    width: BbkUtils.getPixel(156),
    paddingLeft: 0,
    alignItems: 'center',
    borderRadius: BbkUtils.getPixel(2),
  },
  singleTextStyle: {
    ...font.labelLBoldStyle,
  },
  singleIconStyle: {
    fontSize: BbkUtils.getPixel(26),
  },
});

interface LikeLabelProps {
  style?: ViewStyle;
  isThrough?: boolean;
}

const LikeLabel: React.FC<LikeLabelProps> = ({ style, isThrough = true }) => (
  <View
    style={[isThrough ? styles.mainContainer : styles.singleContainer, style]}
  >
    <LinearGradient
      style={[styles.wrap, !isThrough && styles.singleWrap]}
      start={{ x: 0.0, y: 0.0 }}
      end={{ x: 1.0, y: 1.0 }}
      locations={[0, 1]}
      colors={[color.likeGradient1, color.likeGradient2]}
    >
      <View style={{ flexDirection: 'row' }}>
        <BbkText
          type="icon"
          style={[styles.iconStyle, !isThrough && styles.singleIconStyle]}
        >
          &#xe516;
        </BbkText>
        <BbkText
          style={[styles.textStyle, !isThrough && styles.singleTextStyle]}
        >
          {getSharkValue('listCombine_youMayLike')}
        </BbkText>
      </View>
      {isThrough && (
        <Image
          source={{
            uri: BbkUtils.autoProtocol(
              'http://pic.c-ctrip.com/car/osd/mobile/bbk/resource/LikeTriangle.png',
            ),
          }}
          style={styles.imgStyle}
        />
      )}
    </LinearGradient>
  </View>
);
export default withTheme(LikeLabel);
