import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ModalProps,
  Image,
  TouchableOpacity,
  Modal,
} from 'react-native';
import { LinearGradient, CRNModal, URL, Application } from '@ctrip/crn';
import {
  getPixel,
  isHarmony,
  versionCompare,
} from '@ctrip/rn_com_car/dist/src/Utils/src/Utils';
import { color as colors } from '@ctrip/rn_com_car/dist/src/Tokens';
// import { texts } from './Texts';

const color = {
  linearGradientOrangeLight: colors.linearGradientOrangeLight,
  linearGradientOrangeDark: colors.linearGradientOrangeDark,
  white: colors.white,
  gray: colors.fontSecondary,
  backgroundGray: colors.couponItemBg,
  line: colors.grayBorder,
  primary: colors.blueGrayBase,
  containerBg: colors.blackTransparent,
};

const BbkUpgradeModal: React.FC<
  ModalProps & { upgradeModalShowEndVersion?: string }
> = ({ upgradeModalShowEndVersion }) => {
  // 版本大于 8.23.0 不展示此模块
  if (
    versionCompare(
      Application.version,
      upgradeModalShowEndVersion || '8.23.0',
    ) >= 0
  )
    return null;

  const onPressBtn = () => {
    URL.openURL('https://m.ctrip.com/webapp/mkt/centerpage/download/');
  };
  const NewModal = isHarmony ? Modal : CRNModal;
  return (
    <NewModal transparent={true} animationType="fade" visible={true}>
      <View style={styles.modalContainer}>
        <View style={styles.modalWrapper}>
          <View style={styles.content}>
            <Image
              resizeMode="stretch"
              style={styles.headImg}
              source={{
                uri: 'https://pages.c-ctrip.com/rncarapp/ctrip/upgrade/header.png',
              }}
            />
            <View style={styles.description}>
              <Text style={styles.title}>App版本过低</Text>
              <View style={styles.subtitleWrap}>
                <Image
                  style={styles.itemIcon}
                  source={{
                    uri: 'https://pages.c-ctrip.com/rncarapp/ctrip/upgrade/block.png',
                  }}
                />
                <Text style={styles.subtitle}>
                  当前版本已经不能支持正常浏览和使用租车服务。为了给您带来更好的体验，请升级后使用。
                </Text>
              </View>
              <TouchableOpacity onPress={onPressBtn} activeOpacity={0.5}>
                <LinearGradient
                  style={styles.goDownloadBtn}
                  start={{ x: 0.0, y: 0.0 }}
                  end={{ x: 1.0, y: 1.0 }}
                  locations={[0, 1]}
                  colors={[
                    color.linearGradientOrangeLight,
                    color.linearGradientOrangeDark,
                  ]}
                >
                  <Text style={styles.btnText}>升级携程旅行App</Text>
                </LinearGradient>
              </TouchableOpacity>

              <Image
                resizeMode="stretch"
                style={styles.line}
                source={{
                  uri: 'https://pages.c-ctrip.com/rncarapp/ctrip/upgrade/line.png',
                }}
              />

              <View style={styles.centerWrap}>
                <Text style={styles.desc}>您也可以在支付宝或微信搜索</Text>
                <Text style={styles.desc}>
                  <Text style={[styles.desc, styles.fontBold]}>
                    「携程租车」
                  </Text>
                  享受租车服务
                </Text>
              </View>

              <View style={styles.bottomWrap}>
                <Text style={styles.desc}>上</Text>
                <View style={styles.logoWrap}>
                  <Image
                    resizeMode="contain"
                    style={styles.logo}
                    source={{
                      uri: 'https://pages.c-ctrip.com/rncarapp/ctrip/upgrade/alipay.png',
                    }}
                  />
                  <Image
                    resizeMode="contain"
                    style={styles.logo}
                    source={{
                      uri: 'https://pages.c-ctrip.com/rncarapp/ctrip/upgrade/wechat.png',
                    }}
                  />
                </View>
                <Text style={styles.desc}>
                  搜索
                  <Text style={[styles.desc, styles.fontBold]}>携程租车</Text>
                </Text>
              </View>
            </View>
          </View>
        </View>
      </View>
    </NewModal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: color.containerBg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalWrapper: {
    width: getPixel(616),
  },
  headImg: {
    height: getPixel(370),
  },
  goDownloadBtn: {
    width: getPixel(502),
    height: getPixel(74),
    borderRadius: getPixel(16),
    justifyContent: 'center',
    alignItems: 'center',
    top: getPixel(5),
  },
  btnText: {
    color: color.white,
    fontSize: getPixel(30),
  },
  content: {
    borderRadius: getPixel(28),
    overflow: 'hidden',
  },
  title: {
    fontSize: getPixel(36),
    fontWeight: '600',
    textAlign: 'center',
  },
  subtitleWrap: {
    marginTop: getPixel(32),
    flexDirection: 'row',
    marginLeft: getPixel(-12),
  },
  subtitle: {
    fontSize: getPixel(28),
    width: getPixel(488),
    marginBottom: getPixel(39),
    fontWeight: 'normal',
    lineHeight: getPixel(36),
    color: color.primary,
  },
  description: {
    backgroundColor: color.white,
    alignItems: 'center',
    marginTop: -getPixel(5),
  },
  desc: {
    color: color.gray,
    fontSize: getPixel(24),
    lineHeight: getPixel(32),
  },
  fontBold: {
    fontWeight: 'bold',
  },
  centerWrap: {
    marginTop: getPixel(36),
  },
  itemIcon: {
    width: getPixel(16),
    height: getPixel(16),
    top: getPixel(10),
    marginRight: getPixel(10),
  },
  bottomWrap: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: getPixel(23),
    marginBottom: getPixel(50),
    backgroundColor: color.backgroundGray,
    borderRadius: getPixel(8),
    width: getPixel(502),
    height: getPixel(74),
  },
  logoWrap: {
    flexDirection: 'row',
    marginLeft: getPixel(14),
    marginRight: getPixel(7),
  },
  logo: {
    height: getPixel(40),
    width: getPixel(40),
    marginRight: getPixel(7),
  },
  line: {
    width: getPixel(616),
    height: getPixel(2),
    marginTop: getPixel(36),
  },
});
export default BbkUpgradeModal;
