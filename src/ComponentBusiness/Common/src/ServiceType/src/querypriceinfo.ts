import { PointsType } from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';

export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  extraIndexTags?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
  errorCode?: string;
  message?: string;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface ExtensionType {
  Id?: string;
  Version?: string;
  ContentType?: string;
  Value?: string;
}

export interface ResponseStatusType {
  Timestamp?: Date;
  Ack?: string;
  Errors?: Array<ErrorsType>;
  Build?: string;
  Version?: string;
  Extension?: Array<ExtensionType>;
}

export interface CurrenctPriceInfoType {
  currencyCode?: string;
  dailyPrice?: number;
  totalPrice?: number;
  deductAmount?: number;
  actualAmount?: number;
  prepayPrice?: number;
  poaPrice?: number;
  oneWayFee?: number;
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface LabelsType {
  title?: string;
  subTitle?: string;
  code?: string;
  type?: number;
  grade?: number;
  sortNum?: number;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface ItemsType2 {
  title?: ComplexSubTitleType;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface FeeItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType2>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface PayModeInfosType {
  packageId?: number;
  payMode?: number;
  payModeName?: string;
  showPayMode?: number;
  title?: string;
  subTitle?: string;
  description?: string;
  showPrice?: string;
  isRecommended?: boolean;
  currenctPriceInfo?: CurrenctPriceInfoType;
  localPriceInfo?: CurrenctPriceInfoType;
  oneWayFeeInclusive?: boolean;
  feeItems?: Array<FeeItemsType>;
  discountItems?: Array<FeeItemsType>;
  submitName?: string;
  notices?: Array<string>;
  tips?: Array<string>;
  choiceName?: string;
  choiceDesc?: string;
  subChoiceDesc?: string;
  labels?: Array<LabelsType>;
  sortNum?: number;
  isSelected?: boolean;
  payType?: number;
  noNeedCreditCartDesc?: string;
  choiceNameTitle?: string;
  choiceNameCurrencyCode?: string;
  choiceNameAmount?: string;
  choiceNameOriginalAmount?: string;
  submitDesc?: string;
}

export interface CashBackInfoType {
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  items?: Array<FeeItemsType>;
}

export interface FeeDetailInfoType {
  chargesInfos?: Array<FeeItemsType>;
  equipmentInfos?: Array<FeeItemsType>;
  activityInfo?: FeeItemsType;
  couponInfos?: Array<FeeItemsType>;
  cashBackInfo?: CashBackInfoType;
  notIncludeCharges?: FeeItemsType;
  vehicleRentalPriceItem?: FeeItemsType;
  vehicleDepositItem?: FeeItemsType;
  chargesSummary?: FeeItemsType;
  rentalTerm?: number;
  points?: PointsType;
}

export interface PromotionType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  discountType?: number;
  adjustPriceCode?: string;
}

export interface ActivityDetailType {
  title?: string;
  status?: number;
  promotion?: PromotionType;
  promotions?: Array<PromotionType>;
}

export interface UsableCouponsType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  deductionName?: string;
  labels?: Array<LabelsType>;
  unionType?: number;
  vendorCouponCode?: string;
  vendorKey?: string;
  couponType?: number;
}

export interface NotApplyCouponType {
  title?: string;
  description?: string;
  code?: string;
  type?: number;
  typeDesc?: string;
  sortNum?: number;
}

export interface CouponListType {
  usableCoupons?: Array<UsableCouponsType>;
  unusableCoupons?: Array<UsableCouponsType>;
  selectedCoupon?: UsableCouponsType;
  notApplyCoupon?: NotApplyCouponType;
  status?: number;
  title?: string;
  subTitle?: string;
  description?: string;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  tips?: Array<string>;
}

export interface OrderPriceInfoType {
  currencyCode?: string;
  packagePrice?: number;
  dailyPrice?: number;
  totalPrice?: number;
  deductAmount?: number;
  payAmount?: number;
  poaPrice?: number;
  priceCode?: string;
  priceType?: number;
  rentalPrice?: number;
  priceVersion?: string;
}

export interface LocationsType {
  groupCode?: string;
  index?: number;
}

export interface ButtonType {
  title?: string;
  type?: number;
  desc?: string;
  tips?: string;
  icon?: string;
}

export interface FilterItemType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
  mark?: string;
  positionCode?: string;
}

export interface ItemsType2 {
  title?: ComplexSubTitleType;
  desc?: Array<ComplexSubTitleType>;
  note?: ComplexSubTitleType;
}

export interface PromptInfoType {
  title?: string;
  subTitle?: string;
  contents?: Array<ComplexSubTitleType>;
  type?: number;
  icon?: string;
  note?: string;
  locations?: Array<LocationsType>;
  button?: ButtonType;
  buttonExt?: Array<ButtonType>;
  filterItem?: FilterItemType;
  items?: Array<ItemsType2>;
  table?: Array<ItemsType2>;
}

export interface ZhimaInfoType {
  isSupportZhima?: boolean;
  isSupportVcc?: boolean;
  idNo?: string;
  userName?: string;
  idType?: string;
  authorize?: boolean;
  authStatus?: number;
  authStatusName?: string;
  orderId?: string;
  requestId?: string;
  amount?: number;
  current?: string;
  texts?: Array<NotApplyCouponType>;
  authedCountEqOne?: boolean;
  ctripSatisfy?: boolean;
  authCount?: number;
  authOrderCount?: number;
  certifyId?: string;
  certifyUrl?: string;
  authUrl?: string;
  sameDriver?: boolean;
  orderStr?: string;
  promptInfo?: PromptInfoType;
  defaultInfo?: PromptInfoType;
  authInfos?: Array<PromptInfoType>;
  noteInfo?: PromptInfoType;
}

export interface SubObjectType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  summaryContent?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  note?: string;
  sortNum?: number;
}

export interface SubObjectType2 {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  summaryContent?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType>;
  note?: string;
  sortNum?: number;
}

export interface CarRentalMustReadType {
  title?: string;
  subTitle?: string;
  content?: Array<string>;
  summaryContent?: Array<string>;
  type?: number;
  code?: string;
  urlName?: string;
  url?: string;
  urlList?: Array<string>;
  subObject?: Array<SubObjectType2>;
  note?: string;
  sortNum?: number;
}

export interface ChineseGuidReqType {
  isSelected?: boolean;
  wechat?: string;
}

export interface AddOnServicesType {
  required?: boolean;
  uniqueCode?: string;
  equipcode?: string;
  name?: string;
  desc?: string;
  unitPrice?: number;
  count?: number;
  unit?: string;
  cnyTotalPrice?: number;
  vendorServiceCode?: string;
}

export interface AddOnType {
  chineseGuidReq?: ChineseGuidReqType;
  addOnServices?: Array<AddOnServicesType>;
}

export interface SubListType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  labelCode?: string;
  positionCode?: string;
}

export interface SubListType2 {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType>;
  labelCode?: string;
  positionCode?: string;
}

export interface MarketingLabelsType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType2>;
  labelCode?: string;
  positionCode?: string;
}

export interface DepositPayInfosType {
  depositPayType?: number;
  isEnable?: boolean;
  isCheck?: boolean;
  isClickable?: boolean;
  depositTypeInfo?: ItemsType2;
  sortNum?: number;
}

export interface CostItemChangeInfosType {
  serviceCode?: string;
  serviceName?: string;
  sourceTotalPrice?: number;
  source?: Array<ComplexSubTitleType>;
  currentTotalPrice?: number;
  current?: Array<ComplexSubTitleType>;
  priceDiff?: number;
  diff?: Array<ComplexSubTitleType>;
  priceDesc?: string;
}

export interface PriceChangeInfoType {
  code?: string;
  title?: string;
  subTitle?: string;
  currPrice?: number;
  contents?: Array<ComplexSubTitleType>;
  priceDiff?: number;
  costItemChangeInfos?: Array<CostItemChangeInfosType>;
  encourageInfo?: ComplexSubTitleType;
  buttons?: Array<ButtonType>;
}

export interface TrackInfoType {
  vendorPlatFrom?: number;
  depositFreeType?: number;
  depositType?: number;
  riskOriginal?: string;
  riskFinal?: string;
  zhimaResult?: string;
  zhimaResultDesc?: string;
}

export interface PriceInfoResponseType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
  payModeInfos?: Array<PayModeInfosType>;
  feeDetailInfo?: FeeDetailInfoType;
  activityDetail?: ActivityDetailType;
  promotionList?: Array<PromotionType>;
  couponList?: CouponListType;
  cancelRuleInfo?: FeeItemsType;
  confirmInfo?: FeeItemsType;
  depositInfo?: FeeItemsType;
  invoiceInfo?: FeeItemsType;
  positivePolicies?: Array<LabelsType>;
  otherFees?: Array<FeeItemsType>;
  orderPriceInfo?: OrderPriceInfoType;
  zhimaInfo?: ZhimaInfoType;
  carRentalMustRead?: Array<CarRentalMustReadType>;
  addOn?: AddOnType;
  marketingLabels?: Array<MarketingLabelsType>;
  driverTips?: ComplexSubTitleType;
  payInstruction?: ItemsType2;
  depositPayInfos?: Array<DepositPayInfosType>;
  depositLabel?: LabelsType;
  promptInfos?: Array<PromptInfoType>;
  creditVersion?: string;
  isSoldOut?: boolean;
  priceChangeInfo?: PriceChangeInfoType;
  trackInfo?: TrackInfoType;
}

interface DownGradeButtonInfoType {
  code: number;
  name: string;
}

export interface DownGradeInfoType {
  title: string;
  contents: Array<string>;
  buttons: Array<DownGradeButtonInfoType>;
  distanceDesc: string;
}

export interface MembershipPerceptionType {
  curLevelCode: string;
  curLevelName: string;
  preferentialTip: string;
}
