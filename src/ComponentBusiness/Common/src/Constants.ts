import { CurLevelCode } from '@ctrip/rn_com_car/dist/src/Logic/src/Member/Types/MemberShipRights';
import { color, icon } from '@ctrip/rn_com_car/dist/src/Tokens';
import { Utils } from '../../../Util/Index';
import { texts } from './Texts';

export const ImageHost = 'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/';

export const HOLIDAY_IMAGES = {
  UNSELECTED: `${ImageHost}rent_holidayUnlabel.png`,
  SELECTED: `${ImageHost}rent_holidayLabel.png`,
};
const img04Src = 'https://dimg04.c-ctrip.com/images';
const memberlline = Utils.compatImgUrlWithWebp(
  `${img04Src}/0AS6712000984zob14190.png`,
);
// 权益特权相关样式
const MemberStyle = {
  // 普通
  [CurLevelCode.Normal]: {
    linearGradient: [
      color.linearGradientNormalStart,
      color.linearGradientNormalEnd,
    ],
    titleBg: color.normalTitleBg,
    tipBg: color.normalTipBg,
    tipText: color.normalTipText,
    line: color.normalLine,
    text: color.normalText,
    circleBg: color.linearGradientNormalStart,
    lockImg: `${ImageHost}member/normalLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonNormalStart,
      color.linearGradientButtonNormalEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuNormalStart,
      color.linearGradientMenuNormalEnd,
    ],
    icon: `${ImageHost}member/normalIcon.png`,
    title: `${ImageHost}member/normalTitle.png`,
    memberText: color.white,
    number: color.normalNumber,
    booking: null,
    homeModal: null,
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS4n12000aoblimsA212.png`,
    ),
    homeBlockTitleColor: color.normalHomeBlockTitleColor,
  },
  // 白银
  [CurLevelCode.Silver]: {
    linearGradient: [
      color.linearGradientSilverStart,
      color.linearGradientSilverEnd,
    ],
    titleBg: color.silverTitleBg,
    tipBg: color.silverTipBg,
    tipText: color.silverTipText,
    line: color.silverLine,
    text: color.silverText,
    circleBg: color.linearGradientSilverStart,
    lockImg: `${ImageHost}member/silverLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonSilverStart,
      color.linearGradientButtonSilverEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuSilverStart,
      color.linearGradientMenuSilverEnd,
    ],
    icon: `${ImageHost}member/silverIcon.png`,
    title: `${ImageHost}member/silverTitle.png`,
    memberText: color.white,
    number: color.silverNumber,
    booking: null,
    homeModal: null,
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS5312000aoblkbv44DB.png`,
    ),
    homeBlockTitleColor: color.silverHomeBlockTitleColor,
  },
  // 黄金
  [CurLevelCode.Gold]: {
    linearGradient: [
      color.linearGradientGoldStart,
      color.linearGradientGoldEnd,
    ],
    titleBg: color.goldTitleBg,
    tipBg: color.goldTipBg,
    tipText: color.goldTipText,
    line: color.goldLine,
    text: color.goldText,
    circleBg: color.linearGradientGoldStart,
    lockImg: `${ImageHost}member/goldLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonGoldStart,
      color.linearGradientButtonGoldEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuGoldStart,
      color.linearGradientMenuGoldEnd,
    ],
    icon: `${ImageHost}member/goldIcon.png`,
    title: `${ImageHost}member/goldTitle.png`,
    memberText: color.white,
    number: color.goldNumber,
    booking: {
      positivePoliciesImgTxtColor: color.goldBookingTip,
      positivePoliciesImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS5r1200095p0b645E75.png`,
      ),
      positivePoliciesIcon: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS1v1200095ozoka76CF.png`,
      ),
      couponLableImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS0g1200095p05ny2BE8.png`,
      ),
      couponLabelbg: color.goldCouponBg,
      couponLabelTxt: color.goldBookingTxt,
      couponPreTxt: texts.goldCouponPreTxt,
    },
    homeModal: {
      rightLinearGradient: [
        color.linearGradientMenuGoldStart,
        color.linearGradientMenuGoldEnd,
      ],
      bg1: Utils.compatImgUrlWithWebp(`${img04Src}/1of2t12000atss6ii61CE.png`),
      bg2: Utils.compatImgUrlWithWebp(`${img04Src}/1of4p12000atsrz9dEC18.png`),
      btnBg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS0d1200095z9rj7FD7B.png`,
      ),
      btnTxtColor: color.white,
      line: memberlline,
    },
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS6k12000aoblg1h8749.png`,
    ),
    homeBlockTitleColor: color.goldBookingTxt,
  },
  // 铂金
  [CurLevelCode.Platinum]: {
    linearGradient: [
      color.linearGradientPlatinumStart,
      color.linearGradientPlatinumEnd,
    ],
    titleBg: color.platinumTitleBg,
    tipBg: color.platinumTipBg,
    tipText: color.platinumTipText,
    line: color.platinumLine,
    text: color.platinumText,
    circleBg: color.linearGradientPlatinumStart,
    lockImg: `${ImageHost}member/platinumLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonPlatinumStart,
      color.linearGradientButtonPlatinumEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuPlatinumStart,
      color.linearGradientMenuPlatinumEnd,
    ],
    icon: `${ImageHost}member/platinumIcon.png`,
    title: `${ImageHost}member/platinumTitle.png`,
    memberText: color.white,
    number: color.platinumNumber,
    booking: {
      positivePoliciesImgTxtColor: color.platinumBookingTip,
      positivePoliciesImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS0c1200095p002m1772.png`,
      ),
      positivePoliciesIcon: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS3t1200095p0mqaF9D7.png`,
      ),
      couponLableImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS6v1200095p0jjsB51E.png`,
      ),
      couponLabelbg: color.platinumCouponBg,
      couponLabelTxt: color.platinumBookingTxt,
      couponPreTxt: texts.platinumCouponPreTxt,
    },
    homeModal: {
      rightLinearGradient: [
        color.linearGradientMenuPlatinumStart,
        color.linearGradientMenuPlatinumEnd,
      ],
      bg1: Utils.compatImgUrlWithWebp(`${img04Src}/1of6512000atssj3b94D2.png`),
      bg2: Utils.compatImgUrlWithWebp(`${img04Src}/1of1a12000atssa988849.png`),
      btnBg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS6v1200095z9nko762A.png`,
      ),
      btnTxtColor: color.white,
      line: memberlline,
    },
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS4v12000aoblk42AABA.png`,
    ),
    homeBlockTitleColor: color.platinumBookingTxt,
  },
  // 钻石
  [CurLevelCode.Diamond]: {
    linearGradient: [
      color.linearGradientDiamondStart,
      color.linearGradientDiamondEnd,
    ],
    titleBg: color.diamondTitleBg,
    tipBg: color.diamondTipBg,
    tipText: color.diamondTipText,
    line: color.diamondLine,
    text: color.diamondText,
    circleBg: color.linearGradientDiamondStart,
    lockImg: `${ImageHost}member/goldLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonDiamondStart,
      color.linearGradientButtonDiamondEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuDiamondStart,
      color.linearGradientMenuDiamondEnd,
    ],
    icon: `${ImageHost}member/diamondIcon.png`,
    title: `${ImageHost}member/diamondTitle.png`,
    memberText: color.memberText,
    number: color.diamondNumber,
    booking: {
      positivePoliciesImgTxtColor: color.diamondBookingTip,
      positivePoliciesImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS0v1200095ozkcsF2E5.png`,
      ),
      positivePoliciesIcon: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS5a1200095p04s1B4CE.png`,
      ),
      couponLableImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS691200095ozuwl40FF.png`,
      ),
      couponLabelbg: color.diamondCouponBg,
      couponLabelTxt: color.diamondBookingTxt,
      couponPreTxt: texts.diamondCouponPreTxt,
    },
    homeModal: {
      rightLinearGradient: [
        color.linearGradientMenuDiamondStart,
        color.linearGradientMenuDiamondEnd,
      ],
      bg1: Utils.compatImgUrlWithWebp(`${img04Src}/1of5y12000atss09eB5A4.png`),
      bg2: Utils.compatImgUrlWithWebp(`${img04Src}/1of0612000atss3ob5C2D.png`),
      btnBg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS701200095z8zh4C6D8.png`,
      ),
      btnTxtColor: color.memberGoRent,
      line: memberlline,
    },
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS4v12000aoblmqz5290.png`,
    ),
    homeBlockTitleColor: color.linearGradientDiamondStart,
  },
  // 金钻
  [CurLevelCode.GoldDiamond]: {
    linearGradient: [
      color.linearGradientGoldDiamondStart,
      color.linearGradientGoldDiamondEnd,
    ],
    titleBg: color.goldDiamondTitleBg,
    tipBg: color.goldDiamondTipBg,
    tipText: color.goldDiamondTipText,
    line: color.goldDiamondLine,
    text: color.goldDiamondText,
    circleBg: color.linearGradientGoldDiamondStart,
    lockImg: `${ImageHost}member/goldLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonGoldDiamondStart,
      color.linearGradientButtonGoldDiamondEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuGoldDiamondStart,
      color.linearGradientMenuGoldDiamondEnd,
    ],
    icon: `${ImageHost}member/goldDiamondIcon.png`,
    title: `${ImageHost}member/goldDiamondTitle.png`,
    memberText: color.memberText,
    number: color.goldDiamondNumber,
    booking: {
      positivePoliciesImgTxtColor: color.goldDiamondBookingTip,
      positivePoliciesImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS251200095ozxnx0C29.png`,
      ),
      positivePoliciesIcon: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS6x1200095ozsvu15B6.png`,
      ),
      couponLableImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS4w1200095p07b0510B.png`,
      ),
      couponLabelbg: color.goldDiamondCouponBg,
      couponLabelTxt: color.goldDiamondBookingTxt,
      couponPreTxt: texts.goldDiamondCouponPreTxt,
    },
    homeModal: {
      rightLinearGradient: [
        color.linearGradientMenuGoldDiamondStart,
        color.linearGradientMenuGoldDiamondEnd,
      ],
      bg1: Utils.compatImgUrlWithWebp(`${img04Src}/1of5612000atss7x92B1F.png`),
      bg2: Utils.compatImgUrlWithWebp(`${img04Src}/1of1812000atss4ubC583.png`),
      btnBg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS1g1200095z9a779517.png`,
      ),
      btnTxtColor: color.memberGoRent,
      line: memberlline,
    },
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS2k12000aoblj10800A.png`,
    ),
    homeBlockTitleColor: color.goldDiamondBookingTip,
  },
  // 黑钻
  [CurLevelCode.BlackDiamond]: {
    linearGradient: [
      color.linearGradientBlackDiamondStart,
      color.linearGradientBlackDiamondEnd,
    ],
    titleBg: color.blackDiamondTitleBg,
    tipBg: color.blackDiamondTipBg,
    tipText: color.blackDiamondTipText,
    line: color.blackDiamondLine,
    text: color.blackDimamndText,
    circleBg: color.linearGradientBlackDiamondStart,
    lockImg: `${ImageHost}member/goldLock.png`,
    buttonLinearGradient: [
      color.linearGradientButtonBlackDiamondStart,
      color.linearGradientButtonBlackDiamondEnd,
    ],
    menuLinearGradient: [
      color.linearGradientMenuBlackDiamondStart,
      color.linearGradientMenuBlackDiamondEnd,
    ],
    icon: `${ImageHost}member/blackDiamondIcon.png`,
    title: `${ImageHost}member/blackDiamondTitle.png`,
    memberText: color.memberText,
    number: color.blackDiamondNumber,
    booking: {
      positivePoliciesImgTxtColor: color.blackDiamondBookingTip,
      positivePoliciesImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS0k1200095p05ixE218.png`,
      ),
      positivePoliciesIcon: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS2b1200095p053p4272.png`,
      ),
      couponLableImg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS6y1200095p0egr95C8.png`,
      ),
      couponLabelbg: color.blackDiamondCouponBg,
      couponLabelTxt: color.blackDiamondBookingTxt,
      couponPreTxt: texts.blackDiamondCouponPreTxt,
    },
    homeModal: {
      rightLinearGradient: [
        color.blackDiamondNumber,
        color.linearGradientMenuBlackDiamondEnd,
      ],
      bg1: Utils.compatImgUrlWithWebp(`${img04Src}/1of4112000atssdwv42DE.png`),
      bg2: Utils.compatImgUrlWithWebp(`${img04Src}/1of2u12000atsshdd888C.png`),
      btnBg: Utils.compatImgUrlWithWebp(
        `${img04Src}/0AS5f1200095z9h8a0F8F.png`,
      ),
      btnTxtColor: color.memberGoRent,
      line: memberlline,
    },
    homeBlockBg: Utils.compatImgUrlWithWebp(
      `${img04Src}/0AS3t12000aoblm5sA890.png`,
    ),
    homeBlockTitleColor: color.blackDiamondBookingTip,
  },
};

const MemberNumber = {
  '1': icon.memberLockedOne,
  '2': icon.memberLockedTwo,
  '3': icon.memberLockedThree,
  '4': icon.memberLockedFour,
  '5': icon.memberLockedFive,
};

const OrderCouponImgs = {
  redIcon: Utils.compatImgUrlWithWebp(`${img04Src}/0AS3b120009ifrigg2D04.png`),
  modalBg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS4x120009iia4486565.png`),
  modalBg2: Utils.compatImgUrlWithWebp(`${img04Src}/0AS4m120009ii9wya3E06.png`),
  redImg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS6c120009ifrt2sC5CE.png`),
  getBtn: Utils.compatImgUrlWithWebp(`${img04Src}/0AS4s120009ifrsrjCDFF.png`),
  itemBg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS3a120009ig38na2CE4.png`),
  received: Utils.compatImgUrlWithWebp(`${img04Src}/0AS44120009m2q4kxC210.png`),
  getBtnYellow: Utils.compatImgUrlWithWebp(
    `${img04Src}/0AS4l120009m2qgslBB2E.png`,
  ),
  highest: Utils.compatImgUrlWithWebp(`${img04Src}/0AS48120009ihhayu65B0.png`),
};
export const checkBoxImgs = {
  isCheck: Utils.compatImgUrlWithWebp(`${img04Src}/0AS01120009fmeznj5EC5.png`),
  unCheck: Utils.compatImgUrlWithWebp(`${img04Src}/0AS1u120009fmeixqB174.png`),
};

export const depositPaymentModalImgs = {
  modalBg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS1j120009fon6orD7CB.png`),
  noDepositBadge: Utils.compatImgUrlWithWebp(
    `${img04Src}/0AS36120009gzkxrr677A.png`,
  ),
  depositBadge: Utils.compatImgUrlWithWebp(
    `${img04Src}/0AS27120009fomv5c8A80.png`,
  ),
  checkImg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS2v120009fonqcs325E.png`),
  giveUpBg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS1s120009g7teky8280.png`),
};

// fontFamily: 'ct_font_common'
// 携程内置公共字体图标编码
export const ctFontCommon = {
  help: '&#xe013;',
};

export default {
  HOLIDAY_IMAGES,
  MemberStyle,
  MemberNumber,
  ImageHost,
  OrderCouponImgs,
  checkBoxImgs,
  depositPaymentModalImgs,
  ctFontCommon,
};

// 租车定位API BizType
export const LocationBizType = 'zuche-3e94f659-8acf-4cf1-a904-4e81263de192';

export const CashBackBg = Utils.compatImgUrlWithWebp(
  `${img04Src}/0AS6c120009ifrt2sC5CE.png`,
);
export const CancelTipImg = Utils.compatImgUrlWithWebp(
  `${img04Src}/1of3x12000c4exs6kF8DC.png`,
);

export const CancelReasonGuide = {
  logoIcon: Utils.compatImgUrlWithWebp(`${img04Src}/0AS0j120009j7oygb7F46.png`),
  iconHelp: Utils.compatImgUrlWithWebp(`${img04Src}/0AS2b120009jhwlwcF1AC.png`),
  triangle: Utils.compatImgUrlWithWebp(`${img04Src}/0AS2p120009j7p4ciE7A2.png`),
  bg: Utils.compatImgUrlWithWebp(`${img04Src}/0AS0q120009m8wvdy422F.png`),
};

export const cashBackBgs = {
  0: Utils.compatImgUrlWithWebp(`${img04Src}/0AS52120009mgp0ojC7D9.png`),
  1: Utils.compatImgUrlWithWebp(`${img04Src}/0AS0j120009mgoy2p95AA.png`),
  2: Utils.compatImgUrlWithWebp(`${img04Src}/0AS3g120009mgp2kzDA17.png`),
};
