import { Channel, AppContext } from '../../../Util/Index';

export const getSideToolBoxConfig = ({
  pageId,
  bizType = 'carRental',
  styleType = 1,
  jumpUrl,
}) => {
  const { Home, List } = Channel?.getPageId?.() || {};
  return {
    bizType,
    styleType,
    customerServiceConfig: {
      jumpUrl,
    },
    feedbackConfig: {
      tag: '',
      feedback: {
        pageId,
        source: 20,
        productType: 12,
      },
    },
    shareConfig: {
      isShow: [Home?.ID, List?.ID].includes(
        AppContext?.PageInstance?.getPageId?.(),
      ), // 分享API Ctrip APP 8.48 以后都支持，发布最低版本为8.52.6，所以无需判断版本
    },
  };
};
