/* eslint-disable no-param-reassign */
/* eslint-disable global-require */
// @ts-ignore 8.25版本 tsd没有导出IBUSharkUtil
import { crnMarket } from './bbk-marketInfo';
import { AppContext, Utils, CarStorage, CarLog } from './Util/Index';
import {
  initializeABTesting,
  initializePreABTestingSync,
} from './Util/ABTesting';
import { Platform, LogKey, LogKeyDev } from './Constants/Index';
import { PageSearchTraceType } from './Constants/CommonEnums';
import BuildTime from './BuildTime';
import {
  driverAgeAndNumberStore,
  locationAndDateStore,
  marketStore,
} from './State/Index';

const StorageKey = {
  CAR_CROSS_LIST_PARAMETER_ISD: 'car_cross_list_parameter_isd',
  CAR_CROSS_LIST_PARAMETER_OSD: 'car_cross_list_parameter_osd',
  DEBUG: 'debugEntrance',
};
export enum MarketInfoPlatform {
  App = 'app',
}

const marketInfo = crnMarket.getInstance(MarketInfoPlatform.App);

const initializeNewHomeParams = (urlQuery, url) => {
  // 首页融合不包含修改订单
  const notFromModifyOrder = !urlQuery?.ctripOrderId;
  // 2022-8-3 兼容H5唤醒时，因url中的参数公共解析异常，urlQuery中无数据，导致在融合首页却打开的是非融合首页的页面
  const isInPlatHome =
    urlQuery?.isHomeCombine === 'true' || url?.includes('isHomeCombine=true');
  AppContext.setIsHomeCombine(notFromModifyOrder && isInPlatHome);
};

const initializeUserFetchCacheId = (props: any) => {
  const { urlQuery } = props;
  if (urlQuery && urlQuery.queryListCacheId) {
    AppContext.setUserFetchCacheId({
      queryListCacheId: urlQuery.queryListCacheId,
      actionType: 'initializeUserFetchCacheId',
    });
  }
};

// 记录融合首页tab拆分的AB实验信息
const initHomeTabABInfo = urlQuery => {
  const abtforsubtabKey = urlQuery?.abtforsubtabKey;
  const abtforsubtabVal = urlQuery?.abtforsubtab;
  const result: any = {};
  if (abtforsubtabKey && abtforsubtabVal) {
    result[abtforsubtabKey] = {
      ExpCode: abtforsubtabKey,
      ExpVersion: abtforsubtabVal,
    };
    AppContext.setABTesting(result);
    AppContext.setHomeTabInfo({ key: abtforsubtabKey, val: abtforsubtabVal });
  }

  // 记录从融合首页透传到其它页面的ab实验信息
  const homeTabAbVersionInfo = urlQuery?.homeTabAbVersionInfo;
  const result2: any = {};
  if (homeTabAbVersionInfo) {
    const [abKey, abVal] = homeTabAbVersionInfo.split('|');
    if (abKey && abVal) {
      result2[abKey] = {
        ExpCode: abKey,
        ExpVersion: abVal,
      };
      AppContext.setABTesting(result2);
    }
  }
};

const getInitAppType = props => {
  // 2022-8-5 对于融合首页拆分版本, appType取staticQuery
  if (Utils.isNewHomeTab()) {
    return Utils.getAppType(props?.staticQuery?.apptype);
  }
  return Utils.getAppType(AppContext.UrlQuery.apptype);
};

// 初始化urlQuery
const initUrlQuery = props => {
  const { urlQuery, dynmicQuery } = props;
  // 对于融合首页拆分版本,如果dynmicQuery中带的appType与当前appType不符的话，则过滤掉urlQuery中的信息, 只取配置中需保留的字段
  if (Utils.isNewHomeTab() && dynmicQuery?.apptype !== getInitAppType(props)) {
    const validQuery = Object.keys(urlQuery ?? {}).reduce((pre, cur) => {
      if (Platform.FIELD_TO_KEEP.includes(cur.toLowerCase())) {
        pre[cur] = urlQuery[cur];
      }
      return pre;
    }, {});
    AppContext.setUrlQuery(validQuery);
  } else {
    AppContext.setUrlQuery(urlQuery);
  }
};

const initializePropsUrl = (props: any) => {
  const { url, urlQuery, dynmicQuery, staticQuery } = props;
  initHomeTabABInfo(urlQuery);
  AppContext.setUrl(url);
  initUrlQuery(props);
  AppContext.setOriginUrlQuery(dynmicQuery);
  AppContext.setContentUrlQuery(staticQuery);
  initializeNewHomeParams(urlQuery, url);
};

// initialize car environment
// buildTime, apptype
const initializeCarEnv = props => {
  AppContext.setCarEnv({
    buildTime: BuildTime,
    appType: getInitAppType(props),
    env: AppContext.UrlQuery.env,
    testid: AppContext.UrlQuery.testid,
    mock: AppContext.UrlQuery.mock,
  });
  AppContext.setABNewDetail(true);
};

export const setUserTraceQueryVid = () => {
  const driverAgeAndNumber = driverAgeAndNumberStore.getState();
  const locationAndDate = locationAndDateStore.getState();
  const nextDriverAgeAndNumber = JSON.stringify(driverAgeAndNumber);
  const nextLocationAndDate = JSON.stringify(locationAndDate);
  const { queryVid } = AppContext.UserTrace;
  AppContext.setUserTraceQueryVid({
    nextDriverAgeAndNumber,
    nextLocationAndDate,
  });
  if (queryVid !== AppContext.UserTrace.queryVid) {
    CarLog.LogTrace({
      key: LogKey.c_car_page_search,
      info: {
        pageId: Utils.getCurPageId(),
        traceType: PageSearchTraceType.AppLoad,
      },
    });
  }
};

const initializeListQueryParameterSync = () => {
  const listKey = StorageKey.CAR_CROSS_LIST_PARAMETER_ISD;
  try {
    const parameter = CarStorage.privateLoadSync(listKey, false);
    if (parameter) {
      AppContext.setUrlQuery(
        AppContext.UrlQuery,
        'listParameter',
        JSON.parse(parameter),
      );
    }
  } catch (e) {
    // console.log(e)
  }
};

export const getMarketInfoParam = (param = {}) => ({
  query: AppContext.UrlQuery,
  appid: Platform.APP_ID.CTRIP,
  platform: MarketInfoPlatform.App,
  path: AppContext?.PageInstance?.props?.app?.url,
  pageID: Utils.getCurPageId(),
  ubt: Utils.getUBT(),
  ...param,
});

const setMarketInfoToAppContext = (isLoadFinished = false) => {
  const {
    ChannelID,
    ChildChannelId,
    VisitortraceId,
    SID,
    AllianceID,
    AwakeTime,
  } = marketInfo.getCarMarket() || {};
  const { vid } = Utils.getUBT();

  AppContext.setMarketInfo({
    channelId: ChannelID || Platform.CHANNEL_ID.CTRIP_DEFAULT,
    childChannelId: ChildChannelId || '',
    visitortraceId: VisitortraceId || '',
    sId: SID || '',
    aId: AllianceID || '',
    awakeTime: AwakeTime || '',
    vid,
    isLoadFinished,
  });
};

const initializeChannelId = (props: any) => {
  const param = getMarketInfoParam({ path: props?.url });
  marketInfo.setCarChannelID(param);
  setMarketInfoToAppContext();
};

const initializeMarketInfo = async (props: any) => {
  const param = getMarketInfoParam({ path: props?.url });
  await marketInfo.setCarMarket(param);
  setMarketInfoToAppContext(true);
};

const appLoad = async (props: any) => {
  initializeMarketInfo(props);
  initializeABTesting();
};

const appPreLoadSync = () => {
  initializePreABTestingSync();

  AppContext.setLanguageInfo({
    locale: 'zh_cn',
    site: 'cn',
    currency: 'CNY',
    language: 'cn',
    standardLocale: 'zh-CN',
  });
};

export const initialStoreWithParam = urlQuery => {
  const initialPage = urlQuery?.initialPage || 'Home';
  let fakeUrl = decodeURIComponent(AppContext.Url);
  const isCombineCross = AppContext.isHomeCombine && initialPage === 'Home';
  if (isCombineCross) {
    if (!(fakeUrl.indexOf('landingto') > -1)) {
      fakeUrl += '&landingto=home';
    }
  }

  marketStore.getState().loadMarket({
    fakeUrl,
  });
  CarLog.LogTraceDevNoState({
    key: LogKeyDev.c_car_trace_cross_params,
    info: {
      isCombineCross,
      fakeUrl,
    },
  });
};

export {
  appPreLoadSync,
  initializePropsUrl,
  initializeChannelId,
  initializeCarEnv,
  initializeUserFetchCacheId,
  initializeListQueryParameterSync,
};

export default appLoad;
