
import { FETCH_QUERY_QUESTIONNAIRE } from "../Voc/Types";

const needToBeInterceptedActions = [FETCH_QUERY_QUESTIONNAIRE];
const OrderDetailInterceptor = store => next => action => {
  // 用于处理打开多个订详时，数据混乱问题。此处可以拦截非 active 状态下订单的一些 action
  if (needToBeInterceptedActions.includes(action?.type) && action?.data?.orderId) {
    if (global._activeOrderDetailId != action.data.orderId) {
      return;
    }
  }
  return next(action);
};

export default OrderDetailInterceptor;
