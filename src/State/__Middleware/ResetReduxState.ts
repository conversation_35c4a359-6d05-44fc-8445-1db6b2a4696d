import { RESET_REDUX_STATE, SET_REDUX_STATE } from '../__Environment/Types';

// 解决退出时容器未销毁，重复进入时 redux 数据是上一次的问题
const resetReduxState = store => next => action => {
  if (action.type === RESET_REDUX_STATE) {
    const state = store.getState();
    Object.keys(state).forEach(reducer => {
      state[reducer] = undefined;
    });
  }
  return next(action);
};

export const setReduxState = store => next => action => {
  if (action.type === SET_REDUX_STATE && action.data) {
    const state = store.getState();
    Object.keys(state).forEach(reducer => {
      state[reducer] = action.data[reducer];
    });
  }
  return next(action);
};

export default resetReduxState;
