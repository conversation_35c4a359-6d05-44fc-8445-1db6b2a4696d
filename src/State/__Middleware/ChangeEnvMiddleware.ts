import { CHANGE_ENVIRONMENT, ENV_REDUCER } from '../__Environment/Types';
import { PREVIOUS_STATE } from '../__Global/Types';
import { EnvType } from '../__Environment/Actions';
import { Utils } from '../../Util/Index';

const changeEnvMiddleware = store => next => action => {
  if (action.type === CHANGE_ENVIRONMENT) {
    const { nextEnv }: EnvType = action.playload;

    const state = store.getState();

    // 保留当前环境数据
    // 重置reducer
    const currentReducers = {};

    Object.keys(state).forEach(reducer => {
      if (state[reducer].envMeta && state[reducer].envMeta.isMultiEnvironment) {
        // LocationAndDate 校验国内境外数据
        if (reducer === 'LocationAndDate' && state[reducer].pickUp) {
          const { isDomestic } = state[reducer].pickUp;
          if (
            (Utils.isCtripIsd() && isDomestic === true) ||
            (Utils.isCtripOsd() && isDomestic === false)
          ) {
            currentReducers[reducer] = state[reducer];
          }
          /* eslint-disable no-console */
          console.error('zzz-LocationAndDate', currentReducers);
        } else {
          currentReducers[reducer] = state[reducer];
        }

        if (state[ENV_REDUCER][nextEnv][reducer]) {
          // 非首次切换时，使用上一次缓存的数据
          /* eslint-disable no-param-reassign */
          action[reducer] = state[ENV_REDUCER][nextEnv][reducer];
        } else {
          // 首次切换时，使用initialState值
          state[reducer] = undefined;
        }
      }
    });

    /* eslint-disable no-param-reassign */
    action[ENV_REDUCER] = currentReducers;
  }

  return next(action);
};

export default changeEnvMiddleware;
