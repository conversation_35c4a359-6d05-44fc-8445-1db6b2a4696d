import dayjs from '../../Common/src/Dayjs/src';
import _ from 'lodash';
import { SET_DATE_INFO, SET_LOCATION_INFO } from '../LocationAndDate/Types';
import { isValidRentalDate, isValidRentalLocation } from '../LocationAndDate/Util';

// 在更新取还车时间地点时，添加异常检测，若发生异常则进行日志上报处理
const updateLocationAndDateMiddleware = () => next => action => {
  // 更新取还车地点
  if (action.type === SET_LOCATION_INFO) {
    isValidRentalLocation(action.data);
  }
  // 更新取还车时间
  if (action.type === SET_DATE_INFO) {
    const pTime =
      _.get(action, 'data.pickUp.dateTime') || dayjs(action.data.pickup);
    const rTime =
      _.get(action, 'data.dropOff.dateTime') || dayjs(action.data.dropoff);
    isValidRentalDate(pTime, rTime, true);
  }

  return next(action);
};

export default updateLocationAndDateMiddleware;
