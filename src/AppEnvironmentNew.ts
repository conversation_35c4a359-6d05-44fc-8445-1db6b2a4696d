/* eslint-disable import/prefer-default-export */
import _ from 'lodash';
import dayjs from '@ctrip/rn_com_car/dist/src/Dayjs/src';
import { BbkUtils } from '@ctrip/rn_com_car/dist/src/Utils';
import { CarLog, CarStorage, Utils } from './Util/Index';
import { LogKeyDev, StorageKey } from './Constants/Index';

const parseStorageResult = storage => {
  if (storage) {
    try {
      return JSON.parse(storage);
    } catch (e) {
      /* eslint-disable no-console */
      console.warn('getCommentsList storage fail');
    }
  }
  return {};
};

export const getLocationAndDateStorageSync = () => {
  const storage = CarStorage.privateLoadSync(
    StorageKey.CAR_LOCATION_DATE_HISTORY,
  );
  return parseStorageResult(storage);
};

const formatDateTime = locationAndDate => {
  const newLocationAndDate = BbkUtils.cloneDeep(locationAndDate);
  const pickUpTime = _.get(newLocationAndDate, 'rentalDate.pickUp.dateTime');
  const dropOffTime = _.get(newLocationAndDate, 'rentalDate.dropOff.dateTime');
  if (pickUpTime && dropOffTime) {
    newLocationAndDate.rentalDate.pickup = dayjs(pickUpTime).format(
      'YYYY-MM-DD HH:mm:ss',
    );
    newLocationAndDate.rentalDate.dropoff = dayjs(dropOffTime).format(
      'YYYY-MM-DD HH:mm:ss',
    );
    newLocationAndDate.rentalDate.pickUp = undefined;
    newLocationAndDate.rentalDate.dropOff = undefined;
  }

  return newLocationAndDate;
};

// 设置最后一次取还车时间地点的缓存数据
// 当前环境的数据，取locationAndDate的最新数据
// 其它环境的数据，取env对应的数据
export const setLocationAndDateHistory = () => {
  try {
    // eslint-disable-next-line global-require
    const locationAndDateStore = require('./State/LocationAndDate').default;

    const locationAndDateHistory: any = {};
    let curLocationAndDateHistory = locationAndDateStore.getState();
    if (
      _.get(curLocationAndDateHistory, 'rentalLocation.pickUp.isDomestic') ===
        undefined ||
      _.get(curLocationAndDateHistory, 'rentalLocation.dropOff.isDomestic') ===
        undefined
    )
      return;

    // 1、设置当前环境
    const curEnv = Utils.getCurrentEnv();
    curLocationAndDateHistory = formatDateTime(curLocationAndDateHistory);
    locationAndDateHistory[curEnv] = curLocationAndDateHistory;
    const isDomestic = curEnv === 'ISD';
    CarLog.LogTraceDev({
      key: LogKeyDev.c_car_dev_set_location_date_history,
      info: {
        data: curLocationAndDateHistory,
        curEnv,
        // curEnv === 'ISD' 与 isDomestic 相等判定为正常
        isSuccess:
          isDomestic ===
          curLocationAndDateHistory?.rentalLocation?.pickUp?.isDomestic,
      },
    });

    // 2、设置其它环境
    const otherEnv = Utils.getOtherEnv();
    // eslint-disable-next-line global-require
    const environmentStore = require('./State/__Environment').default;
    const environmentState = environmentStore.getState();
    let otherLocationAndDateHistory = _.get(
      environmentState[otherEnv],
      'LocationAndDate',
    );
    if (
      otherLocationAndDateHistory &&
      _.get(otherLocationAndDateHistory, 'rentalLocation.pickUp.isDomestic') !==
        undefined &&
      _.get(
        otherLocationAndDateHistory,
        'rentalLocation.dropOff.isDomestic',
      ) !== undefined
    ) {
      otherLocationAndDateHistory = formatDateTime(otherLocationAndDateHistory);
      locationAndDateHistory[otherEnv] = otherLocationAndDateHistory;
    }

    locationAndDateHistory.storageTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

    // 3、拿storage数据，以免覆盖了其它环境的数据（比如: 当其它环境有缓存，而当前store中并没有其它环境的数据时，就会被覆盖）
    const curStorageData = getLocationAndDateStorageSync() || {};
    const newLocationAndDate = {
      ...curStorageData,
      ...locationAndDateHistory,
    };
    const newStorageData: any = {
      ...newLocationAndDate,
      // 是否需要定位
      // 1、Url参数带地址信息参数时不需要定位
      // 2、国内取还车区域信息缓存且不是定位存储信息或者不是默认信息（仅判断取车地址是否来自定位信息）
      isIsdHistoryLocation:
        !!Utils.getRediectCityId() ||
        (!!newLocationAndDate?.ISD?.rentalLocation &&
          !(
            newLocationAndDate?.ISD?.rentalLocation.pickUp?.isFromPosition ||
            newLocationAndDate?.ISD?.rentalLocation.pickUp?.isFromDefault
          )),
    };
    CarStorage.save(
      StorageKey.CAR_LOCATION_DATE_HISTORY,
      JSON.stringify(newStorageData),
    );
  } catch (e) {
    // eslint-disable-next-line no-console
    console.warn(e);
  }
};
