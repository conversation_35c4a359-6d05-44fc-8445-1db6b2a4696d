import { BbkUtils } from '../../../Common/src/Utils';
import { Utils, CarStorage, AppContext } from '../../../Util/Index';
import { ApiResCode, StorageKey } from '../../../Constants/Index';

const { cloneDeep } = BbkUtils;

export const saveIsdPickUpOnDoor = (code, isSelected) => {
  if (
    Utils.isCtripIsd() &&
    code === ApiResCode.FILTER_CODE.StoreService_PickupOnDoor
  ) {
    CarStorage.save(
      StorageKey.FILTER_TO_HOME_COMBIN,
      JSON.stringify([
        {
          code,
          isSelected,
        }
      ]),
    );
  }
};

export const updateFilterItems = (
  { filterItems, setFilterItems, sesameChecked, filterCodeSelects } = {} as any,
) => {
  const newFilterItems = cloneDeep(filterItems);
  // 非融合版防止无用更新
  let shouldChange = AppContext.isHomeCombine || false;
  if (filterCodeSelects?.length) {
    filterCodeSelects.forEach(item => {
      const marketIndex = newFilterItems.findIndex(f => f.code === item?.code);
      if (marketIndex > -1) {
        if (
          item?.code === ApiResCode.FILTER_CODE.StoreService_PickupOnDoor &&
          item?.isSelected !== newFilterItems[marketIndex].isSelected
        ) {
          saveIsdPickUpOnDoor(item?.code, item?.isSelected);
        }
        newFilterItems[marketIndex].isSelected = item?.isSelected;
        shouldChange =
          shouldChange ||
          newFilterItems[marketIndex].isSelected !==
            filterItems[marketIndex].isSelected;
      }
    });
  }

  if (shouldChange) {
    setFilterItems({
      filterItems: newFilterItems,
      [Utils.isCtripIsd() ? 'isIsdSesameChecked' : 'isOsdSesameChecked']:
        sesameChecked,
    });
  }
};

export const getFilterCodeStr = (filterItems: Array<any>) => {
  let filterCodeStr = '';
  if (Array.isArray(filterItems) && filterItems?.length) {
    filterItems.forEach(item => {
      filterCodeStr += item?.code;
    });
  }
  return filterCodeStr;
};
