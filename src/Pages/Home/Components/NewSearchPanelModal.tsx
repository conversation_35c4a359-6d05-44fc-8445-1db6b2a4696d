/* eslint-disable react/destructuring-assignment */
/* eslint-disable react/jsx-props-no-spreading */
import React, { Component } from 'react';
import { Event } from '@ctrip/crn';
import NewSearchPanelModal from '../../../ComponentBusiness/SearchPanelModal';
import dayjs from '../../../Common/src/Dayjs/src';
import Toast from '../../../Common/src/Components/Basic/Toast/src';
import { getSharkValue } from '../../../Common/src/Shark/src/Index';
import { isAndroid } from '../../../Common/src/Utils/src/Utils';
import { CarLog, Utils, CarStorage, CarABTesting } from '../../../Util/Index';
import { ClickKey, EventName, Platform, StorageKey, PickType } from '../../../Constants/Index';
import { isDataChangeEnvironment, getNextLocation } from '../../../State/__Environment/DataUtils';
import { getLocalDayJs } from '../../../Components/Calendar/Method';
import { composeCityArea2RentalLocation } from '../../../State/LocationAndDate/Mappers';
import {
  getTimeInsufficientWarningFunc,
  getTimeWarningFunc,
} from '../../../State/LocationAndDate/Selectors';

const { PICKUP, DROPOFF } = PickType;

interface IPropsType {
  visible: boolean;
  rentalLocation: any;
  rentalDate: any;
  age: string;
  adultSelectNum: number;
  childSelectNum: number;
  backPageName: string;
  pickType: string;
  locationDatePopType: number;
  airPortTransferTip: { isShow: boolean };
  setLocationAndDatePopIsShow: ({ visible }) => void;
  setDateInfo: (data: any) => void;
  setLocationInfo: (data: any) => void;
  setAgeTipPopIsShow: ({ visible }) => void;
  switchSubTab: () => void;
  updateSettingsCombine: (data: any) => void;
  setAgeAdultAndChildNum: (data: any) => void;
  setPickType: (data: any) => void;
  openList: (data?: any) => void;
  showAirPortTransferTip: () => void;
  getStreamRecCityId: (data) => void;
}

interface IStateType {
  rentalLocation: any;
  rentalDate: any;
  age: string;
  adultSelectNum: number;
  childSelectNum: number;
}

class SearchPanelModal extends Component<IPropsType, IStateType> {
  closeTimer = null;

  constructor(props) {
    super(props);
    this.state = {
      rentalLocation: props.rentalLocation,
      rentalDate: props.rentalDate,
      age: props.age,
      adultSelectNum: props.adultSelectNum,
      childSelectNum: props.childSelectNum,
    };
  }

  componentDidMount() {
    this.addEvents();
  }

  componentDidUpdate(prevProps) {
    if (this.validatePropsIsChange(prevProps, this.props)) {
      this.updateState(this.props);
    }
  }

  componentWillUnmount() {
    this.removeEvents();
    clearTimeout(this.closeTimer);
  }

  validatePropsIsChange = (prevProps, curProps) => {
    let isChange = false;
    Object.keys(this.state).forEach(key => {
      if (prevProps[key] !== curProps[key]) {
        isChange = true;
      }
    });
    if (prevProps.visible !== curProps.visible) {
      isChange = true;
    }
    return isChange;
  };

  updateState = nextProps => {
    const newState = {};
    Object.keys(this.state).forEach(key => {
      if (nextProps[key] !== this.state[key]) {
        newState[key] = nextProps[key];
      }
    });
    if (Object.keys(newState).length > 0) {
      this.setState(newState);
    }
  };

  addEvents = () => {
    // 新版城市区域页回调
    Event.addEventListener(
      EventName.newSearchPanelModalLocationChanged,
      data => {
        const { visible } = this.props;
        if (!visible) {
          return;
        }
        const newLocationInfo = composeCityArea2RentalLocation(data);
        this.updateLocationInfo(newLocationInfo);
      },
    );
  };

  removeEvents = () => {
    Event.removeEventListener(EventName.newSearchPanelModalLocationChanged);
  };

  // 取消
  onCancel = () => {
    this.props.setLocationAndDatePopIsShow({ visible: false });
  };

  // 更新搜索地点
  updateLocationInfo = locationInfo => {
    const { pickType } = this.props;
    const { rentalLocation } = this.state;
    const isDataChangeEnv = isDataChangeEnvironment(
      rentalLocation,
      locationInfo,
    );
    const newLocationInfo = getNextLocation({
      isChangeEnv: isDataChangeEnv,
      isShowDropOff: rentalLocation.isShowDropOff,
      pickType,
      rentalLocation: locationInfo,
    });
    if (pickType === PICKUP && !rentalLocation.isShowDropOff) {
      newLocationInfo[DROPOFF] = newLocationInfo[pickType];
    }
    this.setState({
      rentalLocation: { ...rentalLocation, ...newLocationInfo },
    });
  };

  // 驾龄更新回调
  onAgeChange = data => {
    this.setState({
      age: data,
    });
    CarLog.LogCode({ enName: ClickKey.C_LIST_CHANGEINFO_POP_AGE_CONFIRM.KEY });
  };

  onAgeCancel = () => {
    CarLog.LogCode({ enName: ClickKey.C_LIST_CHANGEINFO_POP_AGE_CANCEL.KEY });
  };

  updateRentalDate = (ptime, rtime) => {
    if (!ptime || !rtime) {
      return;
    }
    const { rentalDate } = this.state;
    const newRentalDate = {
      pickUp: {
        dateTime: ptime,
      },
      dropOff: {
        dateTime: rtime,
      },
    };
    this.setState({
      rentalDate: { ...rentalDate, ...newRentalDate },
    });
  };

  // 取车时间过期
  handleTimepassed = (ptime, rtime) => {
    const curMinutes = Math.ceil(parseInt(dayjs().format('mm'), 10) / 15) * 15;
    const timeDiff =
      Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
    const newPTime = dayjs().add(4, 'hours').minute(curMinutes);
    const newRTime = dayjs()
      .add(4, 'hours')
      .minute(curMinutes + timeDiff);
    this.updateRentalDate(newPTime, newRTime);
    Toast.show(getSharkValue('timePassedWarning'), 2);
  };

  // 时间更新回调
  onTimeChange = data => {
    const { ptime, rtime } = data || {};
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else {
      this.updateRentalDate(ptime, rtime);
    }
    CarLog.LogCode({
      enName: ClickKey.C_LIST_CHANGEINFO_POP_CHANGEDATE_CONFIRM.KEY,
    });
  };

  onTimeCancel = () => {
    CarLog.LogCode({
      enName: ClickKey.C_LIST_CHANGEINFO_POP_CHANGEDATE_CANCEL.KEY,
    });
  };

  // 点击查询
  onPressSearch = () => {
    const { rentalLocation, rentalDate, age, adultSelectNum, childSelectNum } =
      this.state;
    const {
      rentalLocation: propsRentalLocation,
      locationDatePopType,
      airPortTransferTip,
      setLocationInfo,
      setDateInfo,
      setAgeAdultAndChildNum,
      openList,
      showAirPortTransferTip,
      setLocationAndDatePopIsShow,
      switchSubTab,
      updateSettingsCombine,
      getStreamRecCityId,
    } = this.props;
    const isFromIsdRanking = locationDatePopType > 0 && Utils.isCtripIsd();
    const ptime = rentalDate.pickUp.dateTime;
    const rtime = rentalDate.dropOff.dateTime;
    // 1、校验时间是否过期
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
      // 2、若有机场提示则展示机场提示
    } else if (airPortTransferTip?.isShow) {
      showAirPortTransferTip();
    } else {
      // 3、将当前state记录的数据同步更新到Reducer中
      const isDataChangeEnv = isDataChangeEnvironment(
        propsRentalLocation,
        rentalLocation.pickUp,
      );
      // 对于融合首页tab拆分的版本当从国内切换到境外，或境外切换到国内时，不需要在当前容器中进行location的更新,只需记录到localStorage中
      if (isDataChangeEnv && Utils.isNewHomeTab()) {
        const newLocation = {
          [Utils.getOtherEnv()]: {
            rentalLocation,
          },
          storageTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        };

        CarStorage.save(
          StorageKey.CAR_HOME_COMBINE_TAB_LOCATION,
          JSON.stringify(newLocation),
        );

        switchSubTab();

        // 触发更新updateSettingsCombine
        updateSettingsCombine({
          floatingNavigationBarConfig: {
            title: '',
            animationHeights: [0, 100],
          },
        });
      } else {
        if (isDataChangeEnv) {
          const appType = Utils.getNewAppType(rentalLocation.isDomestic);
          Event.sendEvent(EventName.ApptypeChange, {
            apptype: appType,
            canUpdateSetting: false,
          });
        }
        setTimeout(() => {
          setLocationInfo(rentalLocation);
          setDateInfo({
            pickup: ptime,
            dropoff: rtime,
          });
          setAgeAdultAndChildNum({
            age,
            adultSelectNum,
            childSelectNum,
          });
          // 如果是境外，需要更新信息流
          if (this.validateIsOsd() && CarABTesting.isInfoFlow()) {
            getStreamRecCityId({
              historyCityId: rentalLocation.pickUp.cid,
            });
          }
        });
      }

      // 4、跳转到列表页(非榜单)
      if (!isFromIsdRanking) {
        const urlConfig = Platform.CAR_CROSS_URL.MARKETLIST;
        const baseUrl = this.validateIsOsd() ? urlConfig.OSD : urlConfig.ISD;
        openList({
          rentalLocation,
          ptime,
          rtime,
          age,
          adultSelectNum,
          childSelectNum,
          baseUrl,
        });
        const delayTime = isAndroid ? 0 : 500;
        this.closeTimer = setTimeout(
          () => setLocationAndDatePopIsShow({ visible: false }),
          delayTime,
        );
      } else {
        setLocationAndDatePopIsShow({ visible: false });
      }
    }
  };

  onPressAgeSelect = () => {
    CarLog.LogCode({ enName: ClickKey.C_LIST_CHANGEINFO_POP_AGE.KEY });
  };

  // 修改异地按钮
  onIsShowDropOffChange = isShowDropOff => {
    const { rentalLocation } = this.state;
    this.setState({
      rentalLocation: {
        ...rentalLocation,
        isShowDropOff,
        dropOff: rentalLocation.pickUp,
      },
    });
  };

  // 点击取车城市
  onPressPickUpDate = () => {
    CarLog.LogCode({
      enName: ClickKey.C_HOME_SECOND_CALENDAR_PICKUP_DATE.KEY,
    });
  };

  onPressDropOffDate = () => {
    CarLog.LogCode({
      enName: ClickKey.C_HOME_SECOND_CALENDAR_DROPOFF_DATE.KEY,
    });
  };

  // 点击驾龄说明弹层
  onPressAgeTip = () => {
    this.props.setAgeTipPopIsShow({ visible: false });
    CarLog.LogCode({ enName: ClickKey.C_LIST_AGETIPPOP_SHOW.KEY });
  };

  onAgeTipClose = () => {
    this.props.setAgeTipPopIsShow({ visible: true });
    CarLog.LogCode({ enName: ClickKey.C_LIST_AGETIPPOP_CLOSE.KEY });
  };

  getCityInfo = cityInfo => {
    return {
      cityId: cityInfo.cid,
      cityName: cityInfo.cname,
      latitude: cityInfo.area.lat,
      longtitude: cityInfo.area.lng,
      locationCode: cityInfo.area.id,
      locationName: cityInfo.isFromPosition
        ? cityInfo.area.name + getSharkValue('locationNearby')
        : cityInfo.area.name,
      locationType: cityInfo.area.type,
    };
  };

  getSearchPanelModalProps = () => {
    const { rentalLocation, rentalDate, age, adultSelectNum, childSelectNum } =
      this.state;
    const { visible } = this.props;
    return {
      visible,
      pcity: this.getCityInfo(rentalLocation.pickUp),
      rcity: this.getCityInfo(rentalLocation.dropOff),
      showDropoff: rentalLocation.isShowDropOff,
      ptime: rentalDate.pickUp.dateTime,
      rtime: rentalDate.dropOff.dateTime,
      age,
      adultSelectNum,
      childSelectNum,
      useCRNModal: true,
      maxmonths: Utils.getRentalMaxMonth(),
      options: Utils.getRentalPickerOption(),
    };
  };

  validateIsOsd = () => {
    return this.state.rentalLocation.pickUp.isDomestic === false;
  };

  getWarnings = () => {
    const { rentalDate } = this.state;
    const ptime = rentalDate.pickUp.dateTime;
    const rtime = rentalDate.dropOff.dateTime;
    const timeWarning = getTimeWarningFunc(ptime, rtime);
    const insufficientTimeWarning = getTimeInsufficientWarningFunc(ptime);
    return { timeWarning, insufficientTimeWarning };
  };

  render() {
    const isOsd = this.validateIsOsd();
    const { timeWarning, insufficientTimeWarning } = this.getWarnings();
    const { setPickType } = this.props;
    return (
      <NewSearchPanelModal
        {...this.getSearchPanelModalProps()}
        onCancel={this.onCancel}
        onPressPickUpDate={this.onPressPickUpDate}
        onPressDropOffDate={this.onPressDropOffDate}
        onAgeChange={this.onAgeChange}
        onAgeCancel={this.onAgeCancel}
        onTimeChange={this.onTimeChange}
        onPressSearch={this.onPressSearch}
        onPressAgeSelect={this.onPressAgeSelect}
        onIsShowDropOffChange={this.onIsShowDropOffChange}
        onPressAgeTip={this.onPressAgeTip}
        onAgeTipClose={this.onAgeTipClose}
        isOsd={isOsd}
        setPickType={setPickType}
        timeWarning={!isOsd && timeWarning}
        insufficientTimeWarning={insufficientTimeWarning}
      />
    );
  }
}

export default SearchPanelModal;
