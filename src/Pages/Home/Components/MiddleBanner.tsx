import React, { memo } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { URL } from '@ctrip/crn';
import { BbkUtils } from '../../../Common/src/Utils';
import BbkTouchable from '../../../Common/src/Components/Basic/Touchable/src';
import Text from '../../../Common/src/Components/Basic/Text';
import { color, font, layout, space } from '../../../Common/src/Tokens';
import { Utils, CarLog, CarABTesting } from '../../../Util/Index';
import { ClickKey, UITestID } from '../../../Constants/Index';
import CardWithTitle from './CardWithTitle';

const { getPixel, uuid } = BbkUtils;
const debounceTime = 300;

const styles = StyleSheet.create({
  largeContainer: {
    borderRadius: getPixel(8),
    flex: 1,
  },
  title: {
    fontSize: getPixel(30),
    lineHeight: getPixel(38),
  },
  bimageStyle: {
    flex: 1,
    height: getPixel(200),
  },
  freeDeposit: {
    height: getPixel(343),
  },
  isdFirstlineRight: {
    flex: 1,
    marginLeft: getPixel(16),
  },
  sameSizeImage: {
    height: getPixel(164),
  },
  LineWrap: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: getPixel(24),
  },
  isdSecondLineItem: {
    flex: 1,
    alignItems: 'center',
  },
  isdSecondLineIcon: {
    width: getPixel(110),
    height: getPixel(110),
  },
  isdSecondLineTex: {
    color: color.blueGrayBase,
    ...font.title3MediumStyle,
    marginTop: getPixel(10),
  },
  rowFlexDirection: {
    flexDirection: 'row',
  },
  mtop32: {
    marginTop: getPixel(32),
  },
  osdSecondLine: {
    flexDirection: 'row',
    marginTop: space.spaceXXL,
    paddingHorizontal: space.spaceXL,
    height: getPixel(160),
  },
  firstLine: {
    flex: 1,
    height: getPixel(200),
    borderRadius: getPixel(8),
  },
  space: {
    paddingBottom: getPixel(24),
    backgroundColor: color.white,
  },
  titleWrap: {
    paddingBottom: 0,
    position: 'relative',
    zIndex: 1,
    marginTop: -getPixel(25),
  },
});

const MiddleBanner: React.FC<any> = memo(
  ({ style, IsdRentalData = {}, gotoTravelPage }) => {
    const bannerPress = item => {
      CarLog.LogCode({
        enName: ClickKey.C_HOME_MIDDLE_BANNER_PRESS.KEY,
        name: `${ClickKey.C_HOME_MIDDLE_BANNER_PRESS.NAME}_${item.title || ''}`,
      });
      if (item.jumpUrl) {
        if (item.jumpUrl.indexOf('http') > -1) {
          URL.openURL(`${item.jumpUrl}`);
        } else {
          URL.openURL(`${Utils.getHost()}${item.jumpUrl}`);
        }
      } else if (item.jumpTravelPage) {
        gotoTravelPage();
      }
    };
    const IsdFirstLineA = IsdRentalData.firstLine;
    const IsdFirstLineB = IsdRentalData.firstLineB;
    return (
      <View style={[styles.space, style]}>
        {Utils.isCtripIsd() && (
          <CardWithTitle
            title={IsdFirstLineB.title}
            style={styles.titleWrap}
            noSpace={true}
          />
        )}
        <View style={styles.LineWrap}>
          {Utils.isCtripIsd() &&
            CarABTesting.isCreditRent() &&
            IsdFirstLineB?.left &&
            IsdFirstLineB?.right && (
              <View style={styles.rowFlexDirection}>
                <BbkTouchable
                  onPress={() => bannerPress(IsdFirstLineB.left)}
                  debounce={true}
                  debounceTime={debounceTime}
                  style={layout.flex1}
                >
                  <Image
                    source={{ uri: IsdFirstLineB.left.imageUrl || '' }}
                    resizeMode="stretch"
                    style={styles.freeDeposit}
                  />
                </BbkTouchable>
                <View style={styles.isdFirstlineRight}>
                  {IsdFirstLineB.right.length > 0 &&
                    IsdFirstLineB.right.map((item, index) => (
                      <View
                        key={uuid()}
                        style={[
                          layout.flexRow,
                          index !== 0 && { marginTop: getPixel(15) },
                        ]}
                      >
                        {item.map((v, n) => (
                          <BbkTouchable
                            key={uuid()}
                            onPress={() => bannerPress(v)}
                            debounce={true}
                            debounceTime={debounceTime}
                            style={[
                              n !== 0 && { marginLeft: getPixel(15) },
                              layout.flex1,
                            ]}
                            testID={
                              UITestID.car_testid_page_home_banner_first_line_right_B
                            }
                          >
                            <Image
                              source={{ uri: v.imageUrl || '' }}
                              resizeMode="stretch"
                              style={styles.sameSizeImage}
                            />
                          </BbkTouchable>
                        ))}
                      </View>
                    ))}
                </View>
              </View>
            )}
          {Utils.isCtripIsd() &&
            IsdFirstLineA &&
            !CarABTesting.isCreditRent() &&
            IsdFirstLineA.map((item, index) => (
              <BbkTouchable
                onPress={() => bannerPress(item)}
                key={item.imageUrl || ''}
                debounce={true}
                debounceTime={debounceTime}
                style={[
                  { flex: 1 },
                  index !== 0 && { marginLeft: getPixel(10) },
                ]}
              >
                <Image
                  source={{ uri: item.imageUrl || '' }}
                  resizeMode="stretch"
                  style={styles.bimageStyle}
                />
              </BbkTouchable>
            ))}
        </View>
        <View style={styles.LineWrap}>
          {Utils.isCtripIsd() &&
            IsdRentalData?.secondLine?.map((item, index) => (
              <BbkTouchable
                key={item.title || ''}
                onPress={() => bannerPress(item)}
                debounce={true}
                debounceTime={debounceTime}
                style={{ flex: 1 }}
              >
                <View
                  style={[
                    styles.isdSecondLineItem,
                    index === 0 && {
                      alignSelf: 'flex-start',
                      marginLeft: getPixel(41),
                    },
                    index === IsdRentalData.secondLine.length - 1 && {
                      alignSelf: 'flex-end',
                      marginRight: getPixel(55),
                    },
                  ]}
                >
                  <Image
                    source={{ uri: item.imageUrl || '' }}
                    style={styles.isdSecondLineIcon}
                    resizeMode="contain"
                  />
                  <Text style={styles.isdSecondLineTex}>{item.title}</Text>
                </View>
              </BbkTouchable>
            ))}
        </View>
      </View>
    );
  },
);
export default MiddleBanner;
