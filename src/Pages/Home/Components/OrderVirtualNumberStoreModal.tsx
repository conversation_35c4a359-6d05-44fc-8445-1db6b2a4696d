import React, { useEffect, useMemo, useState } from 'react';
import { PhoneNumberRole, PhoneNumber } from '../../../Common/src/Logic/src/Order/Types/QueryOrderNumberType';
import { icon } from '../../../Common/src/Tokens';
import VirtualNumberStoreModal, { ContactBlockTypes } from '../../VendorList/Components/VirtualNumberStoreModal';
import { CustomerPhoneModalProps } from './CustomerPhoneModal';
import { IStoreAttendantType } from '../Types';
import Texts from '../Texts';
import { ClickKey } from '../../../Constants/Index';
import { CustomerPhoneModalType, PhoneModalFromWhere } from '../../../Constants/OrderDetail';
import { setContactData } from '../../../State/OrderDetail/Mappers';
import { useMemoizedFn } from '../../../Common/src/Utils/src/Utils';

const noop = () => {};
interface PhoneNumberDataType {
  phoneNumberList: PhoneNumber[];
  fetchCompleted: boolean;
}
interface IPropsType extends CustomerPhoneModalProps {
  visible: boolean;
  onClose: () => void;
  queryOrderNumber: (data: { orderId: number | string }) => void;
  phoneNumberData: PhoneNumberDataType;
  fetchCallBack: (flag) => void;
  phoneModalFromWhere: PhoneModalFromWhere;
  clearOrderNumber: (orderId: string) => void;
}
const OrderVirtualNumberStoreModal: React.FC<IPropsType> = props => {
  const {
    visible,
    onClose,
    queryOrderNumber,
    phoneNumberData,
    orderId,
    orderStatus,
    storeAttendant,
    vendorImUrl,
    tourImJumpUrl,
    fetchCallBack = noop,
    type,
    phoneModalFromWhere,
    clearOrderNumber,
  } = props;
  const { phoneNumberList, fetchCompleted } = phoneNumberData || {};
  const [modalVisible, setModalVisible] = useState(false);
  useEffect(() => {
    if (visible) {
      queryOrderNumber({ orderId });
    }
  }, [orderId, orderStatus, queryOrderNumber, visible]);

  useEffect(() => {
    if (visible && fetchCompleted && !modalVisible) {
      if (phoneNumberList?.length > 0) {
        fetchCallBack(false);
        setModalVisible(visible);
      } else {
        fetchCallBack(true);
        clearOrderNumber(orderId);
      }
    }
    if (!visible && fetchCompleted) {
      setModalVisible(false);
    }
  }, [
    visible,
    phoneNumberList,
    fetchCompleted,
    fetchCallBack,
    modalVisible,
    clearOrderNumber,
    orderId,
  ]);
  const contactData = useMemo(() => {
    const contacts: ContactBlockTypes[] = setContactData(
      phoneNumberData,
      type,
      storeAttendant,
      icon,
      IStoreAttendantType,
      PhoneNumberRole,
      vendorImUrl,
      phoneModalFromWhere,
      ClickKey,
      PhoneModalFromWhere,
      tourImJumpUrl,
    );
    return contacts;
  }, [
    phoneModalFromWhere,
    tourImJumpUrl,
    type,
    vendorImUrl,
    phoneNumberData,
    storeAttendant,
  ]);
  const onCloseModal = useMemoizedFn(() => {
    clearOrderNumber(orderId);
    setModalVisible(false);
    onClose();
  });
  const title =
    type === CustomerPhoneModalType.Customer
      ? Texts.contactCustomerService
      : '';
  if (!modalVisible) return null;
  return (
    <VirtualNumberStoreModal
      contactData={contactData}
      visible={modalVisible}
      onClose={onCloseModal}
      title={title}
      useBbkModal={true}
    />
  );
};

export default OrderVirtualNumberStoreModal;
