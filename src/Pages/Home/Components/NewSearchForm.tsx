/* eslint-disable @typescript-eslint/no-unused-vars */

/* eslint-disable react/destructuring-assignment */

/* eslint-disable react/require-default-props */

/* eslint-disable global-require */
import React, { Component } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { getSharkValue } from '../../../Common/src/Shark/src/Index';
import dayjs from '../../../Common/src/Dayjs/src';
import { BbkUtils } from '../../../Common/src/Utils';
import { color } from '../../../Common/src/Tokens';
import { NewSearchPanel, ILocation } from '../../../ComponentBusiness/NewSearchPanel/index';
import { getLocalDayJs } from '../../../Components/Calendar/Method';
import { PickType, PageName, ClickKey, ImageUrl } from '../../../Constants/Index';
import { Utils, CarABTesting, AppContext, Channel, CarLog } from '../../../Util/Index';
import { saveIsdPickUpOnDoor } from '../Logic/Filter';
import { setFromPage } from '../../../Global/Cache/Index';
import { ItravelItems } from './ItineraryCard/Type';
import { Enums } from '../../../ComponentBusiness/Common/index';
import EnsureTip from './EnsureTip';

const { cloneDeep, isIos, vw, getPixel, fixIOSOffsetBottom } = BbkUtils;
// interface IToast {
//   show(txt: String, time?: number): Function;
// }

// const LazyModule = {
//   toast: null,

//   get Toast(): IToast {
//     if (!LazyModule.toast) {
//       LazyModule.toast =
//         require('@ctrip/rn_com_car/dist/src/Components/Basic/Toast/src').default;
//     }
//     return LazyModule.toast;
//   },
// };

const styles = StyleSheet.create({
  formWrap: {
    // borderTopLeftRadius: 0,
    // borderTopRightRadius: 0,
    // borderBottomLeftRadius: 8,
    // borderBottomRightRadius: 8,
    // shadowOffset: { width: 0, height: 8 },
    // shadowRadius: 20,
    // // @qunar 阴影处理
    // // shadowColor: color.darkGrayBorder,
    // shadowColor: color.black,
    // shadowOpacity: 0.08,
    // elevation: 12,
    // overflow: 'hidden',
    // marginHorizontal: 12,
  },
  androidFormWrapper: {
    // borderTopLeftRadius: 0,
    // borderTopRightRadius: 0,
    // borderBottomLeftRadius: 8,
    // borderBottomRightRadius: 8,
    // marginHorizontal: 12,
  },
  searchPanel: {
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  leftImg: {
    position: 'absolute',
    width: 12,
    borderWidth: 0,
    top: 0,
    left: -12,
  },
  rightImg: {
    position: 'absolute',
    width: 12,
    borderWidth: 0,
    top: 0,
    right: -12,
  },
  bottomImg: {
    position: 'absolute',
    width: vw(100),
    height: getPixel(40),
    borderWidth: 0,
    bottom: -getPixel(24),
    left: -12,
    zIndex: -1,
  },
  EnsureTipWrapper: {
    backgroundColor: color.newEnsureTipBg,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  }
});

interface INewSearchForm {
  ptime: Date;
  rtime: Date;
  pcity: ILocation;
  rcity: ILocation;
  rentalLocation: any;
  isShowDropOff: boolean;
  adultSelectNum: number;
  childSelectNum: number;
  age: string;
  filterItems: Array<any>;
  isLazyLoad: boolean;
  airPortTransferTip: { isShow: boolean };
  isShowItineraryCard: boolean;
  hasLocationPermission: boolean;
  selectedItineraryCard: ItravelItems;
  travelItems: ItravelItems[];
  searchBtnLottieJson: string;
  searchBtnBg: string;
  isPickupStation: boolean;
  isDropOffStation: boolean;
  fromurl?: string;
  timeWarning: string;
  insufficientTimeWarning: string;
  hasEnsureTip: boolean;
  ensureTipImageUrl?: string;
  ensureTipJumpUrl?: string;
  setDateInfo: (data: any) => void;
  setLocationInfo: (data: any) => void;
  setPickType: (data: any) => void;
  setFilterItems: (data: any) => void;
  preListPageFetch: (data) => void;
  showAirPortTransferTip: () => void;
  onPressPosition: () => void;
  openList: (homeSelectedFilters: any) => void;
  setAge: (data: { age: number }) => void;
  setAgeAdultAndChildNum: (data: any) => void;
  updateSelectedItineraryCard: (data: any) => void;
  fetchItineraryCardInfo: () => void;
}

interface SearchFormStateType {
  shadowHeight: number;
}

const { PICKUP, DROPOFF } = PickType;

export default class NewSearchForm extends Component<
  INewSearchForm,
  SearchFormStateType
> {
  pageInstance: any;

  constructor(props) {
    super(props);
    this.state = {
      shadowHeight: 0,
    };
    this.pageInstance = AppContext.PageInstance;
    AppContext.setHomeSearchFormRef(this);
  }

  componentDidMount() {
    const delayTime = isIos ? 100 : 200;
    setTimeout(() => {
      this.lazyDidMount();
    }, delayTime);
  }

  // eslint-disable-next-line react/sort-comp
  lazyDidMount() {
    const { fetchItineraryCardInfo } = this.props;
    fetchItineraryCardInfo();
  }

  // 取车时间过期
  handleTimepassed = (ptime, rtime) => {
    const curMinutes = Math.ceil(parseInt(dayjs().format('mm'), 10) / 15) * 15;
    const timeDiff =
      Math.ceil(dayjs(rtime).diff(dayjs(ptime), 'minute') / 15) * 15;
    const newRentalDate = {
      pickup: dayjs().add(4, 'hours').minute(curMinutes),
      dropoff: dayjs()
        .add(4, 'hours')
        .minute(curMinutes + timeDiff),
    };
    this.props.setDateInfo(newRentalDate);
    // LazyModule.Toast?.show(getSharkValue('timePassedWarning'), 2);
  };

  // 时间更新回调
  onTimeChange = data => {
    const { ptime } = data;
    const { rtime } = data;
    const newRentalDate = { pickup: ptime, dropoff: rtime };
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else {
      this.props.setDateInfo(newRentalDate);
    }
  };

  // 异地还车按钮回调
  onIsShowDropOffChange = isShowDropOff => {
    const { rentalLocation, setLocationInfo } = this.props;
    setLocationInfo({
      isShowDropOff,
      dropOff: !isShowDropOff ? rentalLocation.pickUp : rentalLocation.dropOff,
      notDisPatchPreFetch: true,
    });
    CarLog.LogCode({
      enName: ClickKey.C_HOME_CHANGEINFO_POP_SWITCH_DROPOFF.KEY,
      isShowDropOff,
    });
  };

  onPressCity = (isPickUp: boolean) => {
    CarLog.LogCode({
      enName: isPickUp
        ? ClickKey.C_HOME_CHANGEINFO_POP_PICKUP_CITY.KEY
        : ClickKey.C_HOME_CHANGEINFO_POP_DROPOFF_CITY.KEY,
    });
  };

  onPressLocation = (isPickUp: boolean) => {
    CarLog.LogCode({
      enName: isPickUp
        ? ClickKey.C_HOME_CHANGEINFO_POP_PICKUP_LOCATION.KEY
        : ClickKey.C_HOME_CHANGEINFO_POP_DROPOFF_LOCATION.KEY,
    });
  };

  // 点击取车时间
  onPressPickUpDate = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_CALENDAR_PICKUP_DATE.KEY });
  };

  // 点击还车时间
  onPressDropOffDate = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_CALENDAR_DROPOFF_DATE.KEY });
  };

  // 点击租期
  onPressRentalTime = () => {
    if (Utils.isCtripIsd()) {
      CarLog.LogCode({ enName: ClickKey.C_HOMEISD_SEARCHFORM_RENTAL_TIME.KEY });
    } else {
      CarLog.LogCode({ enName: ClickKey.C_HOMEOSD_SEARCHFORM_RENTAL_TIME.KEY });
    }
  };

  onPressFilter = item => {
    const { filterItems, setFilterItems, preListPageFetch } = this.props;
    const newFilterItems = cloneDeep(filterItems);
    const index = filterItems?.findIndex(m => m?.code === item.code);
    newFilterItems[index].isSelected = !item.isSelected;
    if (
      newFilterItems &&
      newFilterItems[index] &&
      Utils.isObject(newFilterItems[index])
    ) {
      newFilterItems[index].isSelected = !item?.isSelected;
    }

    setFilterItems({ filterItems: newFilterItems });
    saveIsdPickUpOnDoor(item.code, newFilterItems[index].isSelected);

    // 预请求列表页
    if (CarABTesting.isListInPage()) {
      const filters = [].concat(
        newFilterItems.filter(newItem => newItem.isSelected).map(v => v.code),
      );
      preListPageFetch({ filters });
    }
  };

  getHomeSelectedFilters = () => {
    const { filterItems } = this.props;
    return filterItems.filter(item => item.isSelected).map(v => v.code);
  };

  goList = () => {
    const { fromurl } = this.props;

    AppContext.setRouterListLoader({
      groupCode: '',
      filters: this.getHomeSelectedFilters(),
    });

    setFromPage(this.pageInstance.getPageId());
    this.pageInstance.push(Channel.getPageId().List.EN, {
      fromurl: fromurl || PageName.Home,
    });
  };

  onPressSearch = () => {
    const {
      ptime,
      rtime,
      isLazyLoad,
      isPickupStation,
      isDropOffStation,
      openList,
      airPortTransferTip,
      showAirPortTransferTip,
      adultSelectNum,
      childSelectNum,
      age,
    } = this.props;

    CarLog.LogCode({
      enName: ClickKey.C_HOME_CHANGEINFO_POP_SEARCH.KEY,
      info: {
        ispickupStation: isPickupStation,
        isdropoffStation: isDropOffStation,
        adultsNumber: adultSelectNum,
        childrenNumber: childSelectNum,
        driverAge: age,
      },
    });

    AppContext.setListCacheInterval({
      homeReady: new Date(),
      listCacheBuild: new Date(),
    });
    if (dayjs(ptime).diff(getLocalDayJs(), 'seconds') < 0) {
      this.handleTimepassed(ptime, rtime);
    } else if (airPortTransferTip?.isShow) {
      showAirPortTransferTip();
    } else if (openList) {
      openList(this.getHomeSelectedFilters());
    } else if (isLazyLoad) {
      this.goList();
    }
  };

  onAgeChange = data => {
    this.props.setAge({ age: data });
    CarLog.LogCode({ enName: ClickKey.C_HOME_CHANGEINFO_POP_AGE_CONFIRM.KEY });
  };

  onAgeCancel = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_CHANGEINFO_POP_AGE_CANCEL.KEY });
  };

  onNumberConfirm = data => {
    this.props.setAgeAdultAndChildNum(data);
    CarLog.LogCode({
      enName: ClickKey.C_NUM_POP_CONFIRM.KEY,
      info: {
        adultsNumber: data?.adultSelectNum,
        childrenNumber: data?.childSelectNum,
      },
    });
  };

  onNumberCancel = () => {
    CarLog.LogCode({ enName: ClickKey.C_NUM_POP_CANCEL.KEY });
  };

  onPressNumberSelect = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_SEARCH_NUM.KEY });
  };

  onPressAgeSelect = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_CHANGEINFO_POP_AGE.KEY });
  };

  onPressAgeTip = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_AGETIPPOP_SHOW.KEY });
  };

  onAgeTipClose = () => {
    CarLog.LogCode({ enName: ClickKey.C_HOME_AGETIPPOP_CLOSE.KEY });
  };

  handleLayout = e => {
    const curHeight = e.nativeEvent.layout.height;
    this.setState({
      shadowHeight: curHeight - getPixel(16),
    });
  };

  render() {
    const {
      ptime,
      rtime,
      pcity,
      rcity,
      isShowDropOff,
      adultSelectNum,
      childSelectNum,
      age,
      timeWarning,
      insufficientTimeWarning,
      filterItems,
      hasLocationPermission,
      isShowItineraryCard,
      selectedItineraryCard,
      travelItems,
      searchBtnLottieJson,
      searchBtnBg,
      onPressPosition,
      updateSelectedItineraryCard,
      setDateInfo,
      setLocationInfo,
      setPickType,
      hasEnsureTip,
      ensureTipImageUrl,
      ensureTipJumpUrl,
      showDatePicker,
      maxmonths,
      options,
      showDetailDiff,
      checkPositionPermission,
    } = this.props;
    const { shadowHeight } = this.state;

    let marginTop = {};
    if (AppContext.isHomeCombine) {
      marginTop = { marginTop: isIos ? getPixel(-fixIOSOffsetBottom(0)) : getPixel(-36) }
    } else {
      marginTop = { marginTop: getPixel(0) }
    }
    return (
      <View
        style={[isIos ? styles.formWrap : styles.androidFormWrapper, marginTop]}
        onLayout={this.handleLayout}
      >
        <NewSearchPanel
          style={styles.searchPanel}
          ptime={ptime}
          rtime={rtime}
          pcity={pcity}
          rcity={rcity}
          showDropoff={isShowDropOff}
          age={age}
          isShowFilterItems={Utils.isCtripIsd()}
          filterItems={filterItems}
          isShowLocationBar={Utils.isCtripIsd()}
          hasLocationPermission={hasLocationPermission}
          searchBtnLottieJson={searchBtnLottieJson}
          searchBtnBg={searchBtnBg}
          isShowItineraryCard={isShowItineraryCard}
          selectedItineraryCard={selectedItineraryCard}
          travelItems={travelItems}
          searchPanelButtonType={Enums.SearchPanelButtonEnum.search}
          timeWarning={timeWarning}
          showDatePicker={showDatePicker}
          maxmonths={maxmonths}
          options={options}
          showDetailDiff={showDetailDiff}
          insufficientTimeWarning={insufficientTimeWarning}
          onTimeChange={this.onTimeChange}
          onIsShowDropOffChange={this.onIsShowDropOffChange}
          onPressCity={this.onPressCity}
          onPressLocation={this.onPressLocation}
          onPressFilter={this.onPressFilter}
          onPressSearch={this.onPressSearch}
          onPressPosition={onPressPosition}
          onAgeChange={this.onAgeChange}
          onAgeCancel={this.onAgeCancel}
          updateSelectedItineraryCard={updateSelectedItineraryCard}
          setDateInfo={setDateInfo}
          setLocationInfo={setLocationInfo}
          onPressPickUpDate={this.onPressPickUpDate}
          onPressDropOffDate={this.onPressDropOffDate}
          onPressRentalTime={this.onPressRentalTime}
          onPressNumberSelect={this.onPressNumberSelect}
          onPressAgeSelect={this.onPressAgeSelect}
          onPressAgeTip={this.onPressAgeTip}
          onAgeTipClose={this.onAgeTipClose}
          setPickType={setPickType}
          isShowBusinessLicense={Utils.isCtripIsd()}
          checkPositionPermission={checkPositionPermission}
        />
        {!isIos && (
          <>
            <Image
              source={{
                uri: `${ImageUrl.componentImagePath}Home/homeShadowLeft.png`,
              }}
              style={[styles.leftImg, { height: shadowHeight }]}
            />
            <Image
              source={{
                uri: `${ImageUrl.componentImagePath}Home/homeShadowRight.png`,
              }}
              style={[styles.rightImg, { height: shadowHeight }]}
            />
            <Image
              source={{
                uri: `${ImageUrl.componentImagePath}Home/homeShadowBottom.png`,
              }}
              style={styles.bottomImg}
            />
          </>
        )}
        {
          Utils.isCtripIsd() && hasEnsureTip && <View style={styles.EnsureTipWrapper}>
            <EnsureTip
            imageUrl={ensureTipImageUrl}
            jumpUrl={ensureTipJumpUrl}
          />
          </View>
        }
      </View>
    );
  }
}
