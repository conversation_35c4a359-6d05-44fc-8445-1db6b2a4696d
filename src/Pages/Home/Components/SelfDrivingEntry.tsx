import React, { useCallback } from 'react';
import { StyleSheet, Image } from 'react-native';
import { URL } from '@ctrip/crn';
import { BbkUtils } from '../../../Common/src/Utils';
import { color } from '../../../Common/src/Tokens';
import BbkTouchable from '../../../Common/src/Components/Basic/Touchable/src';
import { Utils } from '../../../Util/Index';

const { getPixel } = BbkUtils;

type ISelfDrivingCity = {
  cityId: number;
  image: string;
  url: string;
};

interface ISelfDrivingEntry {
  cityId: number;
  data: Array<ISelfDrivingCity>;
}

const styles = StyleSheet.create({
  wrapper: {
    flex: 1,
    backgroundColor: color.white,
    borderRadius: getPixel(12),
    marginTop: getPixel(32),
    marginHorizontal: getPixel(24),
  },
  image: {
    width: getPixel(702),
    height: getPixel(116),
  },
});

const SelfDrivingEntry: React.FC<ISelfDrivingEntry> = ({
  cityId,
  data = [],
}) => {
  const currentData = data.find(v => v && v.cityId === cityId);
  const goH5 = useCallback(() => {
    URL.openURL(currentData.url);
  }, [currentData]);

  return currentData && currentData.image && currentData.url ? (
    <BbkTouchable style={styles.wrapper} debounce={true} onPress={goH5}>
      <Image
        source={{ uri: Utils.compatImgUrlWithWebp(currentData.image) }}
        resizeMode="contain"
        style={styles.image}
      />
    </BbkTouchable>
  ) : null;
};

export default SelfDrivingEntry;
