import React, { Fragment, PureComponent } from 'react';
import { NativeModules, View, StyleSheet, Image, ViewStyle } from 'react-native';
import { URL, LinearGradient } from '@ctrip/crn';
import dayjs from '../../../Common/src/Dayjs/src';
import { BbkUtils, DateFormatter } from '../../../Common/src/Utils';
import { getSharkValue } from '../../../Common/src/Shark/src/Index';
import BbkTouchable from '../../../Common/src/Components/Basic/Touchable/src';
import Text from '../../../Common/src/Components/Basic/Text';
import {
  font,
  layout,
  color,
  setOpacity,
  icon,
} from '../../../Common/src/Tokens';
import { selector, isAndroid } from '../../../Common/src/Utils/src/Utils';
import { CarLog, AppContext, Channel } from '../../../Util/Index';
import { ClickKey, ImageUrl, UITestID, Platform } from '../../../Constants/Index';
import {
  CustomerPhoneModalType,
  GuideTabType,
  DirectOpen,
  OrderStatusCtrip,
} from '../../../Constants/OrderDetail';
import ExposureKey from '../../../Constants/ExposureKey';
import { getGuidePageParam } from '../../../State/Home/Mapper';
import { getCombineFlex } from '../StyleUtil';
import Texts from '../Texts';
// eslint-disable-next-line import/no-named-as-default
import SCENES from '../../../Constants/Guide';
import { PreloadData } from '../../../Util/PreloadNextContainer';

const { getPixel, autoProtocol, vw } = BbkUtils;
const debounceTime = 300;

const styles = StyleSheet.create({
  // @qunar change
  wrapper: {
    backgroundColor: color.white,
    borderRadius: getPixel(16),
    marginTop: getPixel(32),
    marginBottom: getPixel(40),
    marginHorizontal: getPixel(24),
    paddingBottom: getPixel(16),
    position: 'relative',
    shadowRadius: getPixel(10),
    shadowOffset: {
      width: 0,
      height: getPixel(4),
    },
    elevation: 4,
    shadowOpacity: 0.08,
    shadowColor: color.cardShadowColor,
    zIndex: 101,
  },
  hasEnsureTip: {
    marginTop: 0,
  },
  carInfo: {
    marginHorizontal: getPixel(30),
    paddingTop: getPixel(16),
    ...layout.betweenHorizontal,
  },
  carImg: {
    width: getPixel(132),
    height: getPixel(88),
    marginRight: getPixel(10),
  },
  carName: {
    flex: 1,
    ...font.selfHelpTitle,
    flexWrap: 'wrap',
  },
  payTickText: {
    color: color.fontSecondary,
    ...font.caption1LightStyle,
  },
  headerTipWrap: {
    borderTopLeftRadius: getPixel(100),
    borderBottomLeftRadius: getPixel(100),
    borderTopRightRadius: getPixel(isAndroid ? 100 : 24),
    borderBottomRightRadius: 0,
    height: getPixel(48),
    ...layout.startHorizontal,
    alignSelf: 'flex-end',
    paddingHorizontal: getPixel(32),
    paddingVertical: getPixel(6),
    position: 'absolute',
    top: getPixel(-8),
  },
  headerTipWrapShadow: {
    shadowRadius: getPixel(2),
    shadowOffset: {
      width: 0,
      height: getPixel(4),
    },
    // @qunar change 没有阴影
    shadowOpacity: 0.1,
    elevation: 2,
  },
  headerTipWrapText: {
    color: color.white,
    ...font.selfHelpTitle,
  },
  tipGapLine: {
    width: getPixel(1),
    height: getPixel(24),
    backgroundColor: color.fontSubLight,
    marginHorizontal: getPixel(12),
  },
  storeInfoBg: {
    width: vw(100) - getPixel(88),
    height: getPixel(186),
    position: 'absolute',
  },
  storeInfoWrap: {
    width: vw(100) - getPixel(88),
    height: getPixel(186),
    marginHorizontal: getPixel(20),
    marginBottom: getPixel(20),
    flexDirection: 'row',
    borderRadius: getPixel(16),
    overflow: 'hidden',
  },
  infoWarp: {
    paddingLeft: getPixel(24),
    width: vw(100) - getPixel(244),
    height: getPixel(186),
    justifyContent: 'center',
  },
  mapIconWrap: {
    width: getPixel(156),
    height: getPixel(186),
    justifyContent: 'center',
    alignItems: 'center',
  },
  mapIcon: {
    width: getPixel(92),
    height: getPixel(92),
  },
  actionTimeTextWrap: {
    ...layout.flexRow,
    alignItems: 'center',
    marginBottom: getPixel(12),
  },
  actionTime: {
    // @qunar change
    color: color.topRank,
    ...font.title3BoldStyle,
    marginLeft: getPixel(12),
  },
  actionWrap: {
    ...layout.flexCenter,
  },
  actionBg: {
    width: getPixel(87),
    height: getPixel(36),
  },
  actionText: {
    color: color.white,
    ...font.caption2BoldStyle,
    position: 'absolute',
    left: getPixel(16),
    backgroundColor: color.topRank,
  },
  descWarp: {
    ...layout.startHorizontal,
  },
  posTitle: {
    color: color.fontSecondary,
    ...font.caption1LightStyle,
    marginRight: getPixel(16),
    alignSelf: 'flex-start',
  },
  desc: {
    color: color.fontSecondary,
    ...font.caption1LightStyle,
    width: getPixel(430),
  },
  buttonsWrap: {
    ...layout.flexRow,
  },
  button: {
    ...layout.flexCenter,
    flex: 1,
    marginHorizontal: getPixel(16),
  },
  btnText: {
    ...font.labelXLStyle,
  },
  btnIcon: {
    fontSize: getPixel(32),
  },
  buttonLine: {
    width: getPixel(1),
    height: getPixel(35),
    backgroundColor: color.fontSubLight,
  },
  payColor: {
    color: color.refundTotalAmount,
  },
  defaultIcon: {
    color: color.blackBase,
  },
  tickWrap: {
    ...layout.betweenHorizontal,
    width: getPixel(170),
  },
  tipWrap: {
    position: 'absolute',
    right: getPixel(20),
    bottom: getPixel(84),
    backgroundColor: setOpacity(color.black, 0.6),
    paddingHorizontal: getPixel(24),
    paddingVertical: getPixel(8),
    borderRadius: getPixel(32),
    borderWidth: getPixel(1),
    borderColor: color.white,
    shadowColor: color.black,
    shadowOffset: {
      width: 0,
      height: getPixel(2),
    },
    shadowRadius: getPixel(2),
    shadowOpacity: 0.3,
    ...layout.flexRow,
  },
  tipText: {
    color: color.white,
    marginRight: getPixel(12),
    ...font.labelLLightStyle,
  },
  tipIcon: {
    color: color.white,
    width: getPixel(24),
    height: getPixel(24),
  },
  upgradeWrap: {
    marginTop: getPixel(-12),
    marginHorizontal: getPixel(20),
    backgroundColor: color.orderCardUpgradeBg,
    borderRadius: getPixel(8),
    paddingLeft: getPixel(22),
    paddingRight: getPixel(24),
    paddingVertical: getPixel(16),
    marginBottom: getPixel(16),
    ...layout.betweenHorizontal,
  },
  upgradeText: {
    color: color.orangeBase,
    ...font.caption1LightStyle,
  },
  upgradeIcon: {
    color: color.orangeBase,
    marginTop: getPixel(2),
  },
  upgradeImg: {
    width: getPixel(30),
    height: getPixel(30),
    marginRight: getPixel(8),
  },
  arrowImg: {
    position: 'absolute',
    width: getPixel(18),
    height: getPixel(18),
    right: getPixel(86),
    bottom: getPixel(-18),
  },
});

const homeOrderMapIcon =
  'https://pages.c-ctrip.com/rncarapp/qunar/app/orderCardBgMap.png';
const homeOrderMapBg =
  'https://pic.c-ctrip.com/car/osd/mobile/bbk/resource/orderCardBg.png';
const homeOrderPickBg =
  'https://pages.c-ctrip.com/rncarapp/qunar/app/order-card-tips-icon.png';

interface IOperation {
  operationId: DirectOpen;
  buttonName: string;
  icon: string;
  enable: boolean;
  label?: string;
}

interface IPosInfo {
  localDateTime: string;
  address: string;
  storeTel: string;
  title1: string;
  title2: string;
  pickReturnWay: string;
}

interface IOrderEntryProps {
  orderId: number;
  ctripOrderStatus: OrderStatusCtrip;
  ctripOrderStatusDesc: string;
  useDayDesc: string;
  cityName: string;
  useDay: number;
  vehicleName: string;
  vehiclePic: string;
  pickUpDate: string;
  returnDate: string;
  pickupLocation: string;
  returnLocation: string;
  earlyReturn: boolean;
  allOperations: IOperation[];
  pickupInfo: IPosInfo;
  returnInfo: IPosInfo;
  isPick: boolean;
  lastEnablePayTime: number;
  orderUpgradeInsInfo: { orderId: number; canUpgrade: boolean };
  setPhoneModalVisible: (data?: any) => void;
  onTimeOut: () => void;
  fetchOrderCardIns: (data: { orderId: number }) => void;
  // eslint-disable-next-line react/require-default-props
  hasEnsureTip?: boolean;
}

interface IOrderEntryState {
  tickVisible: boolean;
  minute: number;
  second: number;
  showTip: boolean;
  onTiming?: (data: any) => void;
}

interface IGoOrderDetail {
  directOpen?: DirectOpen;
}

const debounceConfigProps = {
  debounce: true,
  debounceTime,
};
export default class OrderCard extends PureComponent<
  IOrderEntryProps,
  IOrderEntryState
> {
  timer = null;

  preloadData: PreloadData;

  constructor(props) {
    super(props);
    this.state = {
      tickVisible: false,
      minute: 0,
      second: 0,
      showTip: true,
    };
  }

  componentDidMount() {
    const { lastEnablePayTime, ctripOrderStatus, orderId } = this.props;
    this.startTick(lastEnablePayTime, ctripOrderStatus);
    this.createPreloadKey();
    // eslint-disable-next-line react/destructuring-assignment
    this.props.fetchOrderCardIns({ orderId });
  }

  UNSAFE_componentWillReceiveProps(nextProps) {
    const { lastEnablePayTime, ctripOrderStatus } = nextProps;
    // eslint-disable-next-line react/destructuring-assignment
    const {
      lastEnablePayTime: nowLastEnablePayTime,
      ctripOrderStatus: nowCtripOrderStatus,
    } = this.props;
    if (
      lastEnablePayTime !== nowLastEnablePayTime ||
      ctripOrderStatus !== nowCtripOrderStatus
    ) {
      this.startTick(lastEnablePayTime, ctripOrderStatus);
    }
  }

  componentDidUpdate(prevProps) {
    const { orderId } = this.props;
    if (orderId !== prevProps.orderId) {
      this.createPreloadKey();
    }
  }

  createPreloadKey = () => {
    const { orderId } = this.props;
    // const url = this.getOrderBasePath(orderId);
    // this.preloadData = PreloadNextContainer.getPreloadKey(url);
  };

  getNowTime = lastTime => {
    const n = dayjs().valueOf();
    const minute = dayjs(lastTime - n).minute();
    const second = dayjs(lastTime - n).second();
    return {
      minute,
      second,
      n,
    };
  };

  // eslint-disable-next-line consistent-return
  startTick = (lastEnablePayTime, ctripOrderStatus) => {
    const { minute, second, n } = this.getNowTime(lastEnablePayTime);
    const tickVisible =
      ctripOrderStatus === OrderStatusCtrip.WAITING_PAY &&
      lastEnablePayTime > n;
    if (tickVisible) {
      this.setState({
        tickVisible,
        minute,
        second,
      });
      this.count(lastEnablePayTime);
    }
  };

  count = lastEnablePayTime => {
    clearTimeout(this.timer);
    this.timer = setTimeout(() => {
      // eslint-disable-next-line prefer-const
      let { minute, second, n } = this.getNowTime(lastEnablePayTime);
      second -= 1;
      if (second < 0) {
        second = 59;
        minute -= 1;
        if (minute < 0) {
          second = 0;
          minute = 0;
        }
      }
      this.setState({ second, minute });

      const { onTimeOut, ctripOrderStatus } = this.props;
      if (
        ctripOrderStatus === OrderStatusCtrip.WAITING_PAY &&
        lastEnablePayTime > n
      ) {
        this.count(lastEnablePayTime);
      } else {
        this.setState({ tickVisible: false });
        onTimeOut();
      }
    }, 1000);
  };

  // @qunar change 清除事件
  pageDidDisappear() {
    super.pageDidDisappear();
    this.timer && clearTimeout(this.timer);
  }

  goOrderDetail = (params?: IGoOrderDetail) => {
    const { directOpen } = params || {};
    const { orderId } = this.props;
    if (!orderId) return;

    let param = {
      data: {
        orderId,
        directOpen,
      }
    }

    const { ORDERDETAIL: url } = Platform.CAR_CROSS_URL;
    let orderPath = `${url.NEWISD}&data=${encodeURIComponent(
      JSON.stringify(param)
    )}&orderId=${orderId}&directOpen=${directOpen}`;

    URL.openURL(orderPath);
    // if (AppContext.isHomeCombine) {
    //   let gotoFunc = Ext.open;
    //   if (isAndroid && NativeModules.RentQNavigation && NativeModules.RentQNavigation.open) {
    //     gotoFunc = NativeModules.RentQNavigation.open;
    //   }
    //   gotoFunc('OrderDetail', {
    //     param,
    //     ...param,
    //     isHomeCombine: AppContext.isHomeCombine,
    //     homeCombineNewOpenContainerID: `${new Date().getTime()}`,
    //   })
    // } else {
    //   Ext.open('OrderDetail', {
    //     param,
    //     ...param,
    //   })
    // }
  };

  goGuide = () => {
    const { isPick, orderId, ctripOrderStatusDesc } = this.props;
    const param = getGuidePageParam({
      guideTabId: isPick ? GuideTabType.Pickup : GuideTabType.Dropoff,
      scene: SCENES.HomeOrderCard,
    });
    let gotopage = Channel.getPageId().Guide.EN;
    CarLog.LogCode({
      enName: ClickKey.C_HOME_ODRDER_CARD_GUIDE.KEY,
      info: {
        orderId: String(orderId),
        orderStatus: ctripOrderStatusDesc,
      },
    });
    // 没有 orderId 不做跳转
    if (!param?.orderId) return;
    if (AppContext.isHomeCombine) {
      const pageParam = {
        orderId: param?.orderId,
        selectedId: param?.selectedId,
      };
      URL.openURL(
        // eslint-disable-next-line max-len
        `${Platform.CAR_CROSS_URL.GUIDE.ISD}&orderId=${param?.orderId}&selectedId=${param?.selectedId}&pageParam=${encodeURIComponent(
          JSON.stringify(pageParam)
        )}&klbVersion=${param?.klbVersion}&scene=${SCENES.HomeOrderCard}`,
      );
    } else {
      AppContext.PageInstance.push(gotopage, {
        pageParam: param,
      });
    }
  };

  onPressBtn = operationId => {
    const { setPhoneModalVisible, orderId, ctripOrderStatusDesc } = this.props;

    const EN_NAME_MAP = {
      [DirectOpen.OrderDetail]: ClickKey.C_HOME_ODRDER_CARD_ORDERDETAIL,
      [DirectOpen.Renewal]: ClickKey.C_HOME_ODRDER_CARD_RENEWAL,
      [DirectOpen.PickUpMaterials]: ClickKey.C_HOME_ODRDER_CARD_MATERIAL,
      [DirectOpen.ToPay]: ClickKey.C_HOME_ODRDER_CARD_PAY,
      [DirectOpen.Contact]: ClickKey.C_HOME_ODRDER_CARD_CONTACT,
    };
    CarLog.LogCode({
      enName: EN_NAME_MAP[operationId]?.KEY || '',
      info: {
        orderId: String(orderId),
        orderStatus: ctripOrderStatusDesc,
      },
    });

    switch (operationId) {
      case DirectOpen.Contact:
        setPhoneModalVisible({
          visible: true,
          phoneModalType: CustomerPhoneModalType.HomeOrderCard,
        });
        break;

      default:
        this.goOrderDetail({
          directOpen: operationId,
        });
        break;
    }
  };

  closeTip = () => {
    this.setState({ showTip: false });
  };

  openUpgrade = () => {
    const { orderId, ctripOrderStatusDesc } = this.props;
    this.goOrderDetail({
      directOpen: DirectOpen.UpgradeCarService,
    });
    CarLog.LogCode({
      enName: ClickKey.C_ORDERCARD_UPGRADE_INS.KEY,
      info: {
        orderId: String(orderId),
        orderStatus: ctripOrderStatusDesc,
      },
    });
  };

  getButtonTip = (cardButtons: Array<IOperation>) => {
    const hasTipButton = cardButtons?.find(f => !!f.label);
    return hasTipButton?.label;
  };

  render() {
    const {
      orderId,
      useDay,
      ctripOrderStatusDesc,
      ctripOrderStatus,
      useDayDesc,
      vehiclePic,
      isPick,
      vehicleName,
      pickupInfo,
      returnInfo,
      allOperations = [],
      hasEnsureTip,
      orderUpgradeInsInfo,
      earlyReturn,
    } = this.props;

    const { minute, second, tickVisible, showTip } = this.state;

    if (!orderId) return null;

    const formatDate = localDateTime => {
      const { formatter, ctripWeekDayFormat } = DateFormatter;
      const { pickUpWeekStr: weekStr } = ctripWeekDayFormat({
        ptime: localDateTime,
        rtime: '',
      });
      const dateFormatter = formatter(localDateTime);
      let dateYmdString = dateFormatter.ymdShortString();
      let dateYearString = dateFormatter.hmString();
      dateYmdString = dayjs(localDateTime).format('M-D HH:mm');
      dateYearString = dateFormatter.yString();
      dateYmdString = dateYmdString.split(' ');
      dateYmdString.splice(1, 0, ` ${weekStr} `);
      return {
        dateYearString,
        dateYmdString,
      };
    };

    const isPayingStatus = ctripOrderStatus === OrderStatusCtrip.WAITING_PAY;

    const getBtnColorStyle = (operationId: DirectOpen) => {
      let colorStyle = null;
      if ([DirectOpen.ToPay, DirectOpen.Renewal].includes(operationId)) {
        colorStyle = styles.payColor;
      } else {
        colorStyle = styles.defaultIcon;
      }
      return colorStyle;
    };

    const getHeaderTipWrapColorStyle = () => {
      const gradientColorArrBlue = [
        // @qunar change
        color.topRank,
        color.topRank,
      ];
      const gradientColorArrOrange = [
        color.linearGradientOrderCardOrangeLight,
        color.linearGradientOrderCardOrangeDark,
      ];
      const colorStyle = isPayingStatus
        ? gradientColorArrOrange
        : gradientColorArrBlue;
      return colorStyle;
    };

    const tipWrapShadowStyle = [
      styles.headerTipWrapShadow,
      {
        shadowColor: isPayingStatus
          ? color.refundTotalAmount
          : color.cardShadowColor,
      },
    ];

    const takeInfo = isPick ? pickupInfo : returnInfo;
    const actionText = isPick
      ? getSharkValue('pickUp')
      : getSharkValue('dropOff');

    const { dateYmdString, dateYearString } = formatDate(
      takeInfo?.localDateTime,
    );

    const isShowYear = Boolean(Number(dateYearString) - dayjs().year());

    const formatData = num => (num < 10 ? `0${num}` : `${num}`);

    const timeTick = `${formatData(minute)}:${formatData(second)}`;

    const cardButtons = allOperations.filter(btn => !!btn.enable);

    const buttonTip = this.getButtonTip(cardButtons);

    const canUpgradeIns =
      orderUpgradeInsInfo?.orderId === orderId &&
      orderUpgradeInsInfo.canUpgrade;

    return (
      <View
        // @qunar chang
        style={[styles.wrapper, hasEnsureTip && styles.hasEnsureTip, { flex: getCombineFlex() }]}
        testID={CarLog.LogExposure({
          enName: ExposureKey.C_HOME_ORDER_CARD.KEY,
          info: {
            orderId: String(orderId),
            orderStatus: ctripOrderStatusDesc,
          },
        })}
      >
        <View style={tipWrapShadowStyle}>
          <LinearGradient
            style={[styles.headerTipWrap, tipWrapShadowStyle]}
            start={{ x: 0, y: 0.5 }}
            end={{ x: 1, y: 0.5 }}
            locations={[0, 1]}
            colors={getHeaderTipWrapColorStyle()}
          >
            {selector(
              isPayingStatus,
              <Text style={styles.headerTipWrapText}>
                {getSharkValue('home_myOrderAwaitPayment')}
              </Text>,
              <>
                {selector(
                  useDay,
                  <>
                    <Text style={styles.headerTipWrapText}>{useDay}</Text>
                    <View style={styles.tipGapLine} />
                  </>,
                )}
                <Text style={styles.headerTipWrapText}>{useDayDesc}</Text>
              </>,
            )}
          </LinearGradient>
        </View>

        {/* @qunar change */}
        <View
          style={[
            styles.carInfo,
            {flex: getCombineFlex()}
          ]}
        >
          <Image
            source={{ uri: autoProtocol(vehiclePic) }}
            resizeMode="contain"
            style={styles.carImg}
          />
          <Text style={styles.carName} numberOfLines={1}>
            {vehicleName}
          </Text>
          {selector(
            tickVisible,
            <View style={styles.tickWrap}>
              <Text style={styles.payTickText}>
                {getSharkValue('payRemainTime')}
              </Text>
              <Text style={styles.payTickText}>{timeTick}</Text>
            </View>,
          )}
        </View>
        <View style={styles.storeInfoWrap}>
          <BbkTouchable
            style={styles.infoWarp}
            onPress={() => {
              this.goOrderDetail();
            }}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...debounceConfigProps}
          >
            <Image
              source={{ uri: homeOrderMapBg }}
              resizeMode="cover"
              style={styles.storeInfoBg}
            />
            <View style={styles.actionTimeTextWrap}>
              <View style={styles.actionWrap}>
                <Image
                  source={{ uri: homeOrderPickBg }}
                  resizeMode="contain"
                  style={styles.actionBg}
                />
                <Text style={styles.actionText}>{actionText}</Text>
              </View>
              <Text style={styles.actionTime}>
                {!!earlyReturn && Texts.advanceTipPrefix}
                {selector(isShowYear, `${dateYearString}-`)}
                {dateYmdString}
              </Text>
            </View>
            {selector(
              takeInfo?.pickReturnWay,
              <View
                style={[
                  styles.descWarp,
                  {
                    marginBottom: selector(!takeInfo?.address, 0, getPixel(8)),
                  },
                ]}
              >
                <Text style={styles.posTitle}>{takeInfo?.title1}</Text>
                <Text style={styles.desc} numberOfLines={1}>
                  {takeInfo?.pickReturnWay}
                </Text>
              </View>,
            )}
            {selector(
              takeInfo?.address,
              <View style={styles.descWarp}>
                <Text style={styles.posTitle}>{takeInfo?.title2}</Text>
                <Text style={styles.desc} numberOfLines={1}>
                  {takeInfo?.address}
                </Text>
              </View>,
            )}
          </BbkTouchable>
          <BbkTouchable
            onPress={this.goGuide}
            style={styles.mapIconWrap}
            testID={UITestID.car_testid_home_order_card_btn_guide}
          >
            <Image
              source={{ uri: homeOrderMapIcon }}
              resizeMode="contain"
              style={styles.mapIcon}
            />
          </BbkTouchable>
        </View>
        {/** 升级服务 */}
        {canUpgradeIns && (
          <BbkTouchable style={styles.upgradeWrap} onPress={this.openUpgrade}>
            <View
              style={layout.flexRow}
              testID={CarLog.LogExposure({
                enName: ExposureKey.C_ORDERCARD_UPGRADE_INS.KEY,
                info: {
                  orderId: String(orderId),
                  orderStatus: ctripOrderStatusDesc,
                },
              })}
            >
              <Image
                source={{ uri: ImageUrl.orderCardUpgradeIcon }}
                style={styles.upgradeImg}
              />
              <Text style={styles.upgradeText}>
                {Texts.orderCardUpgradeTip}
              </Text>
            </View>
            <View style={layout.flexRow}>
              <Text style={styles.upgradeText}>
                {Texts.orderCardToUpgradeText}
              </Text>
              <Text type="icon" style={styles.upgradeIcon}>
                {icon.arrowRight}
              </Text>
            </View>
          </BbkTouchable>
        )}

        <View style={styles.buttonsWrap}>
          {cardButtons.map((btn, btnIndex) => (
            <Fragment key={btn.buttonName}>
              <BbkTouchable
                style={styles.button}
                onPress={() => this.onPressBtn(btn?.operationId)}
                // eslint-disable-next-line react/jsx-props-no-spreading
                {...debounceConfigProps}
                testID={`${UITestID.car_testid_page_home_order_card_btn}_${btn.buttonName}`}
              >
                <Text
                  type="icon"
                  style={[styles.btnIcon, getBtnColorStyle(btn.operationId)]}
                >
                  {btn.icon}
                </Text>
                <Text
                  style={[styles.btnText, getBtnColorStyle(btn.operationId)]}
                >
                  {btn.buttonName}
                </Text>
              </BbkTouchable>
              {selector(
                btnIndex < allOperations?.length - 1,
                <View style={styles.buttonLine} />,
              )}
            </Fragment>
          ))}
          {showTip && !!buttonTip && (
            <BbkTouchable style={styles.tipWrap} onPress={this.closeTip}>
              <Text style={styles.tipText}>{buttonTip}</Text>
              <Text type="icon" style={styles.tipIcon}>
                {icon.cross}
              </Text>
              <Image
                source={{ uri: ImageUrl.orderCardArrowIcon }}
                style={styles.arrowImg}
              />
            </BbkTouchable>
          )}
        </View>
      </View>
    );
  }
}
