import React, { use<PERSON><PERSON>back, useMemo, memo } from 'react';
import { View, StyleSheet, ImageBackground, Clipboard, ViewStyle, ScrollView } from 'react-native';
import { LinearGradient, Call, URL } from '@ctrip/crn';
import BbkModal, { BbkComponentPageModal } from '../../../Common/src/Components/Basic/Modal';
import BbkText from '../../../Common/src/Components/Basic/Text';
import { color, font, icon, layout, setOpacity } from '../../../Common/src/Tokens';
import { BbkUtils } from '../../../Common/src/Utils';
import BbkToast from '../../../Common/src/Components/Basic/Toast/src';
import { getSharkValue } from '../../../Common/src/Shark/src/Index';
import texts from '../Texts';
import { Expo<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ImageUrl } from '../../../Constants/Index';
import { CarLog, Utils } from '../../../Util/Index';
import Texts from '../Texts';

const { getPixel, vw, vh, isAndroid, fixIOSOffsetBottom } = BbkUtils;

const styles = StyleSheet.create({
  mbFixIos: {
    marginBottom: fixIOSOffsetBottom(50),
  },
  pt18: {
    paddingTop: getPixel(18),
  },
  wrap: {
    backgroundColor: color.tableGrayBg,
    borderTopLeftRadius: getPixel(24, 'floor'),
    borderTopRightRadius: getPixel(24, 'floor'),
    width: vw(100),
    minHeight: fixIOSOffsetBottom() + getPixel(430),
    maxHeight: fixIOSOffsetBottom() + vh(80),
  },
  wrapBgImg: {
    borderTopLeftRadius: getPixel(24, 'floor'),
    borderTopRightRadius: getPixel(24, 'floor'),
    width: vw(100),
    height: getPixel(430),
  },
  shadow: {
    shadowColor: color.grayBorder,
    shadowOpacity: 1.0,
    shadowRadius: getPixel(16),
    elevation: 4,
  },
  storeIphoneWrap: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  storeTitle: {
    ...font.title2MediumStyle,
  },
  recommendWrap: {
    width: getPixel(94),
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: color.recommendBg,
    marginLeft: getPixel(12),
    borderRadius: getPixel(4),
    marginTop: getPixel(isAndroid ? 1 : 0),
  },
  recommendIcon: {
    color: color.white,
    fontSize: getPixel(23),
  },
  recommendText: {
    color: color.white,
    ...font.labelLStyle,
    marginLeft: getPixel(4),
  },
  numberWrap: {
    marginTop: getPixel(8),
    ...layout.flexRow,
  },
  numberText: {
    ...font.caption1LightStyle,
  },
  extText: {
    ...font.body4Style,
  },
  splitLine: {
    width: getPixel(1),
    height: getPixel(16),
    backgroundColor: color.virtualNumberSplitLine,
    marginHorizontal: getPixel(16),
  },
  descText: {
    marginVertical: getPixel(16),
    color: color.virtualNumberSplitLine,
    ...font.caption1LightStyle,
    marginHorizontal: getPixel(32),
  },
  mainStyle: {
    backgroundColor: color.white,
    marginBottom: getPixel(18),
    borderRadius: getPixel(16),
    marginHorizontal: getPixel(24),
    padding: getPixel(2),
    overflow: 'hidden',
  },
  customerMain: {
    paddingBottom: getPixel(2),
  },
  customerWrap: {
    // flex: 1,
    paddingHorizontal: getPixel(30),
    justifyContent: 'center',
    paddingTop: getPixel(22),
    paddingBottom: getPixel(16),
  },
  customerIcon: {
    color: color.blueBase,
    fontSize: getPixel(48),
  },
  headWrap: {
    height: getPixel(93),
    paddingTop: getPixel(21),
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  headerText: {
    color: color.fontPrimary,
    ...font.title4MediumStyle,
  },
  headerIcon: {
    position: 'absolute',
    fontSize: getPixel(42),
    top: getPixel(22),
    left: getPixel(32),
    color: color.fontSecondary,
  },
  nonBusinessWrap: {
    backgroundColor: color.orangeBg,
    borderRadius: getPixel(8),
    paddingVertical: getPixel(16),
    paddingHorizontal: getPixel(20),
    marginHorizontal: getPixel(24),
    marginBottom: getPixel(18),
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: getPixel(32),
  },
  nonBusinessText: {
    color: color.orangeBase,
    ...font.caption1LightStyle,
  },
  nonBusinessIcon: {
    fontSize: getPixel(26),
    marginRight: getPixel(8),
    color: color.orangeBase,
  },
  copyIcon: {
    fontSize: getPixel(32),
    color: color.fontSecondary,
    marginLeft: getPixel(8),
  },
  titleLine: {
    backgroundColor: color.virtualNumberTitleBg,
    paddingHorizontal: getPixel(32),
    marginBottom: getPixel(18),
    marginHorizontal: getPixel(24),
    borderRadius: getPixel(4),
    height: getPixel(64),
  },
  mt30: {
    marginTop: getPixel(30),
  },
});

const bgGradientcolors = [
  setOpacity(color.virutualNumberBgGradient2, 0),
  setOpacity(color.virutualNumberBgGradient1, 0.5),
  color.white,
];

interface virtualNumberLogDataType {
  vehicleName?: string;
  vehicleCode?: string;
  vendorName?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
}
interface IPropsType {
  visible: boolean;
  isPMSVendor?: boolean;
  virtualNumber?: string; // 虚拟小号
  vendorVNumber?: string; // 商家官方电话
  vendorImUrl?: string;
  secAndPrivacyTips?: string;
  nonBusinessHoursTips?: string;
  virtualNumberLog?: virtualNumberLogDataType;
  onClose: () => void;
  contactData?: ContactBlockTypes[];
  title?: string;
  useBbkModal?: boolean;
}
interface TitleLineTypes {
  title?: string;
  subTitle?: string;
  style?: ViewStyle;
}
export const TitleLine: React.FC<TitleLineTypes> = props => {
  const { title, subTitle, style } = props;
  return (
    <View style={[layout.startHorizontal, styles.titleLine, style]}>
      <BbkText
        style={[
          font.body3Medium2Style,
          { marginRight: getPixel(20), color: color.blueBase },
        ]}
      >
        {title}
      </BbkText>
      <BbkText style={[font.caption1LightStyle, { color: color.blueBase }]}>
        {subTitle}
      </BbkText>
    </View>
  );
};

interface LogInfoType extends virtualNumberLogDataType {
  onPressFrom?: number;
}
export interface ExposureLogTypes {
  enName: string;
  info?: LogInfoType;
}
export interface ContactBlockTypes {
  titleLineInfo?: TitleLineTypes;
  secAndPrivacyTips?: string;
  contactLists?: ContactListsTypes[];
  exposureLog?: ExposureLogTypes;
  mainStyle?: ViewStyle;
  handlePress?: (props: ContactListsTypes) => void;
  index?: number;
}
export interface ContactListsTypes {
  virtualNumber?: string;
  secAndPrivacyTips?: string;
  isRecommend?: boolean;
  rightIcon?: string;
  title: string;
  clickLog?: ExposureLogTypes;
  vendorImUrl?: string;
}

export const ContactBlock: React.FC<ContactBlockTypes> = props => {
  const {
    secAndPrivacyTips,
    exposureLog,
    titleLineInfo = {},
    mainStyle,
    contactLists,
    handlePress,
    index,
  } = props;

  const hasTitleLine = !!Object.keys(titleLineInfo)?.length;
  return (
    <View testID={exposureLog && CarLog.LogExposure(exposureLog)}>
      {hasTitleLine && (
        <TitleLine
          title={titleLineInfo.title}
          subTitle={titleLineInfo.subTitle}
          style={!!index && styles.mt30}
        />
      )}
      <View style={[styles.mainStyle, styles.shadow, mainStyle]}>
        {!!contactLists?.length &&
          contactLists.map((item, idx) => {
            const {
              title,
              virtualNumber,
              rightIcon,
              isRecommend: isRecommendProp,
            } = item;
            const phoneNumber = virtualNumber?.split(',')?.[0];
            const extNumber = virtualNumber?.split(',')?.[1];
            const isRecommend = !index && !idx && !!virtualNumber;
            const handleCopy = () => {
              Clipboard.setString(`${virtualNumber}`);
              BbkToast.show(getSharkValue('copySuccess'), 1);
            };
            return (
              <LinearGradient
                start={{ x: 0.0, y: 1.0 }}
                end={{ x: 1.0, y: 1.0 }}
                locations={[0, 0.5, 1]}
                colors={bgGradientcolors}
                style={[styles.customerWrap]}
              >
                <View style={layout.betweenHorizontal}>
                  <View>
                    <View style={styles.storeIphoneWrap}>
                      <BbkText style={styles.storeTitle}>{title}</BbkText>
                      {(isRecommendProp || !!isRecommend) && (
                        <View style={styles.recommendWrap}>
                          <BbkText type="icon" style={styles.recommendIcon}>
                            {icon.encourageIcon}
                          </BbkText>
                          <BbkText style={styles.recommendText}>
                            {texts.recommend}
                          </BbkText>
                        </View>
                      )}
                    </View>
                    {!!virtualNumber && (
                      <View style={styles.numberWrap}>
                        {!!phoneNumber && (
                          <BbkText style={styles.numberText}>
                            {phoneNumber}
                          </BbkText>
                        )}
                        {!!extNumber && (
                          <>
                            <View style={styles.splitLine} />
                            <BbkText
                              style={styles.extText}
                            >{`${texts.extTitle} ${extNumber}`}</BbkText>
                          </>
                        )}
                        <BbkText
                          type="icon"
                          style={styles.copyIcon}
                          onPress={handleCopy}
                        >
                          {icon.copy}
                        </BbkText>
                      </View>
                    )}
                  </View>
                  <BbkText
                    type="icon"
                    style={styles.customerIcon}
                    onPress={() => handlePress(item)}
                  >
                    {rightIcon}
                  </BbkText>
                </View>
              </LinearGradient>
            );
          })}

        {!!secAndPrivacyTips && (
          <BbkText style={styles.descText}>{secAndPrivacyTips}</BbkText>
        )}
      </View>
    </View>
  );
};

const VirtualNumberStoreModal: React.FC<IPropsType> = memo(props => {
  const {
    visible,
    isPMSVendor,
    virtualNumber,
    vendorVNumber,
    vendorImUrl,
    secAndPrivacyTips,
    nonBusinessHoursTips,
    virtualNumberLog,
    onClose,
    contactData,
    title,
    useBbkModal,
  } = props;

  const handlePress = useCallback(
    ({ vendorImUrl: url, clickLog = {}, virtualNumber: number }) => {
      if (url) {
        URL.openURLWithTitle(url, Texts.imVendorTitle);
        CarLog.LogCode(clickLog);
      }
      if (number) {
        const callNumber = Utils.getGoodFixedTelephone(number);
        Call.makeCall(callNumber);
        CarLog.LogCode({ ...clickLog, virtualNumber: callNumber });
      }
    },
    [],
  );
  const contactBlocks = useMemo(() => {
    const storeItem = virtualNumber
      ? {
          secAndPrivacyTips,
          exposureLog: {
            enName: ExposureKey.C_VIRTUAL_NUMBER_STORE_MODAL_PHONE.KEY,
            info: {
              ...virtualNumberLog,
            },
          },
          contactLists: [
            {
              title: texts.storePhone,
              isRecommend: isPMSVendor,
              virtualNumber,
              rightIcon: icon.phone,
              clickLog: {
                enName: ClickKey.C_VIRTUAL_NUMBER_STORE_MODAL_PHONE.KEY,
                info: {
                  ...virtualNumberLog,
                },
              },
            },
          ],
        }
      : null;
    const vendorItem = vendorVNumber
      ? {
          exposureLog: {
            enName: ExposureKey.C_VIRTUAL_NUMBER_STORE_MODAL_VENDOR.KEY,
            info: {
              ...virtualNumberLog,
            },
          },
          secAndPrivacyTips,
          contactLists: [
            {
              title: texts.vendorText,
              isRecommend: false,
              virtualNumber: vendorVNumber,
              rightIcon: icon.phone,
              clickLog: {
                enName: ClickKey.C_VIRTUAL_NUMBER_STORE_MODAL_VENDOR.KEY,
                info: {
                  ...virtualNumberLog,
                },
              },
            },
          ],
        }
      : null;
    const imItem = vendorImUrl
      ? {
          exposureLog: {
            enName: ExposureKey.C_VIRTUAL_NUMBER_STORE_MODAL_CUSTOMER.KEY,
            info: {
              ...virtualNumberLog,
            },
          },
          mainStyle: styles.customerMain,
          contactLists: [
            {
              isRecommend: !isPMSVendor,
              title: texts.vendorOnlineService,
              rightIcon: icon.customerChat,
              vendorImUrl,
              clickLog: {
                enName: ClickKey.C_VIRTUAL_NUMBER_STORE_MODAL_CUSTOMER.KEY,
                info: {
                  ...virtualNumberLog,
                },
              },
            },
          ],
        }
      : null;

    const data: ContactBlockTypes[] = isPMSVendor
      ? [storeItem, imItem]
      : [imItem, vendorItem, storeItem];
    return data.filter(v => !!v);
  }, [
    vendorImUrl,
    virtualNumberLog,
    virtualNumber,
    vendorVNumber,
    isPMSVendor,
    secAndPrivacyTips,
  ]);
  const currentContactBlocks =
    contactData?.length > 0 ? contactData : contactBlocks;
  // BbkModal的zIndex层级比BbkComponentPageModal高，在订祥页面引用可以盖住违章车损页面
  const Wrapper = useBbkModal ? BbkModal : BbkComponentPageModal;
  return (
    <Wrapper
      visible={visible}
      modalVisible={visible}
      location="bottom"
      animateType="slideUp"
      animateDuration={300}
      onMaskPress={onClose}
    >
      <ImageBackground
        style={styles.wrap}
        source={{
          uri: `${ImageUrl.componentImagePath}VirtualNumber/modalBG.png`,
        }}
        imageStyle={styles.wrapBgImg}
      >
        <View style={styles.headWrap}>
          <BbkText style={styles.headerText}>
            {title || texts.consultStore}
          </BbkText>
          <BbkText type="icon" style={styles.headerIcon} onPress={onClose}>
            {icon.cross}
          </BbkText>
        </View>
        <ScrollView style={styles.pt18}>
          {/** 非营业时间提示 */}
          {!!nonBusinessHoursTips && (
            <View style={styles.nonBusinessWrap}>
              <BbkText style={styles.nonBusinessIcon} type="icon">
                {icon.circleI}
              </BbkText>
              <BbkText style={styles.nonBusinessText}>
                {nonBusinessHoursTips}
              </BbkText>
            </View>
          )}
          <View style={styles.mbFixIos}>
            {currentContactBlocks.map((item, index) => (
              <ContactBlock
                index={index}
                key={item?.contactLists?.[0]?.title}
                {...item}
                handlePress={handlePress}
              />
            ))}
          </View>
        </ScrollView>
      </ImageBackground>
    </Wrapper>
  );
});
export default VirtualNumberStoreModal;
