import { getSharkValue } from '../../Common/src/Shark/src/Index';

export default {
  get notLimit() {
    return getSharkValue('vendor_list_not_limit');
  },
  get limitTip() {
    return getSharkValue('vendor_list_limit_tip');
  },
  get famousBrand() {
    return getSharkValue('vendor_list_famous_brand');
  },
  get score() {
    return getSharkValue('vendor_list_score');
  },
  get noScore() {
    return getSharkValue('listCombine_noneComment');
  },
  get pickUp() {
    return getSharkValue('vendor_list_pick_up');
  },
  get dropOff() {
    return getSharkValue('vendor_list_drop_off');
  },
  get soldOutText() {
    return getSharkValue('listCombine_soldOut');
  },
  get fitResult() {
    return getSharkValue('vendor_list_fit_result');
  },
  get notFitResult() {
    return getSharkValue('vendor_list_not_fit_result');
  },
  get notFitResultNew() {
    return getSharkValue('vendor_list_recommondDescNew');
  },
  get book() {
    return getSharkValue('vendor_list_book');
  },
  get couponBook() {
    return getSharkValue('vendor_list_coupon_book');
  },
  get couponBookNow() {
    return getSharkValue('vendor_list_coupon_book_now');
  },
  get backToList() {
    return getSharkValue('vendor_list_back_to_list_page');
  },
  showMoreVendor: value => getSharkValue('vendor_list_more_vendor', value),
  get listReceivePromotionToast() {
    return getSharkValue('listReceivePromotionToast');
  },
  get seat() {
    return getSharkValue('seat');
  },
  get door() {
    return getSharkValue('List_Uvo4');
  },
  get phoneNumberError() {
    return getSharkValue('product_confirm_phone_number_error');
  },
  get phoneNumberDesc() {
    return getSharkValue('product_confirm_phone_number_desc');
  },
  get contactStore() {
    return getSharkValue('cancelPenaltyStatusContact');
  },
  get contactUs() {
    return getSharkValue('product_confirm_contact_us');
  },
  get cancel() {
    return getSharkValue('cancel');
  },
  get detail() {
    return getSharkValue('product_confirm_vehicleDetail');
  },
  get freeFee() {
    return getSharkValue('freeFee');
  },
  get look_price() {
    return getSharkValue('list_newEnerge_look_price');
  },
  get storeText() {
    return getSharkValue('storeText');
  },
  get vendorText() {
    return getSharkValue('vendorText');
  },
  get vendorOnlineService() {
    return getSharkValue('vendorOnlineService');
  },
  get recommend() {
    return getSharkValue('recommend');
  },
  get storePhone() {
    return getSharkValue('phone');
  },
  get consultStore() {
    return getSharkValue('product_consult_store');
  },
  get extTitle() {
    return getSharkValue('product_ext_title');
  },
  get recommendPriceTitle() {
    return getSharkValue('List_XWQA');
  },
  get lessModalTitle() {
    return getSharkValue('less_modal_title');
  },
  get lessContentTitle() {
    return getSharkValue('less_content_title');
  },
  get lessModalFootText() {
    return getSharkValue('less_modal_foot_text');
  },
  get searchLessResultBtn() {
    return getSharkValue('searchLessResultBtn');
  },
  get searchLessResultBtn2() {
    return getSharkValue('searchLessResultBtn2');
  },
  get recommondDesc() {
    return getSharkValue('vendor_list_recommondDesc');
  },
  get toggleContent() {
    return getSharkValue('toggleContent');
  },
  get newCar() {
    return getSharkValue('newCar');
  },
  get carUnit() {
    return getSharkValue('carUnit');
  },
  get imVendorTitle() {
    return getSharkValue('imVendorTitle')
  }
};
