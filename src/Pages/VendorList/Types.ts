import { ReactNode } from 'react';
import { TextStyle, ViewStyle, ImageStyle } from 'react-native';
import {
  VendorTagType,
  VehicleListType,
} from '../../Common/src/Logic/src/List/Types/ListDtoType';
import { ImgType } from '../../ComponentBusiness/EmptyComponent/src/EmptyComponent';
import { TotalPriceModalType } from '../../ComponentBusiness/Common/src/Enums';
import { IMarketThemeVendorList } from '../../Types/Dto/QueryThemeConfigResponseType';
import {
  ExtraInfosType,
  MediaGroupInfo,
  MediaInfo,
} from '../../Types/Dto/QueryVehicleDetailListResponseType';
import { PageParamType } from '../../Types/Dto/QueryVehicleDetailListRequestType';

export interface IBbkVehicleNameProps {
  name: string;
  isSimilar: boolean;
  isHotLabel: boolean;
  licenseTag: string;
  licenseType: string;
}

export interface IVehicleImage {
  isSmallImage?: boolean;
  smallImage?: ISmallImage;
  isProductLoading?: boolean;
  firstImageUrl?: string;
  imageList?: Array<ISmallImage>;
  video?: string;
  defaultImage?: string;
  animateStyle?: any;
  tipVisiable?: boolean;
  videoRefCallBack?: (data?: any) => void;
  getTestId?: () => void;
  vehicleCode?: string;
  videoStateChangedCallBack?: (videoPlayerState: string) => void;
}

export interface IBouncesImage {
  vehicleCode?: string;
  totalPhotos?: Array<MediaInfo>;
  tabNameMap?: any;
  tabNameMapIndex?: any;
  mediaGroup?: Array<MediaGroupInfo>;
  note?: string;
}

export interface ISmallImage extends IImageLabel {
  url?: string;
}

export interface IImageLabel {
  type?: number;
  sourceName?: string;
}

export enum IImageType {
  StorePicture = 1,
  DefaultPicture = 2,
}

export interface ILimitTipInfoType {
  isShowTip: boolean; // 是否展示限行提示模块
  isLimit: boolean; // 是否限行
  tipText: string; // 提示文案
  onPressTip?: () => void;
}

export interface IVehicleAndLimit extends ILimitTipInfoType {
  isHotLabel?: boolean;
  isSimilar?: boolean;
  licenseTag?: string;
  licenseType?: string;
  name?: string;
  vehicleCode?: string;
  onPressVehicleName?: () => void;

  descList?: Array<any>;
  wrapStyle?: Array<ViewStyle>;
  marketingAtmosphere?: number;
  hasLaborDayLabel?: boolean;
  isShowMarketTheme?: boolean;
  isNewEnergy?: boolean;
  minTotalPrice?: number;
  showFullImmerse?: boolean;
}

export interface ITipCenter {
  isLimit?: boolean;
  tipText?: string;
}
export enum EnterPosition {
  topScreen = 1, // 首屏入口
  secondScreen = 2, // 二屏置顶入口
}

export interface ILocationAndDateMonitor {
  ptime: any;
  rtime: any;
  pickupLocation: {
    locationType: string;
    locationCode: string;
    locationName: string;
  };
  returnLocation: {
    locationType: string;
    locationCode: string;
    locationName: string;
  };
  style?: ViewStyle;
  testID?: string;
  clickWithLog?: () => void;
  datePickerRef?: any;
  enterPosition?: EnterPosition;
}

export enum IMarketingAtmosphere {
  RestAssured = 1,
  Spring = 2,
  YunNan = 3,
}
export interface IMarketingBanner {
  isShowRestAssured?: boolean;
  wrapStyle?: ViewStyle;
  marketingAtmosphere?: IMarketingAtmosphere;
  hasLaborDayLabel?: boolean;
  marketTheme?: IMarketThemeVendorList;
  isShowMarketTheme?: boolean;
  yunnanBannerInfo?: ExtraInfosType;
}

export interface IPriceItem {
  price: number;
  currency: string;
  originalTotalPrice?: number;
}

export interface IPriceDescProps {
  dayPrice: number;
  totalPrice: number;
  dayText: string;
  totalText: string;
  currentCurrencyCode: string;
  originDailyPrice?: number;
}

export interface ITextIncludeSoldOut {
  style: TextStyle;
  text: string;
  theme?: any;
  testID?: string;
}

export interface IVendorHeader {
  vendorName: string;
  isOptimize: boolean;
  score: string;
  commentDesc: string;
  onPressReview: () => void;
  showRightIcon?: boolean; // 供应商名称后面是否要展示右箭头
  arrowRightStyle?: TextStyle;
  nationalChainTagTitle?: string;
  theme?: any;
  rightBtn?: string;
  onPressRightBtn?: () => void;
  wrapStyle?: ViewStyle;
  vendorNameStyle?: TextStyle;
  optimizeImgStyle?: any;
  isOptimizationStrengthen?: boolean; // 是否优选强化
  optimizationStrengthenImgStyle?: ImageStyle;
  optimizationStrengthenDotStyle?: ViewStyle;
  optimizationStrengthenVendorNameStyle?: TextStyle;
  isShowOptimizationStrengthenRightArrow?: boolean;
}

export interface IPickUpDropOffDesc {
  pickUpDesc: string;
  dropOffDesc?: string;
  isPickUpRentCenter?: boolean;
  isDropOffRentCenter?: boolean;
  pickUpRentCenterName?: string;
  dropOffRentCenterName?: string;
  theme?: any;
}

export interface IVendor extends IVendorHeader {
  isEasyLife?: boolean;
  isSoldOut?: boolean;
  almostSoldOutLabel?: string;
  isShowAtTop?: boolean;
  wrapStyle?: ViewStyle;
  isShowBtnIcon?: boolean; // 底部是展示“icon”还是展示“订”字

  pickUpDesc: string;
  dropOffDesc?: string;
  isPickUpRentCenter?: boolean;
  isDropOffRentCenter?: boolean;

  vehicleTags?: Array<VendorTagType>;
  serviceTags?: Array<VendorTagType>;
  marketTags?: Array<VendorTagType>;

  hint?: string;
  newCar?: boolean;
  priceDescProps: IPriceDescProps;

  onPressVendor: (uniqueCode?: string, skuId?: string) => void;
  onPressBooking: (type: TotalPriceModalType, uniqueCode: string) => void;
  onPressQuestion: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressReview: (uniqueCode?: string) => void;

  uniqueCode?: string; // 供应商报价的唯一code

  vendorPriceKey?: string; // 售罄标识
  clickLogData?: any; // 点击埋点数据
  exposureLogData?: any; // 曝光埋点数据
  hasFees?: boolean; // 是否有fees节点
  isCouponBook?: boolean; // 是否是领券订产品
  pickUpRentCenterName?: string;
  dropOffRentCenterName?: string;
  isUrlTop?: boolean; // 是否是车型置顶
  isOptimizationStrengthen?: boolean; // 是否优选强化
  skuId?: string; // 车型skuid（仅卡拉比车型有）
  sortType?: number;
  licenseTag?: string;
  licenseType?: string;
  isNewLicensePlate: boolean;
  isSelfService?: boolean;
}

export interface ISectionHeader {
  isFit?: boolean;
  isShowGradient?: boolean;

  locationDate?: ILocationAndDateMonitor;
  isLocationDateFixed?: boolean;
  isShowFitTitle?: boolean;
  isShowFitBottomGradientLine?: boolean;
  vehicleCode?: string;

  isShowRightArrow?: boolean;
  onPressRightArrow?: () => void;
  testID?: string;
  clickWithLog?: () => void;
}

export interface ISectionFooter {
  moreNum?: number;
  onPressMore?: () => void;
  isShowBackToListPage?: boolean;
  onPressBackToListPage?: () => void;
  isShowGradient?: boolean;
  hasMoreBtn?: boolean;
  isFit?: boolean;
  vehicleCode?: string;
}

export interface ISection {
  isLoading?: boolean;
  sectionHeader?: ISectionHeader;
  vendorList?: Array<IVendor>;
  sectionFooter?: ISectionFooter;
  pTime?: string;
  onPressQuestion?: (
    type: string,
    uniqueCode?: string,
    hasFees?: boolean,
  ) => void;
  onPressBooking?: (type: TotalPriceModalType, uniqueCode: string) => void;
  fetchVehicleDetailList?: () => void;
  errorImgType?: ImgType;
  saleOutList?: Array<string>;
  isHasSpecificButSoldOut?: boolean;
  isNoSpecificButHasFiltered?: boolean;
  priceListLen?: number;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    [props: string]: any;
  }) => void;
  datePickerRef?: any;
  isSpecificList?: boolean;
  showAmount?: number;
  vehicleCode?: string;
  showSearchSelectorWrapWithLog?: () => void;
  sectionHeaderTestID?: string;
  handleshowFit?: () => void;
}

export enum IPicSource {
  SmallImage = 20,
}

export enum IAtmosphere {
  RestAssured = 1, // 安心行
  WarmSpring = 2, // 2022 3-23 暖春租车节
  Yunnan = 3, // 云南文旅
}
export interface IVendorPrice extends IPriceDescProps {
  isSoldOut?: boolean;
  vendorPriceKey?: string; // 供应商报价key,用来判断是否售罄
  onPressQuestion: () => void;
  clickLogData?: any; // 点击埋点数据
  exposureLogData?: any; // 曝光埋点数据
  wrapStyle?: Array<ViewStyle>;
  renderMarket?: () => void;
  hasMarketTags?: boolean;
  hasMarketTagsUpgrade?: boolean;
  isCouponBook?: boolean;
  singlePriceWrapStyle?: ViewStyle;
  totalPriceWrapStyle?: ViewStyle;
}

export interface IVehicleModal {
  visible: boolean;
  onClose: () => void;
  data: any;
  footChildren?: ReactNode;
  onPressHelp?: (fuelType) => void;
  onPressShowLessModal?: () => void;
}

export enum IClickVendorArea {
  Review = 1,
  Question = 2,
  Booking = 3,
  Vendor = 4,
  CouponBook = 5, // 领券订
}

export interface IVendorListFirstScreenParamType {
  vehicleInfo: VehicleListType;
  isUsed?: boolean;
}

/**
 * 注意:当有多个弹层可以同时打开时,如果想通过同一方法进行关闭,则需将先关闭的弹层配置在前面
 * 例如:费用明细弹层可以在信息确认弹层上打开，则需将费用明细的type配置在信息确认弹层之前
 * */
export enum IVendorListModalVisibleType {
  timeOutPopVisible = 'timeOutPopVisible',
  priceDetailModalVisible = 'priceDetailModalVisible',
  totalPriceModalVisible = 'totalPriceModalVisible',
  isEasyLifeModalVisible = 'isEasyLifeModalVisible',
  vehicleModalVisible = 'vehicleModalVisible',
  limitRulePopVisible = 'limitRulePopVisible',
  couponModalVisible = 'couponModalVisible',
  isVirtualNumberDialogVisible = 'isVirtualNumberDialogVisible',
  virtualNumberStoreModalVisible = 'virtualNumberStoreModalVisible',
  productConfirmModalVisible = 'productConfirmModalVisible',
}

export enum PriceDetailModalType {
  isFromRecommendList = 1,
  isFromProductConfirm = 2,
}

export interface ILocationCenterView {
  locationDate?: ILocationAndDateMonitor;
  setLocationAndDatePopIsShow?: (data?: {
    visible?: boolean;
    locationDatePopType?: number;
    [propsKey: string]: any;
  }) => void;
  testID: string;
  datePickerRef?: any;
  clickWithLog?: () => void;
  enterPosition?: EnterPosition;
  initMaxWidth?: number;
}

export interface IVendorListCurrentPageParams {
  pageParam?: PageParamType;
  vehicleIndex?: number;
  vendorListFirstScreenParam: IVendorListFirstScreenParamType;
  priceListLen?: number;
}

export interface IArrow {
  showFullImmerse?: boolean;
}
