import { ResponseMapType } from '../../Common/src/CarFetch/src/FetchTypes';
import { ProductListType } from '../../Common/src/Logic/src/List/Types/ListDtoType';

export interface ExtMapType {
  key?: string;
}

export interface BaseResponseType {
  isSuccess?: boolean;
  code?: string;
  returnMsg?: string;
  requestId?: string;
  cost?: number;
  showMessage?: string;
  extMap?: ExtMapType;
  extraIndexTags?: ExtMapType;
  apiResCodes?: Array<string>;
  hasResult?: boolean;
  errorCode?: string;
  message?: string;
}

export interface ErrorFieldsType {
  FieldName?: string;
  ErrorCode?: string;
  Message?: string;
}

export interface ErrorsType {
  Message?: string;
  ErrorCode?: string;
  StackTrace?: string;
  SeverityCode?: any;
  ErrorFields?: Array<ErrorFieldsType>;
  ErrorClassification?: any;
}

export interface ExtensionType {
  Id?: string;
  Version?: string;
  ContentType?: string;
  Value?: string;
}

export interface ResponseStatusType {
  Timestamp?: Date;
  Ack?: string;
  Errors?: Array<ErrorsType>;
  Build?: string;
  Version?: string;
  Extension?: Array<ExtensionType>;
}

export interface SimilarVehicleInfosType {
  vehicleCode?: string;
  vehicleName?: string;
  vehicleImageUrl?: string;
}

export interface VendorSimilarVehicleInfosType {
  bizVendorCode?: string;
  vendorName?: string;
  vendorLogo?: string;
  similarVehicleInfos?: Array<SimilarVehicleInfosType>;
}

export interface PicListType {
  imageUrl?: string;
  imageClass?: number;
  imageType?: number;
  sortNum?: number;
}

export interface SourcePicInfosType {
  source?: number;
  type?: number;
  sourceName?: string;
  picList?: Array<PicListType>;
}

export enum MediaGroupType {
  /**
   * 全部
   */
  All = -1,
  /**
   * 封面
   */
  Cover = 1,
  /**
   * 视屏
   */
  Video = 2,
  /**
   * VR
   */
  VR = 3,
  /**
   * 外观
   */
  Appearance = 4,
  /**
   * 前排
   */
  Front = 5,
  /**
   * 后排
   */
  Back = 6,
  /**
   * 相册
   */
  Album = 7,
}

export enum AlbumType {
  /**
   * 官方相册
   */
  Official = 1,
  /**
   * 门店实拍（供应商）相册
   */
  Store = 2,
  /**
   * 用户实拍相册
   */
  User = 3,
}

export enum MediaType {
  /**
   * 图片
   */
  Picture = 1,
  /**
   * 视频
   */
  Video = 2,
  /**
   * VR
   */
  VR = 3,
}

export interface MediaInfo {
  type?: number;
  url?: string;
  cover?: string;
  sortNum?: number;
  groupName?: string;
  groupType?: MediaGroupType;
  index?: number;
  groupId?: string;
  itemCountInGroup?: number;
  itemIdInGroup?: number;
  isEqualListImage?: boolean;
}

export interface MediaGroupInfo {
  groupType?: MediaGroupType;
  groupName?: string;
  groupSortNum?: number;
  medias: Array<MediaInfo>;
}

export interface MultimediaAlbum {
  albumName?: string;
  note?: string;
  albumType?: AlbumType;
  mediaGroup?: Array<MediaGroupInfo>;
}

export interface VehicleInfoType {
  brandId?: number;
  brandEName?: string;
  brandName?: string;
  name?: string;
  zhName?: string;
  vehicleCode?: string;
  imageUrl?: string;
  groupCode?: string;
  groupSubClassCode?: string;
  groupName?: string;
  transmissionType?: number;
  transmissionName?: string;
  passengerNo?: number;
  doorNo?: number;
  luggageNo?: number;
  displacement?: string;
  struct?: string;
  fuel?: string;
  gearbox?: string;
  driveMode?: string;
  style?: string;
  imageList?: Array<string>;
  userRealImageList?: Array<string>;
  storeRealImageList?: Array<string>;
  similarImageList?: Array<string>;
  specializedImages?: Array<string>;
  vedio?: string;
  isSpecialized?: boolean;
  isHot?: boolean;
  recommendDesc?: string;
  hasConditioner?: boolean;
  conditionerDesc?: string;
  spaceDesc?: string;
  similarCommentDesc?: string;
  vendorSimilarVehicleInfos?: Array<VendorSimilarVehicleInfosType>;
  vehicleAccessoryImages?: Array<string>;
  license?: string;
  licenseStyle?: string;
  licenseDescription?: string;
  realityImageUrl?: string;
  sourcePicInfos?: Array<SourcePicInfosType>;
  multimediaAlbums?: Array<MultimediaAlbum>;
  luggageSize?: string;
  modeYear?: string;
  versionName?: string;
  vehicleLevel?: string;
  fuelNew?: string;
  oilType?: number;
  video?: string;
  cover?: string;
  vr?: string;
}

export interface CommentListType {
  avatar?: string;
  userName?: string;
  userRate?: number;
  userRateLevel?: number;
  content?: string;
}

export interface CommentInfoType {
  level?: string;
  vendorDesc?: string;
  commentCount?: number;
  qCommentCount?: number;
  qExposed?: string;
  overallRating?: string;
  maximumRating?: number;
  commentLabel?: string;
  hasComment?: number;
  noComment?: string;
  link?: string;
  commentList?: Array<CommentListType>;
}

export interface DeductInfosType {
  dayAmount?: number;
  totalAmount?: number;
  payofftype?: number;
}

export interface PriceInfoType {
  currentDailyPrice?: number;
  currentOriginalDailyPrice?: number;
  oTPrice?: number;
  currentTotalPrice?: number;
  currentCurrencyCode?: string;
  localCurrencyCode?: string;
  marginPrice?: number;
  naked?: boolean;
  priceVersion?: string;
  priceType?: number;
  deductInfos?: Array<DeductInfosType>;
}

export interface VcExtendRequestType {
  responsePickUpLocationId?: string;
  responseReturnLocationId?: string;
  vendorVehicleId?: string;
}

export interface PromotionType {
  type?: number;
  title?: string;
  description?: string;
  longTag?: string;
  longDesc?: string;
  couponDesc?: string;
  deductionPercent?: number;
  deductionAmount?: number;
  dayDeductionAmount?: number;
  payofftype?: number;
  payoffName?: string;
  code?: string;
  isFromCtrip?: boolean;
  islimitedTimeOfferType?: boolean;
  sortNum?: number;
  strategySource?: string;
  earningsCost?: string;
  businessCost?: string;
  resourceCost?: string;
  configVersion?: string;
  isEnabled?: boolean;
  actionedDate?: string;
  expiredDate?: string;
  extDesc?: string;
  selected?: boolean;
  startAmount?: number;
  isOverlay?: boolean;
  promotionId?: number;
  overlayDesc?: string;
  couponName?: string;
  unitName?: string;
  popAmountTile?: string;
  deductionType?: number;
  discountType?: number;
  adjustPriceCode?: string;
}

export interface UnionCardFilterType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
  mark?: string;
  positionCode?: string;
}

export interface SubListType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface SubListType2 {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface LabelsType {
  title?: string;
  titleExtra?: string;
  category?: number;
  type?: number;
  code?: string;
  typeDesc?: string;
  description?: string;
  sortNum?: number;
  subTitle?: string;
  icon?: string;
  showLayer?: number;
  colorCode?: string;
  subList?: Array<SubListType2>;
  labelCode?: string;
  positionCode?: string;
  groupCode?: string;
  tagGroups?: number;
  tagSortNum?: number;
  amountTitle?: string;
  groupId?: number;
  mergeId?: number;
}

export interface MergeInfoType {
  vehicleId?: string;
  storeId?: string;
}

export interface IsdShareInfoType {
  orginaltotal?: number;
  isrec?: boolean;
  recommendOrder?: number;
  mergeId?: number;
  recsort?: number;
  rectype?: number;
  cvid?: number;
  rentalamount?: number;
  totalDailyPrice?: number;
  vdegree?: string;
  grantedcode?: string;
  mergeInfo?: Array<MergeInfoType>;
}
export enum PlatformCode {
  direct = 0,
  pms = 10,
}
export interface ReferenceType {
  bizVendorCode?: string;
  vendorCode?: string;
  pStoreCode?: string;
  rStoreCode?: string;
  vehicleCode?: string;
  packageId?: string;
  packageType?: number;
  vcExtendRequest?: VcExtendRequestType;
  decoratorVendorType?: number;
  easyLifeUpgradePackageId?: number;
  isEasyLife?: boolean;
  withPrice?: boolean;
  packageId4CutPrice?: number;
  payMode?: number;
  bomCode?: string;
  productCode?: string;
  rateCode?: string;
  subType?: number;
  comPriceCode?: string;
  priceVersion?: string;
  priceVersionOfLowestPrice?: string;
  pCityId?: number;
  rCityId?: number;
  vendorVehicleCode?: string;
  age?: number;
  alipay?: boolean;
  aType?: number;
  vendorSupportZhima?: boolean;
  hotType?: number;
  priceType?: number;
  vehicleDegree?: string;
  idType?: number;
  fType?: number;
  rentCenterId?: number;
  isSelect?: boolean;
  unionCardFilter?: UnionCardFilterType;
  noDepositFilter?: UnionCardFilterType;
  labels?: Array<LabelsType>;
  pStoreNav?: string;
  rStoreNav?: string;
  pickUpOnDoor?: boolean;
  dropOffOnDoor?: boolean;
  pickWayInfo?: number;
  returnWayInfo?: number;
  sendTypeForPickUpCar?: number;
  sendTypeForPickOffCar?: number;
  hot?: number;
  freeIllegalDeposit?: boolean;
  creditFreeCarDeposit?: boolean;
  isdShareInfo?: IsdShareInfoType;
  adjustVersion?: string;
  gsId?: number;
  noLp?: number;
  gsDesc?: string;
  pRc?: number;
  rRc?: number;
  rCoup?: number;
  promtId?: number;
  klbVersion?: number;
  platform?: PlatformCode;
}

export interface CommentInfoType2 {
  overallRating?: number;
  commentCount?: number;
  level?: string;
  newVendorDescDescription?: string;
}

export interface EasyLifeVsNormalInfoListType {
  title?: string;
  easyLifeDesc?: string;
  normalDesc?: string;
}

export interface EasyLifeInsuranceIncludesType {
  itemName?: string;
  coverage?: string;
}

export interface ItemListType {
  sortNum?: number;
  name?: string;
}

export interface ItemListType2 {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType>;
}

export interface ExtDescriptionListType {
  sortNum?: number;
  name?: string;
  itemList?: Array<ItemListType2>;
}

export interface EasyLifeInsuranceDetailType {
  bizVendorCode?: string;
  easyLifeInsuranceDesc?: Array<string>;
  easyLifeInsuranceAdditionalDesc?: Array<string>;
  easyLifeInsuranceUncludes?: Array<string>;
  easyLifeInsuranceIncludes?: Array<EasyLifeInsuranceIncludesType>;
  vendorName?: string;
  extDescriptionList?: Array<ExtDescriptionListType>;
  clauseUrl?: string;
}

export interface EasyLifeCtripInsuranceConfigType {
  withCtripInsurance?: boolean;
  productIds?: Array<number>;
}

export interface EasyLifeInfoType {
  isEasyLife?: boolean;
  pickupTips?: string;
  returnTips?: string;
  pickupLongTips?: string;
  returnLongTips?: string;
  tagList?: Array<LabelsType>;
  commentInfo?: CommentInfoType2;
  easyLifeVsNormalInfoList?: Array<EasyLifeVsNormalInfoListType>;
  easyLifeInsuranceDetail?: EasyLifeInsuranceDetailType;
  countryId?: number;
  cityList?: Array<number>;
  bizVendorCode?: string;
  freePreAuthorization?: boolean;
  originFreePreAuth?: boolean;
  needWechat?: boolean;
  easyLifeCtripInsuranceConfig?: EasyLifeCtripInsuranceConfigType;
  onewayfee?: number;
  freePreAuthDesc?: string;
  guidImages?: string;
}

export interface FilterAggregationsType {
  name?: string;
  groupCode?: string;
  binaryDigit?: number;
  checkType?: number;
}

export interface AdjustPriceInfoType {
  bAdjustDailyPrice?: number;
  bAdjustTotalPrice?: number;
  adjustStrategy?: string;
  cashbackStrategy?: string;
}

export interface DetailType {
  code?: string;
  name?: string;
  amount?: number;
  amountDesc?: string;
  desc?: string;
  showFree?: boolean;
}

export interface FeesType {
  code?: string;
  name?: string;
  amount?: number;
  amountStr?: string;
  subAmount?: number;
  subAmountStr?: string;
  qt?: number;
  desc?: string;
  detail?: Array<DetailType>;
  currencyCode?: string;
}

export interface PriceDailysType {
  date?: string;
  oDprice?: string;
  priceStr?: string;
  showType?: number;
}

export interface VendorPriceListType {
  pStoreSortDesc?: string;
  rStoreSortDesc?: string;
  vendorName?: string;
  isMinTPriceVendor?: boolean;
  vendorLogo?: string;
  commentInfo?: CommentInfoType;
  priceInfo?: PriceInfoType;
  reference?: ReferenceType;
  referenceVersion?: string;
  promotions?: Array<PromotionType>;
  isSpecialized?: boolean;
  sortNum?: number;
  newVendorDesc?: string;
  vendorTag?: LabelsType;
  pStoreRouteDesc?: string;
  rStoreRouteDesc?: string;
  easyLifeInfo?: EasyLifeInfoType;
  showIZuCheLogo?: boolean;
  isBroker?: boolean;
  platformName?: string;
  platformCode?: string;
  allTags?: Array<LabelsType>;
  evaluation?: LabelsType;
  filterAggregations?: Array<FilterAggregationsType>;
  extTitle?: string;
  reactId?: string;
  isPStoreSupportCdl?: boolean;
  isRStoreSupportCdl?: boolean;
  privilegesPromotions?: Array<PromotionType>;
  qualityScore?: number;
  sortScore?: number;
  storeScore?: number;
  isSelect?: boolean;
  orignalPriceStyle?: any;
  stock?: number;
  stockDesc?: string;
  distance?: number;
  rDistance?: number;
  adverts?: number;
  extMap?: ExtMapType;
  payModes?: Array<number>;
  freeDeposit?: number;
  urge?: string;
  actId?: string;
  couId?: string;
  joinerType?: string;
  joinerStoreId?: string;
  adjustPriceInfo?: AdjustPriceInfoType;
  pickUpFee?: number;
  pickOffFee?: number;
  showVendor?: boolean;
  isOrderVehicle?: boolean;
  cyVendorName?: string;
  card?: number;
  ctripVehicleCode?: string;
  cashbackPre?: string;
  modifySameStore?: boolean;
  fees?: Array<FeesType>;
  dPriceDesc?: string;
  priceDailys?: Array<PriceDailysType>;
  uniqueCode?: string;
  decorateVehicleName?: string;
  vehicleScopeDesc?: string;
  newCar?: boolean;
  vehicleGroup?: number;
}

export interface SpecificProductGroupsType {
  title?: string;
  vendorPriceList?: Array<VendorPriceListType>;
}

export interface IncludeFeesType {
  tFees?: Array<string>;
  dFees?: Array<string>;
  desc?: string;
}

export interface FeeMapType {
  code?: string;
  name?: string;
  desc?: string;
}
export interface RecommendVehicle {
  /**
   * 车型code
   */
  vehicleCode?: string | null;
  /**
   * 可用搜索地址（r2:按同城异地推荐时使用）
   */
  availableLocation?: string | null;
  /**
   * 经度
   */
  longitude?: string | null;
  /**
   * 维度
   */
  latitude?: string | null;
}

export interface Recommendation {
  /**
   * 提示语
   */
  tilte?: string | null;
  /**
   * 提示副标题
   */
  subTitle?: string | null;
  /**
   * 推荐标题
   */
  recTitle?: string | null;
  /**
   * 推荐信息
   */
  recMessage?: string | null;
  /**
   * 可用还车时间
   */
  pickUpAvailableTime?: string | null;
  /**
   * 可用取车时间
   */
  returnAvailableTime?: string | null;
}

export interface RecommendInfo {
  /**
   * 推荐类型（r1：按时间推荐，r2:按同城异地推荐，r3:扩大搜索半径推荐，r4：推荐附近城市）
   */
  recommendType?: string | null;
  /**
   * 推荐车型信息
   */
  recommendVehicles?: RecommendVehicle[] | null;
  /**
   * 推荐语
   */
  recommendation?: Recommendation | null;
  /**
   * 原因
   */
  reason?: string | null;
  /**
   * 推荐语
   */
  recommend?: string | null;
}

export interface QueryVehicleDetailListResponseType {
  baseResponse?: BaseResponseType;
  ResponseStatus?: ResponseStatusType;
  vehicleInfo?: VehicleInfoType;
  specificProductGroups?: SpecificProductGroupsType;
  filteredProductGroups?: SpecificProductGroupsType;
  includeFees?: IncludeFeesType;
  uniqSign?: string;
  feeMap?: Array<FeeMapType>;
  marketingAtmosphere?: number;
  appResponseMap?: ResponseMapType;
  isFromSearch?: boolean;
  promptInfos?: Array<PromptInfoType>;
  promotMap?: { [key: number]: string };
  extras?: any;
  imStatus?: number;
  recommendInfo?: RecommendInfo;
  recommendProducts?: ProductListType[];
  recommendVehicleList?: VehicleInfoType[];
  productGroupCodeUesd?: string;
}
export interface PromptInfoType {
  title?: string;
  subTitle?: string;
  contents?: Array<ComplexSubTitleType>;
  type?: number;
  icon?: string;
  note?: string;
  locations?: Array<LocationsType>;
  button?: ButtonType;
  buttonExt?: Array<ButtonType>;
  filterItem?: FilterItemType;
  items?: Array<ItemsType2>;
  table?: Array<ItemsType2>;
  extraInfos?: ExtraInfosType;
}

export interface ExtraInfosType {
  url?: string;
  desc?: string;
  amount?: string;
}

export interface ComplexSubTitleType {
  contentStyle?: string;
  stringObjs?: Array<StringObjsType>;
}

export interface LocationsType {
  groupCode?: string;
  index?: number;
}

export interface ButtonType {
  title?: string;
  type?: number;
  desc?: string;
  tips?: string;
  icon?: string;
}

export interface FilterItemType {
  itemCode?: string;
  name?: string;
  code?: string;
  groupCode?: string;
  bitwiseType?: number;
  binaryDigit?: number;
  sortNum?: number;
  isQuickItem?: boolean;
  quickSortNum?: number;
  icon?: string;
  promotion?: PromotionType;
  mark?: string;
  positionCode?: string;
}

export interface ItemsType2 {
  title?: ComplexSubTitleType;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  items?: Array<ItemsType>;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}

export interface StringObjsType {
  content?: string;
  style?: string;
  url?: string;
}

export interface ItemsType {
  title?: string;
  subTitle?: string;
  description?: string;
  complexSubTitle?: ComplexSubTitleType;
  contents?: Array<ComplexSubTitleType>;
  code?: string;
  type?: number;
  size?: string;
  include?: boolean;
  currencyCode?: string;
  currenctDailyPrice?: number;
  currentTotalPrice?: number;
  localCurrencyCode?: string;
  localDailyPrice?: number;
  localTotalPrice?: number;
  showFree?: boolean;
  retractable?: boolean;
  showPrice?: string;
  payMode?: number;
  sortNum?: number;
  notices?: Array<string>;
  labels?: Array<LabelsType>;
  positiveStatus?: boolean;
  isInstantConfirm?: boolean;
  tableTitle?: string;
  freezeDeposit?: boolean;
  showCreditCard?: boolean;
  activityCode?: string;
  discountType?: number;
}
