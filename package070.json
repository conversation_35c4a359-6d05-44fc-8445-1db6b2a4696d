{"name": "rn_car_home_isd", "version": "0.0.18", "appVersion": "8.15", "private": true, "scripts": {"cw": "cw pack", "patch": "crn-cli run-patch", "start": "crn-cli start --port 5389 --reset && npm run ts:watch", "android:c": "crn-cli run-android --app-version 8.33 --port 5389", "ios:c": "crn-cli run-ios --app-version 8.62.0 --port 5388 --simulator 'iPhone 14 Pro' --reset && npm run ts:watch", "android:t": "crn-cli run-android --appid 37 --app-version 7.5.0 --simulator 'iPhone 8 Plus' --port 5388", "ios:t": "crn-cli run-ios --appid 37 --app-version 7.7.0 --simulator 'iPhone X' --reset --port 5388 && npm run ts:watch", "start:trip": "npm run start:t && npm run ts:watch", "android": "npm run android:c && npm run ts:watch", "ios": "npm run ios:c && npm run ts:watch", "android:trip": "npm run android:t && npm run ts:watch", "ios:trip": "npm run ios:t && npm run ts:watch", "lint": "npm run lint:js", "lint:js": "eslint . --ignore-path .eslint<PERSON>ore --ext .js,.ts,.tsx", "lint:fix": "npm run lint:js:fix", "lint:js:staged": "eslint . --ignore-path .eslint<PERSON>ore", "lint:js:fix": "npm run lint:js -- --fix", "lint:js:fix:staged": "eslint --ignore-path .eslint<PERSON>ore --fix", "ts:build": "tsc && tsc-alias && npm run lazyRequire", "ts:watch": "tsc && (concurrently \"tsc -w\" \"tsc-alias -w\")", "build:version": "sh ./scripts/BuildVersion.sh", "precommit": "lint-staged", "test": "jest --coverage --runInBand --forceExit", "test:ui": "jest --config jest.config.ui.js --json --outputFile='./coverage/case-result.json'", "test:ut": "jest --coverage --config jest.config.ut.js --detectOpenHandles  --forceExit", "test:new": "jest --coverage --changedSince=master-064 --coverageThreshold='{\"global\":{\"branches\":\"50\"}}' --detectOpenHandles  --forceExit", "test:watch": "jest --watch --debug --verbose --coverage=false", "test:fix": "npm run test -- -u", "clean": "watchman watch-del-all && rm -rf package-lock.json && rm -rf node_modules && rm -rf $TMPDIR/metro-* && rm -rf $TMPDIR/haste-map-* && npm run install:ctrip", "clean:common": "cd src/Common && rm -rf dist && npm run postinstall && tsc", "product:ts": "rm -rf node_modules && rm -rf  package-lock.json && npm install --production", "install:ctrip": "npm install --registry=--registry=http://artifactory.release.ctripcorp.com/artifactory/api/npm/trip-npm-prod/ --legacy-peer-deps", "tsc": "tsc", "install": "tsc || true", "release-version": "standard-version", "release-push": "git push --follow-tags --no-verify --atomic origin master", "qc": "node ./qconfig sf=C_APP", "qc:c": "node ./qconfig sf=C_APP", "qc:t": "node ./qconfig sf=IBU_APP", "dev": "react-devtools", "shark:es6to5": "./node_modules/.bin/babel ./build/src/Common/src/SharkKeys --out-dir tmp --presets=es2015,stage-2 --plugins=babel-plugin-transform-runtime", "shark:detect": "node --harmony ./scripts/sharkDoctor.js", "shark:format": "node ./scripts/sharkFormat.js", "shark:doctor": "npm run shark:es6to5 && npm run shark:detect && npm run shark:format", "postinstall": "npm run lazyRequire && node ./scripts/postinstall.js && npm run commonScript && tsc-alias", "lazyRequire": "node --harmony ./scripts/lazyRequireReplace", "removeAB": "node ./scripts/removeAB.js", "removeUL": "node ./scripts/removeUseLess.js", "commonScript": "node ./src/Common/scripts/postinstall.js && node ./src/Common/scripts/lazyRequireReplace", "common:watch": "watchman watch-del-all && wml add ./src/Common/dist ./node_modules/@ctrip/rn_com_car/dist && wml start", "transRnComCar": "node ./scripts/transRnComCarPath.js", "i18nReplace": "node ./scripts/i18n-replace.js", "publishCommon": "echo 'deleted'", "copyMdFile": "node ./scripts/copyMdFile.js", "removeSS": "node ./scripts/removeStyleSheet.js"}, "dependencies": {"@babel/cli": "7.16.0", "@babel/core": "7.16.5", "@babel/preset-env": "7.16.5", "@babel/register": "7.16.5", "@babel/runtime": "7.16.5", "@ctrip/crn": "git+http://git.dev.sh.ctripcorp.com/crn/crn.git#rel/8.60.0", "@ctrip/crn-ext-adsdk": "3.5.12", "@ctrip/devhub-trace-log": "^0.2.3", "@ctrip/ttd-marketing-rn": "3.0.4", "@testing-library/jest-native": "^5.0.0", "@testing-library/react-native": "^11.2.0", "@types/jest": "^24.9.1", "@types/node": "13.13.12", "@types/node-fetch": "^2.6.2", "@types/promise-timeout": "^1.3.0", "@types/react": "16.9.36", "@types/react-native": "^0.60.31", "@types/react-test-renderer": "^16.9.2", "@types/uuid": "^3.4.9", "@types/yallist": "^3.0.1", "babel-cli": "6.26.0", "babel-plugin-transform-runtime": "6.23.0", "babel-preset-es2015": "6.24.1", "babel-preset-react-native": "5.0.2", "babel-preset-stage-2": "6.24.1", "tsc-alias": "^1.7.1", "theming": "3.3.0", "lodash": "4.17.21", "memoize-one": "^5.1.1", "metro-react-native-babel-preset": "0.64.0", "node-fetch": "^2.6.9", "promise-timeout": "^1.3.0", "qs": "6.9.6", "react": "18.1.0", "react-native": "0.70.1", "react-native-animatable": "1.3.3", "react-native-typescript-transformer": "^1.2.13", "typescript": "4.1.6", "uuid": "^3.4.0", "fs-extra": "^11.2.0", "yallist": "^4.0.0", "@ctrip/rr-react-native": "5.1.4", "zustand": "4.4.4"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "concurrently": "^7.5.0", "@commitlint/config-conventional": "^8.3.4", "@ctrip/eslint-plugin-car-linter": "1.1.0", "@testing-library/react-hooks": "^8.0.1", "@welldone-software/why-did-you-render": "^6.0.3", "commitizen": "^4.1.2", "cz-conventional-changelog": "^3.2.0", "esbuild": "^0.18.16", "esbuild-jest": "^0.5.0", "gh-pages": "^3.2.3", "husky": "^4.2.5", "jest": "27.5.1", "jest-junit": "^7.0.0", "jest-react-native": "^18.0.0", "lerna": "^3.10.7", "lint-staged": "^8.1.0", "madge": "^5.0.1", "randomstring": "^1.1.5", "standard-version": "^8.0.0", "ts-jest": "^24.3.0", "webpack-bundle-analyzer": "^3.8.0"}, "lint-staged": {"src/**/*.{js,ts,tsx}": ["npm run lint:js:fix:staged", "npx prettier --write"]}, "husky": {"hooks": {"pre-commit": "npm run build:version && git add src/BuildTime.ts && lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "iconFont": ["crn_font_car_app_v1"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}, "cwVersion": "6.1.1", "cwBase": "/webapp/cw/rn_car_app", "cwMain": "/index.html?type=h5"}}